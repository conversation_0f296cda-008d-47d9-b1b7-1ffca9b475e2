import {
  store,
  util,
  Event,
  Emitter,
  global,
  storage,
  breakIn,
  eventName,
  enums,
} from "./common/index.js";

import {
  getWebviewMoment,
  getAppHoldStatus,
  setAppHoldStatus,
  setSpringboardStatus,
  setLoadingMoment,
  getSystemInfo,
  setSFV,
  getTabBarList
} from "./common/utils/userStorage";

import config from './config/index.js'
const { env } = config

const {
  isEmptyObject,
} = util;

const {
  MARKET_CLUE_DEF
} = enums

const {
  SET_PAGE_BACK,
} = eventName

// const versionType = {
//   develop: 'develop', // 开发版
//   trial: 'trial', // 体验版
//   release: 'release' // 正式版
// }

const OPEN_FILE_PATH = 'pages/common/openFile/openFile'
const ACTIVITY_PATH = 'pages/loginAndRegist/activity/activity'

const event = new Event();
const formIds = [];

const log = wx.getRealtimeLogManager ? wx.getRealtimeLogManager() : null

/**
 * 1179
 * short_link 打开小程序
 */
const APP_START_BY_SHORT_LINK = [
  '1179'
]
var sensors = require('./sensorsdata/sensorsdata.min.js');
App({
  event,
  formIds,
  store,
  onLaunch: function () {
    // wx.cloud.init({
    //   env: 'guangfa-cloud-8gk1jm0dee11cab2',
    // })
    breakIn({ name: 'initUnionId' })
    let _sInfo = getSystemInfo()
    log && log?.info({
      page: 'APP',
      lifetimes: 'onLaunch',
      target: '_sInfo',
      hasData: !isEmptyObject(_sInfo),
    })
    if (_sInfo && isEmptyObject(_sInfo)) {
      breakIn({ name: 'initSys' })
    }
    this.sensorsInit()
  },

  sensorsInit() {
    sensors.init({
      // 神策分析注册在 APP 全局函数中的变量名，在非 app.js 中可以通过 getApp().sensors(你这里定义的名字来使用)
      name: 'sensors',
      // 如果要通过 sdk 自动获取 openid，需要在神策分析中配置 appid 和 appsercret，并在这里标志 appid,不需要的话，不用填。
      appid: env.appId,
      // 神策分析数据接收地址
      server_url: env.sensorsdataUrl,
      //请求发送超时时间
      send_timeout: 1000,
      // 传入的字符串最大长度限制，防止未知字符串超长
      max_string_length: 300,
      // 发送事件的时间使用客户端时间还是服务端时间
      use_client_time: false,
      // 是否允许控制台打印查看埋点数据（建议开启查看）
      show_log: true,
      // 是否允许修改 onShareMessage 里 return 的 path，用来增加（用户 id，分享层级，当前的 path），在 app onshow中自动获取这些参数来查看具体分享来源，层级等
      allow_amend_share_path: true,
      // 是否自动采集如下事件（建议开启）
      autoTrack: {
        mpClick: true,
        appLaunch: true, //是否采集 $MPLaunch 事件，true 代表开启。
        appShow: true, //是否采集 $MPShow 事件，true 代表开启。
        appHide: true, //是否采集 $MPHide 事件，true 代表开启。
        pageShow: true, //是否采集 $MPViewScreen 事件，true 代表开启。
        pageShare: true, //是否采集 $MPShare 事件，true 代表开启。
        pageLeave: true, //离开页面
        addFavorites: true, //收藏
      }
    })
  },

  getCurrentPageId(pageName = '') {
    const list = storage.getStorage(global.STORAGE_GLOBAL_TAB_BAR_LIST)
    let pageId = ''
    if (list && list.length) {
      list.forEach((item) => {
        const { pageTabBarConf: { tabName = '' }, id } = item || {}
        if (pageName == tabName) {
          pageId = id
        }
      })
    }
    return pageId
  },

  onShow: (options) => {
    log && log?.info({
      page: 'APP',
      lifetimes: 'onShow',
      target: 'options',
      res: options,
    })
    const { path = '', query = {}, scene = 0 } = options || {}
    storage.setStorage(global.STORAGE_GLOBAL_SCREEN_CODE, scene + '')
    // 阅览||打开文件
    if (OPEN_FILE_PATH == path) {
      storage.setStorage(global.STORAGE_GLOBAL_OPEN_FILE_START, 1)
      return getApp().event.emit(SET_PAGE_BACK)
    }

    // 活动|shortLink 打开
    if (ACTIVITY_PATH == path || APP_START_BY_SHORT_LINK.includes(scene + '')) {
      storage.setStorage(global.STORAGE_GLOBAL_INLINE_PAGE, true)
    }

    // 保存携带参数的scene值
    setSFV(global.STORAGE_GLOBAL_SCREEN_QUERY, query?.scene || '')

    if (getWebviewMoment()) {
      return
    }

    if (getAppHoldStatus()) {
      setAppHoldStatus(false)
      return
    }

    return true
  },

  onHide() {
    log && log?.info({
      page: 'APP',
      lifetimes: 'onHide',
    })
    setAppHoldStatus(true)
  },

  onUnlaunch() {
    let that = this
    that.globalData.customerType = ''
    setSpringboardStatus(false)
    setLoadingMoment(false)
    log && log?.info({
      page: 'APP',
      lifetimes: 'onUnlaunch',
    })
  },

  globalData: {
    entId: 8821,
    cardList: [], // 卡片列表
    tabList: [], // tabBar列表
    shortLink: '', // shortLink跳转其他小程序
    switchRole: '', // 切换身份
    refersMark: '', // 切换身份访问
    routerHome: false, // 跳转站点首页
    customerType: '', // 当前站点类型
    filePath: '', // 文件路径
    isMarkDayStatus: '', // 静默状态
    token: '',
    _fmOpt: {
      partnerCode: "gffunds", // 合作方标识，同盾分配
      appName: "gffund_xcx", // 应用标识，同盾分配
      env: "PRODUCTION"
    },
    submitWay: '',
    emitter: new Emitter()
  },


  /**
   * 分享线索参数
   */
  getCluesInfo(params = {}) {
    let that = this
    const tabList = (that.globalData.tabList)?.length ? that.globalData.tabList : getTabBarList() || []
    const {
      fromTab = '',
      cId = '',
      cName = '',
      mName = ''
    } = params || {}

    let _pageName = ''
    let _pageId = ''
    let _clues = [].concat(MARKET_CLUE_DEF) || []
    let _clueRes = []
    tabList.forEach((item) => {
      const { id = '', pageName = '', pageTabBarConf = {} } = item || {}
      const { tabName = '' } = pageTabBarConf || {}

      if (fromTab == tabName) {
        _pageName = pageName
        _pageId = id
      }
    })

    _clues.forEach((cItem) => {
      const { type = '' } = cItem || {}
      if (type == 'page') {
        cItem.name = _pageName
        cItem.id = _pageId
      }

      if (type == 'card') {
        cItem.name = cName
        cItem.id = cId
      }

      if (type == 'module') {
        cItem.name = mName
      }

      _clueRes.push(cItem)
    })

    return _clueRes || _clues
  }
});

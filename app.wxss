@import "icon.wxss";
@import "colorui.wxss";
@import "nb-icon.wxss";
/**app.wxss**/
page {
  background: #f8f8f8;
  font-size: 14px;
  color: #333;
  /* line-height: 1; */
  font-family: Helvetica Neue, Helvetica, sans-serif;
}
/* .van-cell__title{
  max-width: inherit !important;
} */
/* 去除Button默认样式 */
button::after {
  border: none;
}
button {
  border-radius: 0;
}
button {
  border: 0;
}

/* 去除cover-view样式 */
cover-view {
  visibility: visible;
}

/* ==================
        van cell
 ==================== */
.van-cell {
  /*padding: 24rpx 32rpx !important;*/
  font-size: 26rpx !important;
  color: #969696 !important;
}

.van-cell__value {
  color: #969696 !important;
}

/* ==================
          文本
 ==================== */

.text-xs {
  font-size: 20rpx;
}

.text-sm {
  font-size: 24rpx;
}

.text-df {
  font-size: 28rpx;
}

.text-lg {
  font-size: 32rpx;
}

.text-xl {
  font-size: 36rpx;
}

.text-xxl {
  font-size: 40rpx;
}

.text-sl {
  font-size: 80rpx;
}

.text-xsl {
  font-size: 120rpx;
}

.text-Abc {
  text-transform: Capitalize;
}

.text-ABC {
  text-transform: Uppercase;
}

.text-abc {
  text-transform: Lowercase;
}

.text-price::before {
  content: "¥";
  font-size: 80%;
  margin-right: 4rpx;
}

.text-cut {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.text-break {
  word-break: break-all;
}

.text-bold-sm {
  font-weight: 500;
}

.text-bold-df {
  font-weight: bold;
}

.text-center {
  text-align: center;
}

.text-content {
  line-height: 1.6;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-red,
.line-red,
.lines-red {
  color: #e54d42 !important;
}

.text-orange,
.line-orange,
.lines-orange {
  color: #f37b1d !important;
}

.text-yellow,
.line-yellow,
.lines-yellow {
  color: #fbbd08 !important;
}

.text-olive,
.line-olive,
.lines-olive {
  color: #8dc63f !important;
}

.text-green,
.line-green,
.lines-green {
  color: #39b54a !important;
}

.text-cyan,
.line-cyan,
.lines-cyan {
  color: #1cbbb4 !important;
}

.text-blue,
.line-blue,
.lines-blue {
  color: #0081ff !important;
}

.text-purple,
.line-purple,
.lines-purple {
  color: #6739b6 !important;
}

.text-mauve,
.line-mauve,
.lines-mauve {
  color: #9c26b0 !important;
}

.text-pink,
.line-pink,
.lines-pink {
  color: #e03997 !important;
}

.text-brown,
.line-brown,
.lines-brown {
  color: #a5673f !important;
}

.text-gray,
.line-gray,
.lines-gray {
  color: #969696 !important;
}

.text-black,
.line-black,
.lines-black {
  color: #333 !important;
}

.text-white,
.line-white,
.lines-white {
  color: #fff !important;
}

/* ==================
         卡片
 ==================== */
.nb-card {
  display: block;
  overflow: hidden;
  position: relative;
}

.nb-card > .nb-item {
  display: block;
  /*background: #fff;*/
  /*border-radius: 12rpx;*/
  overflow: hidden;
  margin-left: 28rpx;
  margin-right: 28rpx;
  margin-bottom: 40rpx;
}

.nb-card > .nb-item.shadow-blur {
  overflow: initial;
}

.nb-card.no-card > .nb-item {
  margin: 0rpx;
  border-radius: 0rpx;
}

.nb-card.case .image {
  position: relative;
}

.nb-card.case .image image {
  width: 100%;
}

.nb-card.case .image .nb-tag {
  position: absolute;
  right: 0;
  top: 0;
}

.nb-card.case .image .nb-bar {
  position: absolute;
  bottom: 0;
  width: 100%;
  background-color: transparent;
  padding: 0rpx 28rpx;
  word-wrap: normal;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.nb-card.case.no-card .image {
  margin: 0;
  overflow: hidden;
  border-radius: 8rpx;
}

.nb-card .tag {
  position: absolute;
  top: 30rpx;
  right: 28rpx;
  background-color: #F24D28;
  padding: 4rpx 16rpx;
  color: #fff;
  font-size: 22rpx;
  border-radius: 4px 0px 0px 4px;
  text-align: center;
  line-height: 32rpx;
  font-family: PingFangSC-Regular;
}

/* ==================
          头像
 ==================== */

.nb-avatar {
  font-variant: small-caps;
  margin: 0;
  padding: 0;
  display: inline-block;
  text-align: center;
  background: #ccc;
  color: #fff;
  white-space: nowrap;
  position: relative;
  width: 64rpx;
  height: 64rpx;
  line-height: 64rpx;
  background-size: cover;
  background-position: center;
  vertical-align: middle;
  font-size: 0rpx;
}

/* avatar */
.nb-avatar::first-letter,
.nb-avatar text::first-letter {
  font-size: 40rpx;
}

.nb-avatar text {
  position: absolute;
  left: 50%;
  display: inline-block;
  transform-origin: 0 center;
  transform: scale(1) translateX(-50%);
  font-size: inherit;
}

.nb-avatar.sm {
  width: 48rpx;
  height: 48rpx;
  line-height: 48rpx;
}

.nb-avatar.sm::first-letter,
.nb-avatar.sm text::first-letter {
  font-size: 30rpx;
}

.nb-avatar > text[class*="icon"] {
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  margin: auto;
  transform-origin: 0 center;
  transform: scale(1.2) translateX(-50%);
}

.nb-avatar.sm > text[class*="icon"] {
  transform: scale(0.75) translateX(-50%);
}

.nb-avatar.lg > text[class*="icon"] {
  transform: scale(1.75) translateX(-50%);
}

.nb-avatar.xl > text[class*="icon"] {
  transform: scale(2.2) translateX(-50%);
}

.nb-avatar.lg {
  width: 90rpx;
  height: 90rpx;
  line-height: 90rpx;
  font-size: 22rpx;
}

.nb-avatar.lg::first-letter,
.nb-avatar.lg text::first-letter {
  font-size: 36rpx;
}

.nb-avatar.xl {
  width: 120rpx;
  height: 120rpx;
  line-height: 120rpx;
  font-size: 24rpx;
}

.nb-avatar.xl::first-letter,
.nb-avatar.xl text::first-letter {
  font-size: 40rpx;
}

.nb-avatar-group {
  direction: rtl;
  unicode-bidi: bidi-override;
  padding: 0 10rpx 0 40rpx;
  display: inline-block;
}

.nb-avatar-group .nb-avatar {
  margin-left: -30rpx;
  border: 4rpx solid #f1f1f1;
  vertical-align: middle;
}

.nb-avatar-group .nb-avatar.sm {
  margin-left: -20rpx;
  border: 1rpx solid #f1f1f1;
}

/* flex横向布局 */
.flex-row {
  display: flex;
  flex-direction: row;
}
/* flex纵向布局 */
.flex-column {
  display: flex;
  flex-direction: column;
}
.flex1 {
  flex: 1;
}
/* 显示一行，超出部分显示... */
.text-single-line {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  /* display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden; */
}
.text-two-line {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

/* ==================
          按钮
 ==================== */

.round,
button.icon {
  border-radius: 50% !important;
}

.radius {
  border-radius: 6rpx !important;
}
.border-radius-50{
  border-radius: 50%;
}

.noLogin{
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.text{
  color: #555555;
  font-size: 28rpx;
  border-bottom: 1px solid #555555;
}

.nb-btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  padding-top: 16rpx;
  padding-bottom: 16rpx;
  font-size: 32rpx;
  line-height: 40rpx;
  text-align: center;
  text-decoration: none;
  border-radius: 8rpx;
  overflow: visible;
  color: #fff;
  background-color: #fff;
  margin-left: initial;
  transform: translate(0rpx, 0rpx);
  margin-right: initial;
}

.nb-tag[class*="line-"]::after {
  content: " ";
  width: 200%;
  height: 200%;
  position: absolute;
  top: 0;
  left: 0;
  border: 1rpx solid rgba(0, 0, 0, 0.2);
  transform: scale(0.5);
  transform-origin: 0 0;
  box-sizing: border-box;
  border-radius: inherit;
  z-index: 1;
  pointer-events: none;
}

.nb-btn[class*="line"]::after,
.nb-tag.radius[class*="line"]::after {
  border-radius: 12rpx;
}

.nb-btn.round[class*="line"]::after,
.nb-tag.round[class*="line"]::after {
  border-radius: 1000rpx;
}

.nb-btn[class*="lines"]::after {
  border: 6rpx solid rgba(0, 0, 0, 0.2);
}

.nb-btn[class*="bg-"]::after {
  display: none;
}

.nb-btn.sm {
  padding: 14rpx 14rpx;
  font-size: 24rpx;
}

.nb-btn.lg {
  padding: 32rpx 28rpx;
  font-size: 32rpx;
}

.nb-btn.icon.sm {
  width: 56rpx;
  height: 56rpx;
}

.nb-btn.icon {
  width: 70rpx;
  height: 70rpx;
  padding: 0;
}

button.icon.lg {
  width: 80rpx;
  height: 80rpx;
}

.nb-btn.shadow-blur::before {
  top: 4rpx;
  left: 4rpx;
  filter: blur(6rpx);
  opacity: 0.6;
}

.nb-btn.button-hover {
  transform: translate(1rpx, 1rpx);
}

.block {
  display: block;
}

.nb-btn.block {
  display: flex;
}

.nb-btn[disabled] {
  /* opacity: 0.6; */
  background-color: #cccccc !important;
  color: #fff !important;
}
.submit-btn{
  position: fixed;
  bottom: 0;
  /* padding-left: 28rpx;
  padding-right: 28rpx; */
  width: 100%;
}
.padding-bottom120{
  padding-bottom: 120rpx;
}

/* ==================
         模态窗口
 ==================== */

.nb-modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1110;
  opacity: 0;
  outline: 0;
  text-align: center;
  -ms-transform: scale(1.185);
  transform: scale(1.185);
  backface-visibility: hidden;
  perspective: 2000rpx;
  background: rgba(0, 0, 0, 0.6);
  transition: all 0.3s ease-in-out 0s;
  pointer-events: none;
}

.nb-modal::before {
  content: "\200B";
  display: inline-block;
  height: 100%;
  vertical-align: middle;
}

.nb-modal.show {
  opacity: 1;
  transition-duration: 0.3s;
  -ms-transform: scale(1);
  transform: scale(1);
  overflow-x: hidden;
  overflow-y: auto;
  pointer-events: auto;
}

.nb-dialog {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  margin-left: auto;
  margin-right: auto;
  width: 622rpx;
  max-width: 100%;
  background: #f8f8f8;
  border-radius: 8rpx;
  overflow: hidden;
}

.nb-modal.bottom-modal::before {
  vertical-align: bottom;
}

.nb-modal.bottom-modal .nb-dialog {
  width: 100%;
  border-radius: 0;
}

.nb-modal.bottom-modal {
  margin-bottom: -1000rpx;
}

.nb-modal.bottom-modal.show {
  margin-bottom: 0;
}

.nb-modal.drawer-modal {
  transform: scale(1);
  display: flex;
}

.nb-modal.drawer-modal .nb-dialog {
  height: 100%;
  min-width: 200rpx;
  border-radius: 0;
  margin: initial;
  transition-duration: 0.3s;
}

.nb-modal.drawer-modal.justify-start .nb-dialog {
  transform: translateX(-100%);
}

.nb-modal.drawer-modal.justify-end .nb-dialog {
  transform: translateX(100%);
}

.nb-modal.drawer-modal.show .nb-dialog {
  transform: translateX(0%);
}

.arrow::before {
  font-family: "iconfont" !important;
  display: block;
  content: "\e6a3";
  position: absolute;
  font-size: 34rpx;
  color: #aaa;
  line-height: 30rpx;
  height: 30rpx;
  width: 30rpx;
  text-align: center;
  top: 0rpx;
  bottom: 0;
  right: 30rpx;
  margin: auto;
}

.arrow-auto::before {
  font-family: "iconfont" !important;
  display: block;
  content: "\e6a3";
  position: absolute;
  font-size: 34rpx;
  color: #aaa;
  line-height: 30rpx;
  height: 30rpx;
  width: 30rpx;
  text-align: center;
  top: 0rpx;
  bottom: 0;
  right: 0;
  margin: auto;
}



.position-relative{
  position: relative;
}

.home-card-bgc {
  background-color: #e5d2a1;
}
.background-fff {
  background-color: #fff;
}
.background-a8a8a8 {
  background-color: #a8a8a8;
}

.w10{
  width: 10%;
}
.w20{
  width: 20%;
}
.w25{
  width: 25%;
}
.w45{
  width: 45%;
}
.w25{
  width: 25%;
}
.w30{
  width: 30%;
}
.w40{
  width: 40%;
}
.w90{
  width: 90%;
}
.w100 {
  width: 100%;
}
.h100{
  height: 100%;
}
.w120rpx{
  width: 120rpx;
}
.w60rpx{
  width: 60rpx;
}
.h60rpx{
  height: 60rpx;
}
.w150px{
  width: 150px;
}
.h120rpx{
  height: 120rpx;
}
.h150px{
  height: 150px;
}
.lh2-7{
  line-height: 2.7;
}
.lh1{
  line-height: 1;
}
.w50{
  width: 50%;
}
.w70{
  width: 70%;
}
.w60{
  width: 60%;
}
.border-top {
  border-top: 1rpx solid #fff;
}
.border-bottom-eee {
  border-bottom: 1rpx solid #eee;
}

.margin-top18 {
  margin-top: 18rpx;
}
.margin-left18 {
  margin-left: 18rpx;
}
.margin-left8{
  margin-left: 8rpx;
}
.margin-right18{
  margin-right: 18rpx;
}
.margin-top28 {
  margin-top: 28rpx;
}
.margin-top-30 {
  margin-top: 30%;
}
.margin-bottom0 {
  margin-bottom: 0rpx !important;
}
.margin-bottom18 {
  margin-bottom: 18rpx;
}
.margin-bottom28 {
  margin-bottom: 28rpx;
}
.margin-bottom100 {
  padding-bottom: 150rpx;
}
.padding-top18 {
  padding-top: 18rpx;
}

.padding-top28 {
  padding-top: 28rpx;
}
.padding-right18{
  padding-right: 18rpx;
}
.padding-bottom18 {
  padding-bottom: 18rpx;
}
.padding-bottom60{
  padding-bottom: 60rpx;
}
.pd28 {
  padding: 28rpx;
}
.padding-top0 {
  padding-top: 0;
}
.padding-bottom0 {
  padding-bottom: 0;
}

.border-right {
  border-right: 1rpx solid #fff;
}

.bdr8 {
  border-radius: 8rpx;
}
.flex{
  display: flex;
}
.align-item-center {
  display: flex;
  align-items: center;
}
.align-item-flex-end{
  display: flex;
  align-items: flex-end;
}
.align-item-flex-start{
  display: flex;
  align-items: flex-start;
}

.flex-wrap{
  flex-wrap: wrap;
}

.margin-left40 {
  margin-left: 40rpx;
}
.margin-left180 {
  margin-left: 180rpx;
}

.item-tips-text {
  min-width: 40rpx;
  background: #f4333c;
  border-radius: 8rpx;
  padding: 3rpx 6rpx;
  border: 4rpx solid rgba(255, 255, 255, 1);
  position: absolute;
  top: -34rpx;
  right: -45rpx;
  color: rgba(255, 255, 255, 1);
  font-size: 20rpx;
}

.justify-content-between{
  justify-content: space-between;
}
.color-fff{
  color: #fff;
}
.color-red{
  color: red;
}
.color-007AFE{
  color: #007AFE
}
.color-ccc{
  color:#ccc;
}
.color-787878{
  color: #787878;
}
.bgc007AFE{
  background-color: #007AFE;
}
.border-eee{
  border: 1rpx solid #eee !important;
}
.border-16{
  border-radius: 16rpx;
}
.nb-tab-btn{
  padding: 6rpx 12rpx;
}
.line{
  width: .5px;
  height: 100%;
}
.img-wh30{
  width: 30px;
  height: 30px;
}
.order-btn{
  flex: 1;
  background-color: #0176ff;
  padding: 8rpx 16rpx;
  border-radius: 6rpx;
  font-size: 36rpx;
  color: #fff;
  text-align: center;
}


var formatTimestamp = function(number, format){
  if (!number) {
    return ''
  }
  var date = getDate(number)
  var Y = date.getFullYear()
  var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1)
  var D = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();

  var h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours())
  var m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes())
  var s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
  switch (format) {
    case 'yyyymmdd': // 2018-10-10
      // return `${Y}-${M}-${D}`
      return Y+'-'+M+'-'+D
    case 'mmdd': // 10-10
      // return `${M}-${D}`
      return M+'-'+D
    case 'MMDD': // 10月10日
      // return `${M}月${D}日`
      return M+'月'+D+'日'
    case 'hhmm': // 16:16
      // return `${h}:${m}`
      return h+':'+m
    case 'yyyymmddhhmm': // 2018-10-10 16:16
      // return `${Y}-${M}-${D} ${h}:${m}`
      return Y + '-' + M + '-' + D + ' ' + h + ':' + m
    case 'YYYYMMDDhhmm':
      return Y + '年' + M + '月' + D + '日' + ' ' + h + ':' + m
    // case 'mmddhhmm': // 10-10 16:16
    //   return `${M}-${D} ${h}:${m}`
    // case 'cnmmddhh': // 10月10日16点
    //   return `${M}月${D}日${h}点`
    default:
      break;
  }
}

var strTrim = function(str){
  if(!str){
    return str
  }
  return str.trim()
}

var substr = function (str, start, end) {
  if (!start) start = 0
  if (!end) end = 0
  return str ? str.substring(start, end) : str
}

// 手机号加横杠 188-8888-8888
var formatMobile = function (mobile) {
  if (!mobile) return mobile
  return mobile.split(' ').join('-')
}

var plusXing = function(str, frontLen, endLen) {
  var len = str.length - frontLen - endLen;
  var xing = '';
  for (var i = 0; i < len; i++) {
    xing += '*';
  }
  return str.substring(0, frontLen) + xing + str.substring(str.length - endLen);
}

module.exports = {
  formatTimestamp: formatTimestamp,
  strTrim: strTrim,
  substr: substr,
  formatMobile: formatMobile,
  plusXing: plusXing
};

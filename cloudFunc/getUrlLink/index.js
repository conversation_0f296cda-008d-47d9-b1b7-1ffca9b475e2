// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({env: cloud.DYNAMIC_CURRENT_ENV}) // 使用当前云环境

exports.main = async (event = {}, context) => {
  console.log('== event >>>', event)
  const {params = {}} = event || {}

  try {
    const result = await cloud.openapi.urllink.generate(params)
    console.log('result >>', result)

    const {urlLink, errMsg, errCode} = result || {}
    return urlLink || ''
  } catch (err) {
    console.error('== err >>', err)
    return err
  }
}

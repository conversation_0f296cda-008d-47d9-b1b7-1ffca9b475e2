// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({env: cloud.DYNAMIC_CURRENT_ENV}) // 使用当前云环境

const defConfig = {
  path: '/pages/loginAndRegist/startUp/index',
  query: 'scene=E514F15898512261D90B932330ED5B52',
  isExpire: true,
  expireType: 1,
  expireInterval: 21,
  envVersion: 'release',
}

// 云函数入口函数
exports.main = async (event = {}, context) => {
  console.log('getLink event >>', event)
  const {param = {}} = event || {}

  let res = {}
  try {
    res = await cloud.callFunction({
      name: 'getUrlLink',
      data: {
        params: {
          ...defConfig,
          ...param
        }
      }
    })
  } catch (err) {
    console.error('getLink err >>', err)
  }

  console.log('getLink res >>', res)
  return res
}

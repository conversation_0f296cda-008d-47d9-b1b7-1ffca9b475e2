@keyframes fadeIn{
    0%{
        opacity: 0;
    }
    100%{
        opacity: 1;
    }
}

.card{
    background-color: #fff;
}

.card-box{
    display: flex;
    flex-direction: column;
    margin: 4px 14px 0 14px;
    padding-bottom: 14px;
    border-bottom: 1px solid rgba(238, 238, 238, 0.3);
}

.card-title{
    display: flex;
    flex-direction: row;
    align-items: flex-end;
    justify-content: flex-start;
    flex: 1;
}

.card-title-text{
    font-size: 14px;
    color: #333;
    font-weight: 500;
    font-family: PingFangSC-Medium;
}

.card-subtitle-text{
    font-size: 24rpx;
    color: #969696;
    font-weight: 400;
    margin-top: 6px;
    margin-left: 10px;
    font-family: 'DINCond-Bold';
    flex: 1;
}

.tag-container{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    margin-bottom: 10rpx;
}

.tag-view{
    display: flex;
    background-color: #E8340F1A;
    padding: 8rpx 8rpx 8rpx 8rpx;
    border-radius: 6rpx;
    margin-right: 10px;
    margin-top: 10px;
    /* border:1rpx solid #969696; */
}

.tag-text{
    font-size: 10px;
    color: #E8340F;
    font-weight: 400;
    font-family: PingFangSC-Regular;
}

.bottom-container{
    display: flex;
    flex-direction: row;
    /* justify-content: space-between; */
    margin-top: 10px;
    flex: 1;
}

.bottom-view{
    display: flex;
    flex-direction: column;
    flex: 1;
}

.bottom-left-text{
    font-size: 40rpx;
    color: #F21509;
    font-weight: 400;
    font-family: 'DINCond-Bold';
}

.bottom-right-text{
    font-size: 24rpx;
    color: #969696;
    font-weight: 400;
    font-family: PingFangSC-Regular;
}

.text-one-line{
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
}

.item-normal{
}

.signal-normal{
}

.item-last{
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.item-list{
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.item-bottom{
    width: 100%;
    height: 15vh;
    background-color: #f7f7f7;
}

.item-signal-top{
    border-top-left-radius: 12rpx;
    border-top-right-radius: 12rpx;
}

.item-signal-bottom{
    border-bottom-left-radius: 12rpx;
    border-bottom-right-radius: 12rpx;
}

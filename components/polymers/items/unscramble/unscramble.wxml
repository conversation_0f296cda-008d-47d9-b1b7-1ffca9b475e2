<view class="card {{itemClass}} {{signalClass}} {{listClass}}">
  <view class="card-box" data-item="{{data}}" bind:tap="onMarketingAction"
        style="margin-top:{{(signalBlock && data.index==0)?-6:(isFromCard?0:14)}}px;padding-top:{{(signalBlock && data.index==0)?12:(isFromCard&&data.index!= 0)?14:0}}px;border-bottom-color: {{data.isLast?'#fff':'rgba(238, 238, 238, 0.3)'}}">
    <view class="card-title">
      <text class="card-title-text text-one-line">{{data.fundName || '-'}}</text>
      <text class="card-subtitle-text">{{data.fundCode || '-'}}</text>
    </view>
    <view class="tag-container">
      <view class="tag-view" wx:for="{{data.operateLabel}}" wx:for-item="item" wx:for-index="id" wx:key="id">
        <text class="tag-text">{{item || '-'}}</text>
      </view>
    </view>

    <view class="bottom-container">
      <view class="bottom-view" style="align-items: flex-start;">
        <text class="bottom-left-text text-one-line"
              style="color:{{data.dayRange > 0 ? '#F21509':'#07C91D'}};">{{(data.dayRange || '-') + '%'}}</text>
        <text class="bottom-right-text text-one-line">日涨跌幅</text>
      </view>
      <view class="bottom-view" style="align-items: flex-end;">
        <text class="bottom-left-text text-one-line" style="color: #333333;">{{data.netValue || '-'}}</text>
        <text class="bottom-right-text text-one-line">单位净值({{data.enddateStr}})</text>
      </view>
    </view>

  </view>
</view>

<view wx:if="{{itemClass==='item-last' && nomore}}" class="item-bottom"/>

<view class="card {{itemClass}} {{signalClass}} {{listClass}}">
  <view
      class="card-box"
      data-item="{{data}}"
      bind:tap="onMarketingAction"
      style="margin-top:{{(signalBlock && data.index==0)?-6:(isFromCard?0:14)}}px;padding-top:{{(signalBlock && data.index==0)?12:(isFromCard&&data.index!= 0)?14:0}}px;border-bottom-color: {{data.isLast?'#fff':'rgba(238, 238, 238, 0.3)'}}">

    <image src="../../../../imgs/icon/<EMAIL>" class="report-icon" mode="aspectFill"/>

    <view class="card-right">
      <text class="card-title-text text-one-line">{{data.fundName || '-'}}</text>
      <text class="card-subtitle-text text-one-line">{{data.fundCode || '-'}}</text>
    </view>
  </view>
</view>

<view wx:if="{{itemClass==='item-last' && nomore}}" class="item-bottom"/>

<view class='media {{itemClass}} {{signalClass}} {{listClass}}'>
  <view class="media-block"
        data-item="{{data}}"
        bind:tap="onMediaAction"
        id="{{data.index}}" data-content="action://share/AppAdvInvestorTrainCom" data-name="{{data.cName}}"  data-type="recommend" 
        style="margin-top:{{(signalBlock && data.index==0)?-6:(isFromCard?0:14)}}px;padding-top:{{(signalBlock && data.index==0)?12:(isFromCard&&data.index!= 0)?14:0}}px;border-bottom-color: {{data.isLast?'#fff':'rgba(238, 238, 238, 0.3)'}}">
    <image
        src="{{cover}}"
        mode="aspectFill"
        class="image {{imgAnimate}}"
    />

    <view class="media-content-block">
      <view class="media-title text-two-line">
        {{data.title || data.name || '-'}}
      </view>
      <view class="media-bottom">
        <view wx:if="{{data.timeCreatedStr || data.chapterName}}"
              class="card-row-box">
          <van-icon class="icon-time" name="underway-o" size="15px"/>
          <view wx:if="{{data.timeCreatedStr}}" class="card-left-time text-one-line">
            {{data.timeCreatedStr}} | 共{{data.countChapter || '-'}}章
          </view>

          <view wx:if="{{data.chapterName}}" class="card-left-time text-one-line">
            {{data.chapterName || '-'}}
          </view>
        </view>
      </view>
    </view>
  </view>
</view>

<view wx:if="{{itemClass==='item-last' && nomore}}" class="item-bottom"/>

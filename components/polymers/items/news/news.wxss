@keyframes fadeIn{
    0%{
        opacity: 0;
    }
    100%{
        opacity: 1;
    }
}

.card{
    background-color: #fff;
}

.card-box{
    display: flex;
    flex-direction: row;
    margin: 14px 14px 0 14px;
    padding-bottom: 14px;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(238, 238, 238, 0.3);
}

.card-row-box{
    display: flex;
    /*flex: 1;*/
    flex-direction: row;
    align-items: center;
    /*margin-top: 14px;*/
}

.card-left{
    display: flex;
    flex-direction: column;
    flex: 1;
    align-items: flex-start;
    justify-content: space-between;
    min-height: 140rpx;
    /*height: 140rpx;*/
    /* padding: 0 28rpx; */
}

.text-two-line{
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
}

.text-one-line{
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
}

.card-subtitle-txt{
    font-size: 24rpx;
    color: #999;
    font-weight: 400;
    margin-top: 6rpx;
    font-family: PingFangSC-Regular;
}

.card-left-title{
    font-size: 14px;
    /*line-height: 22px;*/
    color: #333;
    font-weight: 500;
    font-family: PingFangSC-Medium;
    letter-spacing: 0;
}

.card-left-time{
    font-size: 12px;
    font-weight: 400;
    color: #969696;
    display: flex;
    flex-direction: row;
    align-items: center;
    /*line-height: 16px;*/
}

.image{
    width: 100px;
    height: 75px;
    border-radius: 6px;
    margin-left: 20px;
}

.card-news-bottom{
    display: flex;
    flex-direction: row;
    align-items: flex-end;
    flex: 1
}

.icon-time{
    width: 14px;
    height: 14px;
    padding: 0;
    margin: 0 6px 0 0;
}

.item-normal{
}

.signal-normal{
}

.item-last{
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.item-list{
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.item-bottom{
    width: 100%;
    height: 15vh;
    background-color: #f7f7f7;
}

.item-signal-top{
    border-top-left-radius: 12rpx;
    border-top-right-radius: 12rpx;
}

.item-signal-bottom{
    border-bottom-left-radius: 12rpx;
    border-bottom-right-radius: 12rpx;
}

.fade_in{
    animation: fadeIn 1.2s both;
}

.fade_null{

}

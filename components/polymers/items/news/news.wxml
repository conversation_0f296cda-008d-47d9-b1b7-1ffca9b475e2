<view class="card {{itemClass}} {{signalClass}} {{listClass}}">
  <view
      class="card-box"
      data-item="{{data}}"
      bind:tap="onNewsAction"
      id="{{data.index}}" data-content="action://share/AppAdvNewsDetail" data-name="{{data.cName}}" data-type="recommend" 
      style="margin-top:{{(signalBlock && data.index==0)?-6:(isFromCard?0:14)}}px;padding-top:{{(signalBlock && data.index==0)?12:(isFromCard&&data.index!= 0)?14:0}}px;border-bottom-color: {{data.isLast?'#fff':'rgba(238, 238, 238, 0.3)'}}">

    <view class="card-left">
      <view class="card-left-title text-two-line">{{data.title || '-'}}</view>
      <view wx:if="{{subTitle}}" class="card-news-bottom">
        <view class="card-subtitle-txt text-one-line">
          {{subTitle || '-'}}
        </view>
      </view>

    </view>

    <image
        src="{{cover}}"
        mode="aspectFill"
        class="image {{imgAnimate}}"
    />
  </view>
</view>

<view wx:if="{{itemClass==='item-last' && nomore}}" class="item-bottom"/>

@keyframes fadeIn{
    0%{
        opacity: 0;
    }
    100%{
        opacity: 1;
    }
}

.card{
    margin: 0 14px 0 14px;
    background-color: #fff;
}

.card-box{
    display: flex;
    flex-direction: column;
    margin: 0 14px 0 14px;
    padding: 14px 0;
}

.card-total-title{
    display: flex;
    flex-direction: column;
    flex: 1;
}

.card-total-text{
    font-size: 14px;
    color: #333;
    font-family: PingFangSC-Regular;
}

.card-title{
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    justify-content: flex-start;
    flex: 1;
    /* border-top: 1px solid red; */
}

.card-title-text{
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
    font-family: PingFangSC-Medium;
    flex: 1;
}

.card-subtitle-text{
    font-size: 24rpx;
    color: #333;
    margin-top: 6rpx;
    margin-left: 14rpx;
    margin-right: 14rpx;
    font-family: 'DINCond-Bold';
}

.tag-container{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    background-color: #E8340F1A;
    padding: 8rpx 8rpx 8rpx 8rpx;
    border-radius: 6rpx;
}

.tag-text{
    font-size: 10px;
    color: #E8340F;
    font-weight: 400;
    font-family: PingFangSC-Regular;
}

.bottom-container{
    display: flex;
    flex-direction: row;
    margin-top: 14px;
    flex: 1;
}

.bottom-view{
    display: flex;
    flex-direction: row;
    align-items: center;
}

.bottom-left-text{
    font-size: 40rpx;
    color: #333333;
    font-weight: 400;
    font-family: 'DINCond-Bold';
    margin-right: 8rpx;
}

.bottom-right-text{
    font-size: 24rpx;
    color: #969696;
    font-weight: 400;
    font-family: PingFangSC-Regular;
    margin-top: 6rpx;
}

.text-one-line{
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
}

.item-normal{
}

.signal-normal{
}

.item-last{
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.item-list{
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.item-top{
    border-top-left-radius: 12rpx;
    border-top-right-radius: 12rpx;
}

.item-bottom{
    width: 100%;
    height: 15vh;
    background-color: #f7f7f7;
}

.item-signal-top{
    border-top-left-radius: 12rpx;
    border-top-right-radius: 12rpx;
}

.item-signal-bottom{
    border-bottom-left-radius: 12rpx;
    border-bottom-right-radius: 12rpx;
}

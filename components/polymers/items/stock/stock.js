import {enums, global, storage} from "../../../../common/index.js";

const {PAGE_INFO_REALM} = enums

Component({
  properties: {
    data: {
      type: Object,
      value: {},
    },
    isFirst: {
      type: Boolean,
      value: false,
    },
    isLast: {
      type: Boolean,
      value: false
    },
    isFromCard: {
      type: Boolean,
      value: false
    },
    signalBlock: {
      type: Boolean,
      value: false
    },
    elementType: {
      type: String,
      value: ''
    },
    nomore: {
      type: Boolean,
      value: false
    }
  },

  data: {
    imgAnimate: 'fade_null',
    itemClass: 'item-normal',
    listClass: 'item-normal',
    signalClass: 'signal-normal'
  },

  attached() {
    const {data = {}, isLast = false, nomore = false, signalBlock = false, elementType = ''} = this.properties || {}
    const {id = ''} = data || {}

    let hashPool = storage.getStorage(global.STORAGE_GLOBAL_HASH_POOL_LIST)
    if (!hashPool){
      hashPool = []
    }

    let itemClass = 'item-normal'
    let signalClass = 'signal-normal'
    let listClass = 'item-normal'
    if (isLast && nomore){
      itemClass = 'item-last'
    }
    if (signalBlock){
      if (data.index === 0){
        signalClass = 'item-signal-top'
      }

      if (data.isLast){
        signalClass = 'item-signal-bottom'
      }
      itemClass = 'signal-normal'
    } else {
      if (data.isLast){
        signalClass = 'item-signal-bottom'
      }
    }

    if (elementType && elementType === 'LIST'){
      listClass = 'item-list'
    }

    if (!data.hasOwnProperty('isItemLast')){
      listClass = 'item-normal'
    }

    if (data.isTotal){
      listClass = 'item-top'
    }

    this.setData({itemClass, signalClass, listClass})

    if (!hashPool.includes(id) && !!id){
      this.setData({
        imgAnimate: 'fade_in'
      })
      hashPool.push(id)
      storage.setStorage(global.STORAGE_GLOBAL_HASH_POOL_LIST, hashPool)
    }
  },

  methods: {
    onMarketingAction(e={}) {
      const {
        dataset: {
          item = {}
        }
      } = e.currentTarget || {}

      const {realm = '', isTotal} = item || {}
      if (isTotal) return

      const passParams = {
        ...item,
        action: `action://share/${PAGE_INFO_REALM[realm] || 'advFundScram'}`
      }

      //提供给事件监听函数
      this.triggerEvent('onItemClick', passParams, {bubbles: true, composed: true})
    }
  }
});

<view class="card {{itemClass}} {{signalClass}} {{listClass}}">
  <view class="card-box" data-item="{{data}}" bind:tap="onMarketingAction" style="margin-top:{{data.isTotal ? 14:0}}px;border-bottom: {{data.isTotal ? 1:0}}px solid #EEEEEE;">
    <view class="card-total-title" wx:if="{{data.isTotal}}">
      <text class="card-total-text">持有 {{data.holdname || '-'}}({{data.holdcode || '-'}}) 的基金有：</text>
      <text class="card-total-text">持仓市值合计：{{data.f_prt_stkvalue || '-'}}万</text>
    </view>
    <view class="card-title" wx:else="">
      <text class="card-title-text ">{{data.fundname || '-'}}<text class="card-subtitle-text">({{data.fundcode ||'-'}})</text>
      </text>
      <view class="tag-container">
        <text class="tag-text text-one-line">第 {{data.rn || '-'}} 大重仓</text>
      </view>
    </view>

    <view class="bottom-container" wx:if="{{!data.isTotal}}">
      <view class="bottom-view" style="justify-content: flex-start;flex: 1;">
        <text class="bottom-left-text">{{(data.f_prt_valueratio || '-') + '%'}}</text>
        <text class="bottom-right-text text-one-line">持仓比例</text>
      </view>
      <view class="bottom-view" style="justify-content: flex-end">
        <text class="bottom-left-text">{{data.f_prt_stkvalue || '-'}}</text>
        <text class="bottom-right-text text-one-line">持仓市值(万)</text>
      </view>
    </view>

  </view>
</view>

<view wx:if="{{itemClass==='item-last' && nomore}}" class="item-bottom"/>

import {global, storage} from "../../../../common/index.js";

Component({
  properties: {
    item: {
      type: Object,
      value: {}
    },
    isLast: {
      type: Boolean,
      value: false
    },
    elementType: {
      type: String,
      value: ''
    },
    nomore: {
      type: Boolean,
      value: false
    }
  },
  data: {
    imgAnimate: 'fade_null',
    itemClass: 'item-normal',
    listClass: 'item-normal',
    signalClass: 'signal-normal'
  },

  attached() {
    const {item = {}, isLast = false, nomore = false, signalBlock = false, elementType = ''} = this.properties || {}
    const {id = ''} = item || {}

    let hashPool = storage.getStorage(global.STORAGE_GLOBAL_HASH_POOL_LIST)
    if (!hashPool){
      hashPool = []
    }

    let itemClass = 'item-normal'
    let signalClass = 'signal-normal'
    let listClass = 'item-normal'
    if (isLast && nomore){
      itemClass = 'item-last'
    }
    if (signalBlock){
      if (item.index === 0){
        signalClass = 'item-signal-top'
      }

      if (item.isLast){
        signalClass = 'item-signal-bottom'
      }
      itemClass = 'signal-normal'
    } else {
      if (item.isLast){
        signalClass = 'item-signal-bottom'
      }
    }

    if (elementType && elementType === 'LIST'){
      listClass = 'item-list'
    }

    this.setData({itemClass, signalClass, listClass})

    if (!hashPool.includes(id) && !!id){
      this.setData({
        imgAnimate: 'fade_in'
      })
      hashPool.push(id)
      storage.setStorage(global.STORAGE_GLOBAL_HASH_POOL_LIST, hashPool)
    }
  },
  methods: {
    onPosterAction() {
    }
  }
});

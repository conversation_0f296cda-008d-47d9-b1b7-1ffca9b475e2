@keyframes fadeIn{
    0%{
        opacity: 0;
    }
    100%{
        opacity: 1;
    }
}

.card-box{
    display: flex;
    flex-direction: column;
    width: calc((100vw - 28px) / 3);
    align-items: center;
    justify-content: center;
}

.poster-image{
    height: 340rpx;
    /*margin-bottom: 12px;*/
    margin: 0 22rpx 12px 22rpx;
}

.poster-block{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    /* width: 220rpx; */
    height: 360rpx;
    border-radius: 12rpx;
    background-color: #ccc;
}

.poster-empty-img{
    height: 340rpx;
    margin-bottom: 12px;
}

.item-normal{
}

.signal-normal{
}

.item-last{
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.item-list{
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.item-bottom{
    width: 100%;
    height: 15vh;
    background-color: #f7f7f7;
}

.item-signal-top{
    border-top-left-radius: 12rpx;
    border-top-right-radius: 12rpx;
}

.item-signal-bottom{
    border-bottom-left-radius: 12rpx;
    border-bottom-right-radius: 12rpx;
}

.fade_in{
    animation: fadeIn 1.2s both;
}

.fade_null{

}

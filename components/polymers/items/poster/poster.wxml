<view class="card {{itemClass}} {{signalClass}} {{listClass}}" bind:tap="onPosterAction">
  <image
      wx:if="{{item.uri}}"
      mode="heightFix"
      src="{{item.uri||''}}?x-oss-process=image/resize,m_fill,h_360,w_220"
      class="poster-image {{imgAnimate}}"
  />
  <view wx:else class="poster-block">
    <image src="../../../../imgs/icon/<EMAIL>"
           class="poster-empty-img"
           mode="heightFix"/>
  </view>
</view>
<view wx:if="{{itemClass==='item-last' && nomore}}" class="item-bottom"/>

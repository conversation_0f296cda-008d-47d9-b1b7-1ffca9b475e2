<view class="card {{itemClass}} {{signalClass}} {{listClass}}">
  <view
      class="card-box"
      data-item="{{data}}"
      bind:tap="onMarketingAction"
      id="{{data.index}}" data-content="action://share/advProductDataDetail" data-name="{{data.cName}}"  data-type="recommend" 
      style="margin-top:{{(signalBlock && data.index==0)?-6:(isFromCard?0:14)}}px;padding-top:{{(signalBlock && data.index==0)?12:(isFromCard&&data.index!= 0)?14:0}}px;border-bottom-color: {{data.isLast?'#fff':'rgba(238, 238, 238, 0.3)'}}">
    <image
        src="{{cover}}"
        mode="aspectFill"
        class="image {{imgAnimate}}"
    />
    <view class="card-right">
      <text class="market-title-txt text-one-line">{{data.title || '-'}}</text>
      <view class="card-left-subtitle text-one-line">{{data.subTitle || data.summary || '-'}}</view>

      <view class="card-bottom-info">
        <view class="card-left-time text-one-line">{{data.shortDesc || data.guide || data.chapterName || '-'}}</view>
      </view>
    </view>
  </view>
</view>

<view wx:if="{{itemClass==='item-last' && nomore}}" class="item-bottom"/>

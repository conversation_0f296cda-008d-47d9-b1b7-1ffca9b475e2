@keyframes fadeIn{
    0%{
        opacity: 0;
    }
    100%{
        opacity: 1;
    }
}

.card{
    background-color: #fff;
}

.card-box{
    display: flex;
    flex-direction: row;
    margin: 14px 14px 0 14px;
    padding-bottom: 14px;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(238, 238, 238, 0.3);
}

.card-right{
    display: flex;
    flex-direction: column;
    flex: 1;
    align-items: flex-start;
    justify-content: flex-start;
    /* height: 140rpx; */
}

.market-title-txt{
    font-size: 14px;
    color: #333;
    font-weight: 500;
    font-family: PingFangSC-Medium;
}

.text-one-line{
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
}

.card-left-title{
    font-size: 36rpx;
    color: #333;
}

.card-left-subtitle{
    font-size: 24rpx;
    color: #969696;
    font-weight: 400;
    margin-top: 6px;
    font-family: PingFangSC-Regular;
}

.card-bottom-info{
    display: flex;
    flex-direction: row;
    align-items: flex-end;
    justify-content: flex-start;
    flex: 1;
}

.card-left-time{
    font-size: 12px;
    /* line-height: 16px; */
    font-weight: 400;
    margin-top: 20px;
    color: #ccc;
}

.image{
    width: 80px;
    height: 80px;
    border-radius: 12rpx;
    margin-right: 14px;
}

.item-normal{
}

.signal-normal{
}

.item-last{
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.item-list{
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.item-bottom{
    width: 100%;
    height: 1rpx;
    background-color: #f7f7f7;
}

.item-signal-top{
    border-top-left-radius: 12rpx;
    border-top-right-radius: 12rpx;
}

.item-signal-bottom{
    border-bottom-left-radius: 12rpx;
    border-bottom-right-radius: 12rpx;
}

.fade_in{
    animation: fadeIn 1.2s both;
}

.fade_null{

}

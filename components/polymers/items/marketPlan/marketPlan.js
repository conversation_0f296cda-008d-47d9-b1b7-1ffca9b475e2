import {enums, global, storage} from "../../../../common/index.js";

const {PAGE_INFO_REALM, OSS_BASE} = enums

const NORMAL_CUT = '/crop,w_600,x_22,g_west'
const NORMAL_RESIZE = '/resize,m_mfit,h_600,limit_0'

Component({
  properties: {
    data: {
      type: Object,
      value: {}
    },
    isFromCard: {
      type: Boolean,
      value: false
    },
    signalBlock: {
      type: Boolean,
      value: false
    },
    cId: {
      type: String | Number,
      value: ''
    },
    cName: {
      type: String,
      value: ''
    },
    mName: {
      type: String,
      value: ''
    },
    isLast: {
      type: Boolean,
      value: false
    },
    elementType: {
      type: String,
      value: ''
    },
    nomore: {
      type: Boolean,
      value: false
    }
  },

  data: {
    imgAnimate: 'fade_null',
    itemClass: 'item-normal',
    listClass: 'item-normal',
    signalClass: 'signal-normal',
    cover: ''
  },

  attached() {
    const {data = {}, isLast = false, nomore = false, signalBlock = false, elementType = ''} = this.properties || {}
    const {id = '', avatar = '', cover = ''} = data || {}
    let _cover = avatar || cover || ''
    if (`${_cover}`.includes('x-oss-process')){
      const [oUrl, param] = `${_cover}`.split('?x-oss-process=image')
      _cover = `${oUrl}${OSS_BASE}${param}${NORMAL_RESIZE}/format,jpg`
    } else {
      _cover = `${_cover}${OSS_BASE}${NORMAL_RESIZE}${NORMAL_CUT}`
    }

    let hashPool = storage.getStorage(global.STORAGE_GLOBAL_HASH_POOL_LIST)
    if (!hashPool){
      hashPool = []
    }

    let itemClass = 'item-normal'
    let signalClass = 'signal-normal'
    let listClass = 'item-normal'
    if (isLast && nomore){
      itemClass = 'item-last'
    }
    if (signalBlock){
      if (data.index === 0){
        signalClass = 'item-signal-top'
      }

      if (data.isLast){
        signalClass = 'item-signal-bottom'
      }
      itemClass = 'signal-normal'
    } else {
      if (data.isLast){
        signalClass = 'item-signal-bottom'
      }
    }

    if (elementType && elementType === 'LIST'){
      listClass = 'item-list'
    }

    this.setData({itemClass, signalClass, listClass, cover: _cover})

    if (!hashPool.includes(id) && !!id){
      this.setData({
        imgAnimate: 'fade_in'
      })
      hashPool.push(id)
      storage.setStorage(global.STORAGE_GLOBAL_HASH_POOL_LIST, hashPool)
    }
  },

  methods: {
    onMarketingAction(e={}) {
      const {
        dataset: {
          item = {}
        }
      } = e.currentTarget || {}

      const {cName = '', cId = '', mName = ''} = this.data || {}
      const {realm = ''} = item || {}

      const passParams = {
        ...item,
        action: `action://share/${PAGE_INFO_REALM[realm] || 'advProductDataDetail'}`,
        cName,
        cId,
        mName,
      }

      //提供给事件监听函数
      this.triggerEvent('onItemClick', passParams, {bubbles: true, composed: true})
    }
  }
});

<view class="card-box  {{itemClass}} {{signalClass}} {{listClass}}">
  <view class="img-box">
    <image
        src="{{cover}}"
        mode="widthFix"
        class="image"
    />
  </view>
  <view class="card-status">
    <view class="card-bottom-status text-one-line"
          style="background-color:{{LIVE_STATUS[item.liveStatus]||'crimson'}};">
      {{item.liveStatusStr || ''}}
    </view>
  </view>
  <view class="card-bottom">
    <view class="card-bottom-line">
      <view class="card-bottom-title text-one-line">{{item.title || '-'}}</view>
    </view>
    <view class="card-bottom-line">
      <view class="card-bottom-line-top">
        <image
            src="../../../../imgs/empty/<EMAIL>"
            mode="scaleToFill"
            class="head-icon"
        />
        <view class="card-bottom-time">{{item.speaker || '-'}}</view>
      </view>
      <view class="card-bottom-line-top">
        <image
            src="../../../../imgs/empty/<EMAIL>"
            mode="scaleToFill"
            class="head-icon"
        />
        <view class="card-bottom-time">{{item.startTimeStr}}</view>
      </view>
    </view>
  </view>
</view>

<view wx:if="{{itemClass==='item-last' && nomore}}" class="item-bottom"/>

.card-box{
    display: flex;
    flex-direction: column;
    position: relative;
}

.card-bottom{
    flex-direction: column;
    align-items: center;
}

.text-one-line{
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
}

.card-bottom-title{
    font-size: 14px;
    color: #333;
    margin: 14px 14px 0;
    font-weight: 500;
}

.card-bottom-time{
    font-size: 12px;
    /* line-height: 16pt; */
    font-weight: 400;
    color: #969696;
    margin: 14px 14px 14px 0;
}

.img-box{
    padding: 0;
    margin: 0;
}

.image{
    width: 100%;
    height: 480rpx;
    max-height: 480rpx;
}

.head-icon{
    width: 14px;
    height: 14px;
    margin-left: 14px;
    margin-right: 6px;
}

.card-bottom-line{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}

.card-bottom-line-top{
    display: flex;
    flex-direction: row;
    align-items: center;
}

.card-bottom-status{
    padding: 5px 20px;
    color: #fff;
    font-size: 16px;
    border-bottom-left-radius: 10px;
}

.card-status{
    position: absolute;
    right: 0;
    top: 0;
}

.item-normal{
}

.signal-normal{
}

.item-last{
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.item-list{
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.item-bottom{
    width: 100%;
    height: 15vh;
    background-color: #f7f7f7;
}

.item-signal-top{
    border-top-left-radius: 12rpx;
    border-top-right-radius: 12rpx;
}

.item-signal-bottom{
    border-bottom-left-radius: 12rpx;
    border-bottom-right-radius: 12rpx;
}

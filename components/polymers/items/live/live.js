import {enums} from "../../../../common/index.js";

const {LIVE_STATUS, OSS_BASE} = enums

const NORMAL_RESIZE = '/resize,m_pad,h_420,w_750'

Component({
  properties: {
    item: {
      type: Object,
      value: {}
    },
    isLast: {
      type: Boolean,
      value: false
    },
    elementType: {
      type: String,
      value: ''
    },
    nomore: {
      type: Boolean,
      value: false
    }
  },
  data: {
    LIVE_STATUS,
    itemClass: 'item-normal',
    listClass: 'item-normal',
    signalClass: 'signal-normal',
    cover: ''
  },

  attached() {
    const {item = {}, isLast = false, nomore = false, signalBlock = false, elementType = ''} = this.properties || {}
    const {url = ''} = item || {}
    let _cover = url || ''
    if (`${_cover}`.includes('x-oss-process')){
      const [oUrl, param] = `${_cover}`.split('?x-oss-process=image')
      _cover = `${oUrl}${OSS_BASE}${param}${NORMAL_RESIZE}/format,jpg`
    } else {
      _cover = `${_cover}${OSS_BASE}${NORMAL_RESIZE}`
    }

    let itemClass = 'item-normal'
    let signalClass = 'signal-normal'
    let listClass = 'item-normal'
    if (isLast && nomore){
      itemClass = 'item-last'
    }
    if (signalBlock){
      if (item.index === 0){
        signalClass = 'item-signal-top'
      }

      if (item.isLast){
        signalClass = 'item-signal-bottom'
      }
      itemClass = 'signal-normal'
    } else {
      if (item.isLast){
        signalClass = 'item-signal-bottom'
      }
    }

    if (elementType && elementType === 'LIST'){
      listClass = 'item-list'
    }
    this.setData({itemClass, signalClass, listClass, cover: _cover})
  }
});

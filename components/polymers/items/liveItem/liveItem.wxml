<view class='card {{itemClass}} {{signalClass}} {{listClass}}'>
  <view class="live-block"
        bind:tap="onLiveAction"
        id="{{data.index}}" data-content="action://share/AppAdvLive" data-name="{{data.cName}}"  data-type="recommend" 
        style="margin-top:{{(signalBlock && data.index==0)?-6:0}}px;padding-top:{{(signalBlock && data.index==0)?12:14}}px;">
    <view class="live-img-block" style="border-bottom-color: {{data.isLast?'#fff':'rgba(238, 238, 238, 0.3)'}}">
      <image
          src="{{cover}}"
          class="live-img {{imgAnimate}}"
          mode="widthFix"/>

      <view wx:if="{{data.liveStatusStr}}" class="live-status-block"
            style="background-color:{{LIVE_STATUS[data.liveStatus]||'crimson'}};">
        <view class="status-tips">{{data.liveStatusStr || ''}}</view>
      </view>
    </view>

    <view class="live-content-block">
      <view class="live-title text-two-line">
        {{data.title || '-'}}
      </view>

      <view class="live-bottom">
        <view class="live-bottom-txt text-one-line" wx:if="{{data.shortDesc}}">
          {{data.shortDesc || '-'}}
        </view>

        <view class="live-bottom-txt text-one-line" wx:if="{{!data.shortDesc && data.subTitle}}">
          {{data.subTitle || '-'}}
        </view>
      </view>
    </view>
  </view>
</view>

<view wx:if="{{itemClass==='item-last' && nomore}}" class="item-bottom"/>

import { getMarketPosterList, getPosterList, getH5PosterList } from "../../../../common/nb/home";
import {
  enums,
  interaction,
  md5,
  qs,
} from "../../../../common/index.js";
import { getLoadingMoment, getUserRole } from "../../../../common/utils/userStorage";

const { CARD_SURROUND, LOGIN_VISITOR } = enums
const PAGE_SIZE = 4

Component({
  properties: {
    data: {
      type: Object,
      value: {},
      observer: 'onHandleRefresh'
    },
    intoView: {
      type: String,
      value: 'poster0',
    },
    isHeaderBlock: {
      type: Boolean,
      value: false
    },
    cardId: {
      type: String | Number,
      value: ''
    }
  },
  data: {
    cardInfo: {},
    cardName: '',
    aggRecCardIds: '',
    list: [],
    contentInfo: [],
    titleInfo: {},
    titleAreaVisible: '',
    template: '',
    isSignalCard: false,

    currList: [],
    TabCur: 0,  // 一级分类idx
    initNextIdxs: [], // 二级分类idx
    nextIndex: 0, // 二级分类idx
    posterLine: 'poster-line-more',
  },

  attached() {
    const { data = {} } = this.properties || {}
    return this.installCardInfo(data)
  },

  methods: {
    async getPosterList(params = {}) {
      let posterRes = []
      const { success, msg, param } = await getMarketPosterList(params)
      if (!success) {
        interaction.showToast(msg || '')
        return posterRes
      }

      // action://share/AppAdvInvestorTrainCom?value=3094776719325704&shareConfig=true&categoryId=3095560693464058
      const { content = [] } = param || {}
      if (content && content.length) {
        content.forEach((pItem, pIndex) => {
          const { categoryId, channel, groupId, id } = pItem || {}
          const params = {
            categoryId,
            channel,
            groupId,
            id,
          }
          const props = {
            ...pItem,
            posterIndex: pIndex,
            action: `action://share/AppAdvPosterInfo?${qs.stringify(params)}`
          }

          posterRes.push(props)
        })
      }

      return posterRes
    },

    onPosterClick(e = {}) {
      const {
        currentTarget: {
          dataset: {
            item = {}
          }
        }
      } = e || {}

      //提供给事件监听函数
      this.triggerEvent('onItemClick', {
        ...item,
        action: 'action://share/AppAdvPosterInfo'
      }, { bubbles: true, composed: true })
    },

    // 切换一级 tab
    tabSelect(e = {}) {
      const { contentInfo } = this.data || {}
      const { index = 0 } = e.detail || {}

      let tIdx = contentInfo && contentInfo[index]?.category[0]?.idx || 0
      if (tIdx !== -1) {
        let _currList = contentInfo[index]?.category[tIdx]?.content || []
        if (_currList && _currList.length) {
          _currList = _currList.map((_cItem) => {
            let { uri = '' } = _cItem || {}
            let _split = `${uri}`.split("://")
            if (_split.length <= 1) {
              uri = uri.replace('https:/', 'https://')
            }

            return {
              ..._cItem,
              uri
            }
          })
        }

        this.data.currList = _currList
        this.data.initNextIdxs = contentInfo[index]?.category || []
        let pLen = this.data.currList.length
        let posterLine = 'poster-line-less'
        if (pLen == 4) {
          posterLine = 'poster-line-more'
        } else if (pLen == 3) {
          posterLine = 'poster-line-center'
        }

        this.setData({
          TabCur: index,
          currList: this.data.currList,
          initNextIdxs: this.data.initNextIdxs,
          nextIndex: tIdx,
          posterLine
        })
      } else {
        this.data.initNextIdxs = contentInfo[index]?.category || []
        this.setData({
          TabCur: index,
          currList: [],
          initNextIdxs: this.data.initNextIdxs,
          nextIndex: tIdx,
        })
      }
    },

    // 切换二级 tab
    clickCategory(e = {}) {
      const { initNextIdxs } = this.data || {}
      const { index = 0 } = e.detail || {}

      let nextIndex = index
      this.data.initNextIdxs[index].idx = index
      let _currList = initNextIdxs[index]?.content || []
      if (_currList && _currList.length) {
        _currList = _currList.map((cItem) => {
          let { uri = '' } = cItem || {}
          let _split = `${uri}`.split("://")
          if (_split.length <= 1) {
            uri = uri.replace('https:/', 'https://')
          }

          return {
            ...cItem,
            uri
          }
        })
      }

      let pLen = initNextIdxs[index]?.content?.length
      let posterLine = 'poster-line-less'
      if (pLen == 4) {
        posterLine = 'poster-line-more'
      } else if (pLen == 3) {
        posterLine = 'poster-line-center'
      }

      this.setData({
        nextIndex,
        initNextIdxs: this.data.initNextIdxs,
        currList: _currList,
        posterLine: posterLine,
      })
    },

    async getPosterListByTag(list = []) {
      const userRole = getUserRole()
      const hasLogin = !LOGIN_VISITOR.includes(userRole * 1)

      let _reInstall = []
      if (list && list.length) {
        for (const item of list) {
          let _tubList = []
          const { category = [] } = item || {}
          if (category && category.length) {
            for (let i = 0; i < category.length; i++) {
              const { id = '' } = category[i] || {}
              const params = {
                categoryId: id,
                page: 0,
                pageSize: PAGE_SIZE
              }
              let posterApi = hasLogin ? getPosterList : getH5PosterList
              const { success, param = {} } = await posterApi(params)

              let _content = []
              if (success) {
                const { content = [] } = param || {}
                _content = [].concat(content)
              }

              const rInsItem = {
                ...category[i],
                content: _content,
                idx: _content && _content.length ? 0 : -1 // -1为没有二级分类
              }
              _tubList.push(rInsItem)
            }
          }

          const tubParam = {
            ...item,
            category: _tubList
          }
          _reInstall.push(tubParam)
        }
      }

      return _reInstall
    },

    async installCardInfo(data = {}) {
      const { cardName = '', aggRecCardIds = '', finalConf = {} } = data
      const { TabCur = 0, nextIndex = 0, cardId = '' } = this.data || {}
      let cardInfo = {}
      if (typeof finalConf === 'string') {
        cardInfo = JSON.parse(finalConf)
      }
      if (typeof finalConf === 'object') {
        cardInfo = Object.assign(cardInfo, finalConf)
      }

      let _pList = []
      const {
        interaction = {},
        subTitle = {},
        title = {},
        template = '',
        titleAreaVisible = 1,
        area = 1,
        imageUrl = '',
        dataSource: {
          data: pList = []
        }
      } = cardInfo || {}
      let _cardName = title?.text || ''

      if (pList && pList.length && Object.keys(pList[0]).length) {
        _pList = [].concat(pList)
      }

      if (_pList && _pList.length) {
        let currList = []
        _pList = await this.getPosterListByTag(_pList)

        const initNextIdxs = []
        if (_pList && _pList.length) {
          _pList.forEach((iItem, iIndex) => {
            if (TabCur === iIndex) {
              const { category = [] } = iItem || {}

              category.forEach((caItem) => {
                const props = {
                  ...caItem,
                }
                initNextIdxs.push(props)
              })
            }
          })

          const fList = _pList[TabCur]?.category || []
          currList = fList[nextIndex]?.content || []

          currList = currList.map((dItem, dIndex) => {
            let { uri = '' } = dItem || {}
            let _split = `${uri}`.split("://")
            if (_split.length <= 1) {
              uri = uri.replace('https:/', 'https://')
            }

            return {
              ...dItem,
              uri,
              isLast: dIndex == currList.length - 1,
              signalBlock: false,
              cName: _cardName,
              cId: cardId,
              mName: fList[nextIndex]?.name || ''
            }
          })
        }

        const titleInfo = {
          interaction,
          subTitle,
          title,
          template,
          cardType: cardName,
          titleAreaVisible,
          cardSurround: CARD_SURROUND[area * 1],
          hasIcon: imageUrl,
          contentHasScrollTab: _pList && _pList.length > 1,
          contentHasSubNav: true
        }

        let pLen = currList?.length
        let posterLine = 'poster-line-less'
        if (pLen == 4) {
          posterLine = 'poster-line-more'
        } else if (pLen == 3) {
          posterLine = 'poster-line-center'
        }

        this.setData({
          list: currList || [],
          currList,
          posterLine,
          aggRecCardIds,
          template,
          titleInfo,
          titleAreaVisible,
          contentInfo: _pList,
          initNextIdxs,
          isSignalCard: titleAreaVisible == 2
        })
        return
      }

      let params = {
        page: 0,
        pageSize: PAGE_SIZE,
        recommendation: 'RECOMMENDATION',
        status: 'PUBLISHED'
      }
      let posterRes = await this.getPosterList(params)
      posterRes = posterRes.map((item) => {
        return {
          ...item,
          cName: _cardName,
          cId: cardId,
          mName: ''
        }
      })

      const titleInfo = {
        interaction,
        subTitle,
        title,
        template,
        cardType: cardName,
        titleAreaVisible,
        cardSurround: CARD_SURROUND[area * 1],
        hasIcon: imageUrl,
        contentHasScrollTab: true
      }
      let pLen = posterRes?.length
      let posterLine = 'poster-line-less'
      if (pLen == 4) {
        posterLine = 'poster-line-more'
      } else if (pLen == 3) {
        posterLine = 'poster-line-center'
      }

      this.setData({
        list: posterRes || [],
        currList: posterRes || [],
        posterLine,
        aggRecCardIds,
        template,
        titleInfo,
        titleAreaVisible,
        isSignalCard: titleAreaVisible == 2
      })
    },

    onHandleRefresh(newVal = {}, oldVal = {}) {
      const { finalConf: newFC = '' } = newVal
      const { finalConf: oldFC = '' } = oldVal
      let doRefresh = getLoadingMoment()

      const diffVal = md5.hexMD5(JSON.stringify(newFC)) === md5.hexMD5(JSON.stringify(oldFC))
      if (!diffVal && doRefresh) {
        return this.installCardInfo(newVal)
      }
    }
  }
});

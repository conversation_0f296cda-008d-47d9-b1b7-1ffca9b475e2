<nb-card wx:if="{{list.length>0}}" isGridCard="{{true}}">
  <titleBar data="{{titleInfo}}" isHeaderBlock="{{isHeaderBlock}}" />

  <view class="poster-nav" wx:if="{{contentInfo && contentInfo.length>1}}" style="margin-top: {{isSignalCard ? 40 : 0}}rpx">
    <scrollable-tabview wx:if="{{contentInfo && contentInfo.length>1}}" class=".current-item {{index==TabCur?'text-black':''}}" tabs='{{contentInfo}}' bind:tabNavClick='tabSelect' backgroundColor="#fff" activeColor="#333" color="#999" showTabLine="{{true}}" barBgColor="{{$state.themeColor}}" currentIndex="{{TabCur}}" />
  </view>

  <view class="subNav" wx:if="{{initNextIdxs[nextIndex].idx!==-1 && contentInfo[TabCur].category.length>1}}" style="border-top-left-radius: {{!(contentInfo && contentInfo.length>1)?12:0}}rpx;border-top-right-radius: {{!(contentInfo && contentInfo.length>1)?12:0}}rpx">
    <category tabs="{{contentInfo[TabCur].category}}" intoView="poster0" hasMoreTab="{{contentInfo && contentInfo.length>1}}" bind:tabBarClick='clickCategory' currentIndex="{{initNextIdxs[nextIndex].idx}}" />
  </view>

  <view wx:if="{{currList && currList.length}}" class="poster-view-block {{posterLine}}" style="width: 100%;margin-top: {{isSignalCard && !contentInfo.length?40:(initNextIdxs.length>1?-18:0)}}rpx">
    <view class="poster-item" style="margin-right: {{currList.length<4?28:0}}rpx" wx:for="{{currList}}" wx:key="idx" wx:for-index="idx" wx:for-item="item">
      <image wx:if="{{item.uri}}" class="poster-image" src="{{item.uri}}?x-oss-process=image/resize,m_fill,h_228,w_160" data-item="{{item}}" id="{{idx}}" data-content="{{item.uri}}" data-name="{{titleInfo.title.text}}" data-type="poster" mode="aspectFill" bindtap="onPosterClick" />
      <view wx:else class="poster-temp-block">
        <image src="../../../../imgs/icon/<EMAIL>" class="poster-empty-img" mode="aspectFill" />
      </view>
    </view>
  </view>

  <view wx:else class="poster-empty-block" style="width: 100%;margin-top: {{isSignalCard && !contentInfo.length?40:(initNextIdxs.length>1?-18:0)}}rpx">
    <image src="../../../../imgs/empty/<EMAIL>" class="empty-img" mode="aspectFill" />
    <view class="empty-tips-txt">
      {{'暂无内容'}}
    </view>
  </view>
</nb-card>
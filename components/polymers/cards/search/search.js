import {getLoadingMoment} from "../../../../common/utils/userStorage";
import {md5} from "../../../../common/index.js";

function isEmptyObject(obj = {}) {
  return Object.keys(obj).length === 0;
}

Component({
  properties: {
    data: {
      type: Object,
      value: {},
      observer: 'onHandleRefresh'
    },
    cardId: {
      type: String | Number,
      value: ''
    }
  },

  data: {
    placeholder: '',
    template: '',
    titleAreaVisible: 0,
    cardName: '',
    cardType: '',
    aggRecCardIds: '',
    channelIds: [],
    searchInfo: {}
  },

  attached() {
    const {data = {}} = this.properties || {}
    return this.installCardInfo(data)
  },

  methods: {
    doSearch() {
      const {channelIds, data = {}, placeholder = ""} = this.data || {}
      const {id} = data || {}
      const passProps = {
        channelIds,
        id,
        placeholder
      }

      //提供给事件监听函数
      this.triggerEvent('onSearchAction', passProps, {bubbles: true, composed: true})
    },

    installCardInfo(params = {}) {
      const {
        finalConf = {},
        aggRecCardIds = '',
        cardName = '',
        cardType = '',
        id
      } = params || {}
      let searchInfo = {}
      if (typeof finalConf === 'string'){
        searchInfo = JSON.parse(finalConf)
      }
      if (typeof finalConf === 'object'){
        searchInfo = Object.assign(searchInfo, finalConf)
      }
      let _channelIds = [], _title = ''
      const {
        dataSource = {},
        template = '',
        titleAreaVisible = '',
        title
      } = searchInfo || {}

      if (!isEmptyObject(dataSource)){
        const {channelIds = [], title = ''} = dataSource || {}
        _channelIds = [].concat(channelIds)
        _title = title
      }

      this.setData({
        placeholder: _title,
        channelIds: _channelIds,
        template,
        titleAreaVisible,
        cardName,
        cardType,
        aggRecCardIds,
        searchInfo,
        searchId:id
      })
    },

    onHandleRefresh(newVal = {}, oldVal = {}) {
      const {finalConf: newFC = ''} = newVal
      const {finalConf: oldFC = ''} = oldVal
      const diffVal = md5.hexMD5(JSON.stringify(newFC)) === md5.hexMD5(JSON.stringify(oldFC))

      if (!diffVal && getLoadingMoment()){
        return this.installCardInfo(newVal)
      }
    }
  }
});

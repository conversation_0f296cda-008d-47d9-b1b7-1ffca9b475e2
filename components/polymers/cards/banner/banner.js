import { global, md5, storage } from "../../../../common/index.js";
const { platform } = wx.getSystemInfoSync()
Component({
  properties: {
    data: {
      type: Array | Object,
      value: [],
      observer: 'onHandleRefresh'
    },
    isSignalCard: {
      type: <PERSON><PERSON><PERSON>,
      value: false
    }
  },
  data: {
    modePlat: (platform === "ios"),
    swiperCurrent: 0,
    imgAnimate: 'fade_null',
    newData: []
  },

  attached() {
    const { data = [] } = this.properties || {}

    let hashPool = storage.getStorage(global.STORAGE_GLOBAL_HASH_POOL_LIST)
    if (!hashPool) {
      hashPool = []
    }

    let bannerId = md5.hexMD5(JSON.stringify(data))
    if (!hashPool.includes(bannerId) && !!bannerId) {
      this.setData({
        imgAnimate: 'fade_in'
      })
      hashPool.push(bannerId)
      storage.setStorage(global.STORAGE_GLOBAL_HASH_POOL_LIST, hashPool)
    }
  },

  methods: {
    handleClickBanner(e = {}) {
      const { currentTarget: { dataset: { item = {} } } } = e || {}
      const { cardName = '' } = this.data || {}
      const passProps = {
        cardName,
        ...item
      }
      //提供给事件监听函数
      this.triggerEvent('onItemClick', passProps, { bubbles: true, composed: true })
      // bubbles: true, composed: true ===> 绑定穿透
    },

    installCardInfo(data = []) {
      if (data && data.length) {
        const newData = data.map((item, index) => {
          return { ...item, index }
        })
        this.setData({ newData })
      }
    },

    onHandleRefresh(newVal = []) {
      return this.installCardInfo(newVal)
    },

    fn(e = {}) {
      const { dataset: { i = 0 } } = e.currentTarget || {}
      this.setData({
        swiperCurrent: i
      })
    },

    swiperChange: function (e = {}) {
      let { current, source } = e.detail || {}
      if (source === 'autoplay' || source === 'touch') {
        //根据官方 source 来进行判断swiper的change事件是通过什么来触发的，autoplay是自动轮播。
        // touch是用户手动滑动。
        // 其他的就是未知问题。
        // 抖动问题主要由于未知问题引起的，所以做了限制，只有在自动轮播和用户主动触发才去改变current值，达到规避了抖动bug
        this.setData({
          swiperCurrent: current
        })
      }
    }
  }
});

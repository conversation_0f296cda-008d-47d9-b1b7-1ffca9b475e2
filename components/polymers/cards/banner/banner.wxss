@keyframes fadeIn {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.banner-block {
  /*解决真机图片圆角问题*/
  transform: translateY(0);
  width: 100%;
  height: 166px;
}

.swiper-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  overflow: unset;
  text-align: center;
  /*margin-top: -10px;*/
}

.image-swiper {
  width: calc(100% - 40px);
  /*width: 100%;*/
  /*box-shadow: 0 10px 10px 0 rgba(3, 3, 3, 0.15);*/
  z-index: 99;
}

.dots {
  position: absolute;
  left: 0;
  right: 0;
  /*bottom: 20rpx;*/
  margin-top: -20px;
  display: flex;
  justify-content: center;
  z-index: 100;
}

.dots .dot {
  margin: 0 8rpx;
  width: 14rpx;
  height: 14rpx;
  background: #c3c3c3;
  border-radius: 8rpx;
  transition: all .6s;
}

.dots .dot.active {
  /*width: 24rpx;*/
  width: 14rpx;
  background: #fff;
}

.fade_in {
  animation: fadeIn 1.2s both;
}

.fade_null {}
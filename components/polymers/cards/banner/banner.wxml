<block wx:if="{{newData.length>0}}">
  <swiper class="banner-block" style="margin: 0;padding: 0;background-color: '#fff';" previous-margin="0" next-margin="0" circular="true" indicator-color="#ccc" indicator-active-color="rgba(255,255,255,1)" indicator-dots="{{false}}" autoplay="{{true}}" interval="{{15000}}" current="{{swiperCurrent}}" bindchange="swiperChange" duration="{{500}}">
    <block wx:for="{{newData}}" wx:key="index">
      <swiper-item class="swiper-item">
        <image class="h100 image-swiper" style="border-radius:30rpx;height: 300rpx;" data-item="{{item}}" id="{{index}}" data-content="{{item.action}}" data-name="{{item.headerTitle}}" data-type="banner" bind:tap="handleClickBanner" src="{{item.pic}}?x-oss-process=image/resize,m_fill,h_308,w_670" mode="aspectFill {{imgAnimate}}" />
      </swiper-item>
    </block>
  </swiper>

  <view class="dots" wx:if="{{data.length > 1}}">
    <block wx:for="{{data}}" wx:key="unique">
      <view data-i='{{index}}' bindtap='fn' class="dot{{index == swiperCurrent ? ' active' : ''}}" />
    </block>
  </view>
</block>
import { enums, eventName, global, md5, storage, util, vLog } from "../../../../common/index";
import { checkLogin, getAdvStaffCardInfo } from "../../../../common/nb/home";
import {
  getUnionID,
  getLoadingMoment,
  getOpenId,
  getUser,
  setUser,
  setUserRole,
  setSaveUserInfo,
  setUserLogin,
  getUserLogin,
  getCustomerTypeInt,
  setToken,
  setUserId
} from "../../../../common/utils/userStorage";

const { CARD_SURROUND, DEV_PHONE } = enums
const { REFRESH_PAGE_DATA } = eventName
const { isEmptyObject } = util

const app = getApp()

Component({
  properties: {
    data: {
      type: Object,
      value: {},
      observer: 'onHandleRefresh'
    },
    isHeaderBlock: {
      type: Boolean,
      value: false
    },
    hasLogin: {
      type: Boolean,
      value: false
    },
    cardId: {
      type: String | Number,
      value: ''
    }
  },

  data: {
    list: [],
    titleInfo: {},
    aggRecCardIds: '',
    template: 'template1',
    titleAreaVisible: 1,
    wInfo: {},
    itemInfo: {}
  },

  async attached() {
    const { data = {} } = this.properties
    return this.installCardInfo(data)
  },

  methods: {
    onHandleProfileAction() { },

    async getWechatUserInfo() {
      const param = {
        unionid: getUnionID(),
        openid: getOpenId(),
        customerType: getCustomerTypeInt()
      }

      const { code: wxcode } = await wx.login()
      const _resCheckLogin = await checkLogin({ code: wxcode, customerType: param.customerType, wechatCode: global.SOURCE_CODE })
        .catch(error => {
          vLog.error('PROFILE doCheckLogin catch error >>>', error).report()
        })
      if (_resCheckLogin && _resCheckLogin?.success) {
        const { code, data } = _resCheckLogin
        setUserRole(code * 1)
        setUserLogin(true)
        setSaveUserInfo(data)

        if (data?.token) {
          const { token = '', userId = '' } = data || {}
          setToken(token)
          setUserId(userId)
        } else {
          setToken('')
          setUserId('')
        }
      }

      const { success, data } = await getAdvStaffCardInfo({
        timeStamp: new Date().getTime()
      })
      let _wInfo = getUser()
      if (success) {
        const { orgId = '' } = data || {}
        storage.setStorage('orgId', orgId)

        _wInfo = {
          ..._wInfo,
          ...data
        }
        setUser(_wInfo)
      }

      return _wInfo
    },

    async installCardInfo(params = {}) {
      const { hasLogin, cardId = '' } = this.data || {}
      const {
        finalConf = {},
        aggRecCardIds = '',
        cardName = '',
        id
      } = params || {}

      app.globalData.emitter.on(REFRESH_PAGE_DATA, async (data) => {
        if (!isEmptyObject(data)) {
          const { isRefresh, registerRouter = false } = data || {}
          const {
            titleInfo: {
              titleAreaVisible = 1
            }
          } = this.data

          const hasLogin = getUserLogin()
          if (isRefresh) {
            const wInfo = await this.getWechatUserInfo()

            const itemInfo = {
              wInfo,
              hasLogin,
              signalCard: (titleAreaVisible == 2) || registerRouter
            }

            this.setData({
              itemInfo,
            })
            return true
          }
        }
      })

      let cardInfo = {}
      if (typeof finalConf === 'string') {
        cardInfo = JSON.parse(finalConf)
      }
      if (typeof finalConf === 'object') {
        cardInfo = Object.assign(cardInfo, finalConf)
      }
      let phone = ''

      const wInfo = await this.getWechatUserInfo()
      if (wInfo && Object.keys(wInfo).length) {
        const { phone: nPhone = '' } = wInfo
        phone = nPhone
      }

      let list = []
      const {
        dataSource = {},
        interaction = {},
        subTitle = {},
        title = {},
        template = '',
        area = 1,
        titleAreaVisible = 1,
        imageUrl = '',
      } = cardInfo || {}

      let _cardName = title?.text || ''
      if (!isEmptyObject(dataSource)) {
        const { data } = dataSource || {}
        list = [].concat(data)
        list = list.map((item) => {
          return {
            ...item,
            cName: _cardName,
            cId: cardId,
          }
        })
      }

      const titleInfo = {
        interaction,
        subTitle,
        title,
        template,
        cardType: cardName,
        hasIcon: imageUrl,
        cardSurround: CARD_SURROUND[area],
        titleAreaVisible,
        contentHasScrollTab: true
      }

      const itemInfo = {
        wInfo: { ...wInfo, headerTitle: titleInfo.title.text, cardId: id },
        hasLogin,
        signalCard: titleAreaVisible == 2,
        showVersion: DEV_PHONE.includes(`${phone}`)
      }

      this.setData({
        list,
        aggRecCardIds,
        template: template || 'template1',
        titleInfo,
        titleAreaVisible,
        wInfo,
        itemInfo
      })
    },

    onHandleRefresh(newVal = {}, oldVal = {}) {
      const { finalConf: newFC = '' } = newVal
      const { finalConf: oldFC = '' } = oldVal

      const diffVal = md5.hexMD5(JSON.stringify(newFC)) === md5.hexMD5(JSON.stringify(oldFC))
      if (!diffVal && getLoadingMoment()) {
        return this.installCardInfo(newVal)
      }
    }
  }
});

import { getVersionInfo } from "../../../../../../common/utils/userStorage";

function isEmptyObject(obj = {}) {
  return Object.keys(obj).length === 0;
}

Component({
  properties: {
    data: {
      type: Object,
      value: {},
      observer: 'onHandleRefresh'
    },
    showArrow: {
      type: Boolean,
      value: false
    },
    isSignalCard: {
      type: Boolean,
      value: false
    },
    showVersion: {
      type: <PERSON><PERSON><PERSON>,
      value: false
    },
    hasLogin: {
      type: <PERSON>olean,
      value: false
    },
    currRole: {
      type: String,
      value: '切换角色'
    }
  },

  data: {
    version: '',
    envVersion: '',
    appId: ''
  },

  attached() {
    // console.log('========= PROFILE Card this.properties >>>>', this.properties)
    const { showVersion = false } = this.properties || {}
    if (showVersion) {
      return this.getCurrentVersion()
    }
  },

  methods: {
    onProfileClick(e) {
      const { hasLogin } = this.data || {}
      let {
        currentTarget: {
          dataset: {
            item = {}
          }
        }
      } = e || {}

      item = {
        ...item,
        hasLogin
      }

      //提供给事件监听函数
      this.triggerEvent('onProfileAction', item, { bubbles: true, composed: true })
    },

    installCardInfo(data = {}) {
      if (!isEmptyObject(data)) {
        this.setData({
          data
        })
      }
    },

    getCurrentVersion() {
      const _versionInfo = getVersionInfo()
      if (_versionInfo && !isEmptyObject(_versionInfo)) {
        const { appId = '', envVersion = '', version = '' } = _versionInfo
        this.setData({
          version,
          envVersion,
          appId,
        })
      }
    },

    onCopyInfo() {
      // console.log('===== onCopyInfo e >>>', e)
      const { version, envVersion, appId, } = this.data || {}
      const { data } = this.properties || {}
      const copyInfo = {
        version,
        envVersion,
        appId,
        ...data,
      }

      return wx.setClipboardData({
        data: JSON.stringify(copyInfo),
        success() {
          // console.log('==== res >>>', res)
        }
      })
    },

    onChangeRole(e) {
      console.log('========= onChangeRole e >>>', e)
      const item = {
        type: 'CHANGE_ROLE'
      }
      //提供给事件监听函数
      this.triggerEvent('onChangeRole', item, { bubbles: true, composed: true })
    },

    onHandleRefresh(newVal = {}) {
      return this.installCardInfo(newVal)
    }
  }
});

.profile-card1-block{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    max-height: 30vw;
    background-color: #fff;
    border-radius: 12rpx;
    padding: 28rpx 40rpx;
}

.profile-avatar{
    width: 104rpx;
    height: 104rpx;
    border-radius: 52rpx;
    border: 1rpx solid #FAFAFA;
}

.profile-card1-content{
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    flex: 1;
    justify-content: center;
    margin-left: 28rpx;
}

.profile-card1-content .profile-name{
    font-size: 16Px;
    color: #333;
    font-weight: 500;
    font-family: PingFangSC-Medium;
}

.profile-card1-content .profile-channel-name{
    font-size: 11Px;
    color: #666;
    font-family: PingFangSC-Regular;
    margin-top: 12rpx;
    padding-right: 20px;

    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box!important;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
}

.profile-choose-block{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;
    min-width: 15vw;
    height: 100%;

    font-size: 14Px;
    color: #464646;
}

.choose-icon{
    margin-left: 4rpx;
    margin-bottom: -8rpx;
}

.profile-icon{
    margin-bottom: -4rpx;
}

.version-block{
    display: flex;
    flex-direction: column;
    width: 100%;
    background-color: #fff;
    border-bottom-left-radius: 12rpx;
    border-bottom-right-radius: 12rpx;
    padding: 12rpx;
    margin-top: -12rpx;
}

.version-tips{
    font-size: 12Px;
    color: #ececec;
    font-weight: 400;
    margin-top: 6rpx;
    font-family: PingFangSC-Regular;
}

<nb-card wx:if="{{list.length>0}}" isGridCard="{{true}}">
  <titleBar data="{{titleInfo}}" isHeaderBlock="{{isHeaderBlock}}" />

  <template is="{{template}}" data="{{itemInfo}}">
  </template>
</nb-card>

<!--产品 1行3列-->
<template name="template1" data="{{itemInfo}}">
  <profile1  data="{{itemInfo.wInfo}}" isSignalCard="{{itemInfo.signalCard}}" showVersion="{{itemInfo.showVersion}}" hasLogin="{{itemInfo.hasLogin}}" bind:onProfileAction="onHandleProfileAction" />
</template>
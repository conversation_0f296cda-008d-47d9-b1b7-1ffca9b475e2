import {util, md5} from "../../../../common/index.js";
import {getLoadingMoment, getViolationsPhone} from "../../../../common/utils/userStorage";
import * as enums from "../../../../common/const/enum";

const {CARD_SURROUND, REVIEW_PHONE} = enums
const reviewFilter = ['live', 'multimedia']
const {isEmptyObject} = util

Component({
  properties: {
    data: {
      type: Object,
      value: {},
      observer: 'onHandleRefresh'
    },
    isHeaderBlock: {
      type: Boolean,
      value: false
    },
    cardId: {
      type: String | Number,
      value: ''
    }
  },
  data: {
    TabCur: 0,  // 一级分类idx
    contentInfo: [],
    titleInfo: {},
    currList: [],
    signalCard: false,
    signalBlock: false,
    showPaddingTop: false,

    showViolations: true,
    hasTitle: true
  },

  attached() {
    const {data = {}} = this.properties || {}

    this.setData({
      showViolations: REVIEW_PHONE.includes(getViolationsPhone())
    }, () => {
      return this.installCardInfo(data)
    })
  },

  methods: {
    // review 过滤
    filterHideCategory(param) {
      let contentRes = []

      for (let i = 0; i < param.length; i++) {
        let {name = '', category = {}} = param[i] || {}
        let {dataList} = category || {}
        if (this.data.showViolations){
          dataList = dataList.filter((fItem) => {
            const {realm = ''} = fItem || {}
            return !(reviewFilter.includes(`${realm}`))
          })
        }

        let _dataList = []
        if (dataList && dataList.length){
          // 排序
          dataList = dataList.sort(function(a, b) {
            return a.ordinal - b.ordinal
          })

          dataList.forEach((dItem, dIndex) => {
            const props = {
              ...dItem,
              index: dIndex,
              isLast: dIndex === dataList.length - 1
            }

            _dataList.push(props)
          })
        }

        const filterProps = {
          name,
          dataList: _dataList || dataList
        }
        contentRes.push(filterProps)
      }

      return contentRes
    },

    /**
     * 过滤tab
     */
    filterRankTab(list) {
      return list.filter((item) => {
        const {
          category: {
            dataList,
          }
        } = item || {}

        return dataList && dataList.length
      })
    },

    // 切换一级 tab
    tabSelect(e = {}) {
      const {contentInfo} = this.data || {}
      const {index = 0} = e.detail || {}

      this.data.currList = contentInfo[index].dataList
      this.setData({
        TabCur: index,
        currList: this.data.currList
      })
    },

    installCardInfo(data = {}) {
      const {cardName = '', aggRecCardIds = '', finalConf = {}} = data
      const {cardId, TabCur = 0} = this.data || {}
      let cardInfo = {}
      let hasTitle = true
      if (typeof finalConf === 'string'){
        cardInfo = JSON.parse(finalConf)
      }
      if (typeof finalConf === 'object'){
        cardInfo = Object.assign(cardInfo, finalConf)
      }
      let list = []
      const {
        dataSource = {},
        interaction = {},
        subTitle = {},
        title = {},
        template = '',
        titleAreaVisible = 1,
        area = 1,
        imageUrl = ''
      } = cardInfo || {}

      let _cardName = title?.text || ''
      if (!isEmptyObject(dataSource)){
        const {data = []} = dataSource || {}
        list = [].concat(data)
      }
      let currList = []

      list = this.filterRankTab(list)
      let contentInfo = this.filterHideCategory(list)
      let showPaddingTop = contentInfo.length && CARD_SURROUND[area * 1]
      if (contentInfo && contentInfo.length){
        currList = contentInfo[this.data.TabCur].dataList || []

        currList = currList.map((item, index) => {
          return {
            ...item,
            cName: _cardName,
            cId: cardId,
            mName: contentInfo[TabCur]?.name || '',
          }
        })
      }

      if (titleAreaVisible && titleAreaVisible == 1){
        if (area && area == 2){
          hasTitle = false
        }
      } else {
        hasTitle = false
      }

      const titleInfo = {
        interaction,
        subTitle,
        title,
        template,
        cardType: cardName,
        titleAreaVisible,
        cardSurround: CARD_SURROUND[area * 1],
        hasIcon: imageUrl,
        contentHasScrollTab: true,
      }

      this.setData({
        aggRecCardIds,
        cardName,
        currList,
        cardInfo,
        titleInfo,
        contentInfo,
        signalCard: titleAreaVisible == 2,
        showPaddingTop,
        hasTitle
      })
    },

    // 刷新监听
    onHandleRefresh(newVal = {}, oldVal = {}) {
      const {finalConf: newFC = ''} = newVal
      const {finalConf: oldFC = ''} = oldVal

      const diffVal = md5.hexMD5(JSON.stringify(newFC)) === md5.hexMD5(JSON.stringify(oldFC))
      if (!diffVal && getLoadingMoment()){
        return this.installCardInfo(newVal)
      }
    }
  }
});

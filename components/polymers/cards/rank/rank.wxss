.content-nav{
    width: 100%;
    z-index: 9999;
}

.nav{
    color: #999;
    font-size: 32rpx;
    background-color: #fff;
    white-space: nowrap;
    display: flex;
    flex-direction: row;
    border-bottom: 1px solid #eee;
    border-top-left-radius: 12rpx;
    border-top-right-radius: 12rpx;
}

.nav .current-item{
    display: flex;
    flex: 1;
    flex-direction: column;
    height: 100rpx;
    justify-content: center;
    align-items: center;
    position: relative;
}

.nav .current-item .underline{
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 50rpx;
    height: 4rpx;
}

.content-list{
    display: flex;
    flex-direction: column;
    width: 100%;
    border-top-left-radius: 12rpx;
    border-top-right-radius: 12rpx;
    /*background-color: coral;*/
}

<nb-card wx:if="{{contentInfo && contentInfo.length>0}}" isGridCard="{{true}}">
  <titleBar data="{{titleInfo}}" isHeaderBlock="{{isHeaderBlock}}"/>

  <view class="content-nav" style="margin-top: {{signalCard ? 40 : 0}}rpx">
    <scrollable-tabview
        wx:if="{{contentInfo && contentInfo.length>1}}"
        class=".current-item {{index==TabCur?'text-black':''}}"
        tabs='{{contentInfo}}'
        bind:tabNavClick='tabSelect'
        backgroundColor="#fff"
        activeColor="#333"
        color="#999"
        showTabLine="{{true}}"
        barBgColor="{{$state.themeColor}}"
        currentIndex="{{TabCur}}"
    />
  </view>

  <rankCell
      wx:if="{{currList.length>0}}"
      data="{{currList}}"
      paddingT="{{showPaddingTop}}"
      hasTitle="{{hasTitle}}"
  />
</nb-card>

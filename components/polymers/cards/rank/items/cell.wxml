<view class="content-list" wx:if="{{currList.length>0}}" wx:for="{{currList}}" wx:for-item="item" wx:for-index="id" wx:key="id">
  <view class="cell-item {{item.cellStyle}} {{item.cellBoxStyle}}" data-id="{{item.id}}" id="{{id}}" data-content="{{item.rankId}}" data-name="{{item.name}}" data-type="rankinglist" bind:tap="onCellAction" style="padding-top: {{!item.currentIndex && !paddingT && hasTitle ? 0 : 28}}rpx;border-bottom-color: {{item.isLast?'#fff':'rgba(238, 238, 238, 0.3)'}}">
    <view class="cell-content">
      <view class="title">
        <image wx:if="{{item.showIcon}}" src="{{item.icon}}" mode="aspectFill" class="cell-icon number-block" />
        <view wx:else class="cell-number number-block">
          {{item.currentIndex + 1 + '.'}}
        </view>
        <view wx:if="{{item.name}}">{{item.name || ''}}</view>
      </view>

      <view class="footer">
        <view class="visit-block">{{'共' + item.customizeVisits + '次访问'}}</view>
      </view>
    </view>

    <image wx:if="{{item.cover}}" src="{{item.cover}}?x-oss-process=image/resize,m_mfit,h_300,limit_0/crop,w_400,x_15,g_west" mode="aspectFill" class="cell-image" />

    <image wx:else src="{{CONTENT_COVER[item.realm]+'?x-oss-process=image/resize,m_mfit,h_300,limit_0/crop,w_400,x_15,g_west'}}" mode="aspectFill" class="cell-image" />
  </view>
</view>
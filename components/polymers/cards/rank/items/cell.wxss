.rank-1{
    background-image: linear-gradient(180deg, rgba(255, 255, 255, 0.00) 0%, #FFF4EF 100%);
}

.rank-2{
    background-image: linear-gradient(180deg, rgba(255, 255, 255, 0.00) 0%, #ECF5FF 100%);
}

.rank-3{
    background-image: linear-gradient(180deg, rgba(255, 255, 255, 0.00) 0%, #FBF6EE 100%);
}

.rank-normal{
    background-color: #fff;
}

.content-list{
    display: flex;
    flex-direction: column;
    background-color: #fff;
    border-bottom-right-radius: 8px;
    border-bottom-left-radius: 8px;
}

.cell-item{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    /*padding: 28rpx;*/
    padding-left: 28rpx;
    padding-right: 28rpx;
    padding-bottom: 28rpx;
    border-bottom: 1px solid rgba(238, 238, 238, 0.3);
}

.cell-content{
    display: flex;
    flex-direction: column;
    flex: 1;
    justify-content: space-between;
    min-height: 75px;

}

.cell-content .title{
    display: flex;
    flex-direction: row;
    /*align-items: center;*/
    justify-content: flex-start;

    font-size: 16Px;
    color: #333;
    font-weight: bold;
}

.title-line{
    margin-left: 10rpx;
    margin-right: 10rpx;
}

.title-realmType{
    min-width: 40px;
}

.title-block{
    display: flex;
    flex-direction: row;
    position: absolute;
    white-space: pre-wrap;
    max-width: 450rpx;
    /*white-space*/
}

.cell-icon{
    width: 20px;
    height: 19px;
}

.number-block{
    margin-right: 12rpx;
}

.cell-content .footer{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;

    font-size: 12Px;
    color: #969696;
    font-weight: 400;
}

.visit-block{
    /*margin-left: 12rpx;*/
    margin-left: 2rpx;
}

.cell-image{
    width: 100px;
    height: 75px;
    border-radius: 6px;
    margin-left: 20px;
}

.cell-box{

}

.cell-last{
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
}

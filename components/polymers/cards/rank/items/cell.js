import {
  md5
} from "../../../../../common/index.js";

const RANK_FLOWER = {
  0: {
    icon: "../images/ic_rank_1.png",
    style: 'rank-1'
  },
  1: {
    icon: "../images/ic_rank_2.png",
    style: 'rank-2'
  },
  2: {
    icon: "../images/ic_rank_3.png",
    style: 'rank-3'
  }
}

const RANK_CLUE = [0, 1, 2]

const CONTENT_TYPE = {
  "ARTICLE": '资讯',
  "MARKET_PLAN": '资料',
  "LIVE": '直播',
  "MULTI_COURSE": '课程',
}

const CONTENT_TITLE = {
  "ARTICLE": '篇文章',
  "MARKET_PLAN": '项资料',
  "LIVE": '场直播',
  "MULTI_COURSE": '节课程',
}

const CONTENT_COVER = {
  "ARTICLE": 'https://aim-pic-dev.gffunds.com.cn/image/course/2023-01-03/55c4ba15-8dd6-4bd1-915e-310e806280ef.png',
  "MARKET_PLAN": 'https://aim-pic-dev.gffunds.com.cn/image/course/2023-01-03/cfe802ba-caa0-473c-a6d6-71265b409192.png',
  "LIVE": 'https://aim-pic-dev.gffunds.com.cn/image/course/2023-01-03/21342a5c-a27f-40cd-8104-48cf6e78ba33.png',
  "MULTI_COURSE": 'https://aim-pic-dev.gffunds.com.cn/image/course/2023-01-03/4d07ce7c-001f-40e9-bdd1-33fd36873ded.png',
}

const CONTENT_ACTION = {
  "ARTICLE": 'AppAdvNewsList',
  "MARKET_PLAN": 'AppAdvProductData',
  "LIVE": 'AppAdvLiveList',
  "MULTI_COURSE": 'AppAdvTrainListCom',
}

Component({
  properties: {
    data: {
      type: Object,
      value: {},
      observer: 'onHandleRefresh'
    },
    paddingT: {
      type: Boolean,
      value: false
    },
    cId: {
      type: String | Number,
      value: ''
    },
    cName: {
      type: String,
      value: ''
    },
    mName: {
      type: String,
      value: ''
    },
    hasTitle: {
      type: Boolean,
      value: true
    }
  },
  data: {
    currList: [],
    CONTENT_TITLE,
    CONTENT_COVER
  },

  attached() {
    const {data} = this.properties || {}
    this.initCellItem(data)
  },

  methods: {
    onCellAction(e = {}) {
      const {
        dataset: {
          id = ''
        }
      } = e.currentTarget || {}
      const {currList, cName = '', cId = '', mName = ''} = this.data || {}
      let target = currList.find(item => item && item.id == id)
      const {realm, name} = target || {}
      const passParams = {
        ...target,
        action: `action://share/${CONTENT_ACTION[realm]}?categoryIds=${id}`,
        pageType: realm,
        actionValue: name || '',
        cName,
        cId,
        mName,
      }

      //提供给事件监听函数
      this.triggerEvent('onItemClick', passParams, {bubbles: true, composed: true})
    },

    initCellItem(data = []) {
      let currList = []
      data = data.sort(function(a, b) {
        return a.ordinal - b.ordinal
      })

      data.forEach((item, index) => {
        const {realm = '', isLast} = item || {}
        let showIcon = true
        let realmType = CONTENT_TYPE[realm] || '资讯'
        let icon = RANK_FLOWER[0].icon
        let cellStyle = RANK_FLOWER[0].style
        let currentIndex = index
        let cellBoxStyle = 'cell-box'

        if (RANK_CLUE.includes(index)){
          icon = RANK_FLOWER[index].icon
          cellStyle = RANK_FLOWER[index].style
        }

        if (!RANK_CLUE.includes(index)){
          showIcon = false
          cellStyle = 'rank-normal'
        }

        if (isLast){
          cellBoxStyle = 'cell-last'
        }

        item = {
          ...item,
          realmType,
          icon,
          cellStyle,
          showIcon,
          currentIndex,
          cellBoxStyle
        }

        currList.push(item)
      })

      this.setData({currList})
    },

    // 刷新监听
    onHandleRefresh(newVal = [], oldVal = []) {
      const diffVal = md5.hexMD5(JSON.stringify(newVal)) === md5.hexMD5(JSON.stringify(oldVal))
      if (!diffVal){
        return this.initCellItem(newVal)
      }
    }
  }
});

import {getVersionInfo} from "../../../../common/utils/userStorage";

function isEmptyObject(obj = {}) {
  return Object.keys(obj).length === 0;
}

Component({
  properties: {
    data: {
      type: Object,
      value: {},
    },
  },

  data: {
    version: '',
    envVersion: '',
    appId: '',
    vTime: ''
  },

  attached() {
    this.getCurrentVersion()
  },

  methods: {
    installCardInfo(data = {}) {
      if (!isEmptyObject(data)){
        this.setData({
          data
        })
      }
    },

    getCurrentVersion() {
      const _versionInfo = getVersionInfo()
      if (_versionInfo && !isEmptyObject(_versionInfo)){
        const {appId = '', envVersion = '', version = '', vTime=''} = _versionInfo
        this.setData({
          version,
          envVersion,
          appId,
          vTime
        })
      }
    }
  }
});

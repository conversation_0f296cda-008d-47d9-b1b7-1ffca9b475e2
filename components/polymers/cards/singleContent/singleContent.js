import {breakIn, md5} from "../../../../common/index.js";
import {getLoadingMoment, getViolationsPhone} from "../../../../common/utils/userStorage";
import * as enums from "../../../../common/const/enum";

const {
  REVIEW_PHONE
} = enums

Component({
  properties: {
    data: {
      type: Object,
      value: {},
      observer: 'onHandleRefresh'
    },
    isHeaderBlock: {
      type: Boolean,
      value: false
    },
    cardId: {
      type: String | Number,
      value: ''
    }
  },
  data: {
    TabCur: 0,  // 一级分类idx
    contentInfo: [],
    titleInfo: {},
    currList: [],
    signalCard: false,
    signalBlock: false,

    showViolations: true
  },

  attached() {
    const {data = {}} = this.properties || {}

    this.setData({
      showViolations: REVIEW_PHONE.includes(getViolationsPhone())
    }, () => {
      return this.installCardInfo(data)
    })
  },

  methods: {
    // 切换一级 tab
    tabSelect(e = {}) {
      const {contentInfo} = this.data || {}
      const {index = 0} = e.detail || {}

      this.data.currList = contentInfo[index].dataList
      this.setData({
        TabCur: index,
        currList: this.data.currList
      })
    },

    async installCardInfo(data = {}) {
      const {cardId, TabCur = 0, showViolations} = this.data || {}
      const _resCardInfo = await breakIn({
        name: 'doInstallCardInfo',
        elementName: 'SINGLE_CONTENT',
        data,
        state: {
          TabCur,
          cardId,
          showViolations
        }
      })
      this.setData({..._resCardInfo})
    },

    // 刷新监听
    onHandleRefresh(newVal = {}, oldVal = {}) {
      const {finalConf: newFC = ''} = newVal
      const {finalConf: oldFC = ''} = oldVal

      const diffVal = md5.hexMD5(JSON.stringify(newFC)) === md5.hexMD5(JSON.stringify(oldFC))
      if (!diffVal && getLoadingMoment()){
        return this.installCardInfo(newVal)
      }
    }
  }
});

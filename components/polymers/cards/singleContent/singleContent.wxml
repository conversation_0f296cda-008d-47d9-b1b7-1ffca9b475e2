<nb-card wx:if="{{contentInfo && contentInfo.length>0}}" isGridCard="{{true}}">
  <titleBar data="{{titleInfo}}" isHeaderBlock="{{isHeaderBlock}}"/>

  <view class="content-nav" style="margin-top: {{signalCard ? 40 : 0}}rpx">
    <scrollable-tabview
        wx:if="{{contentInfo && contentInfo.length>1}}"
        class=".current-item {{index==TabCur?'text-black':''}}"
        tabs='{{contentInfo}}'
        bind:tabNavClick='tabSelect'
        backgroundColor="#fff"
        activeColor="#333"
        color="#999"
        showTabLine="{{true}}"
        barBgColor="{{$state.themeColor}}"
        currentIndex="{{TabCur}}"
    />
  </view>

  <view
      class="content-list"
      wx:if="{{currList.length>0}}"
      wx:for="{{currList}}"
      wx:for-item="item"
      wx:for-index="id"
      wx:key="id">

    <news
        wx:if="{{item.realm=='news'}}"
        data="{{item}}"
        cName="{{item.cName}}"
        cId="{{item.cId}}"
        mName="{{item.mName}}"
        isFromCard="true"
        signalBlock="{{true}}"
    />

    <liveItem
        wx:if="{{item.realm=='live'}}"
        data="{{item}}"
        cName="{{item.cName}}"
        cId="{{item.cId}}"
        mName="{{item.mName}}"
        ignalBlock="{{true}}"
    />

    <marketPlan
        wx:if="{{item.realm=='marketPlan'}}"
        data="{{item}}"
        cName="{{item.cName}}"
        cId="{{item.cId}}"
        mName="{{item.mName}}"
        isFromCard="true"
        signalBlock="{{true}}"
    />

    <multimedia
        wx:if="{{item.realm=='multimedia'}}"
        data="{{item}}"
        cName="{{item.cName}}"
        cId="{{item.cId}}"
        mName="{{item.mName}}"
        isFromCard="true"
        signalBlock="{{true}}"
    />
  </view>
  <view wx:if="{{!currList ||!currList.length}}">
    <emptyBlock tips="暂无内容" isContent="{{true}}}" noTabBar="{{!contentInfo || !contentInfo.length}}"/>
    <vertical-space/>
  </view>
</nb-card>

import * as enums from '../../../../common/const/enum.js'
import {breakIn, md5} from "../../../../common/index.js";
import {getLoadingMoment, getViolationsPhone} from "../../../../common/utils/userStorage";

const {
  REVIEW_PHONE
} = enums

// const app = getApp()

Component({
  properties: {
    data: {
      type: Object,
      value: {},
      observer: 'onHandleRefresh'
    },
    isHeaderBlock: {
      type: Boolean,
      value: false
    },
    cardId: {
      type: String | Number,
      value: ''
    }
  },
  data: {
    TabCur: 0,  // 一级分类idx,
    tIdx: 0,
    initNextIdxs: [], // 二级分类idx

    cardInfo: {},
    cardName: '',
    aggRecCardIds: '',
    list: [],
    contentInfo: [],
    titleInfo: {},
    currList: [],
    signalCard: false,
    signalBlock: false,
    showViolations: true
  },

  attached() {
    const {data = {}} = this.properties || {}
    this.setData({
      showViolations: REVIEW_PHONE.includes(getViolationsPhone())
    }, () => {
      return this.installCardInfo(data)
    })
  },

  methods: {
    // 切换一级 tab
    tabSelect(e = {}) {
      const {initNextIdxs, contentInfo, tIdx: _tIdx, TabCur} = this.data || {}
      const {index = 0} = e.detail || {}
      let tIdx = initNextIdxs && initNextIdxs[index] && initNextIdxs[index].idx || 0

      if ((index === TabCur) && (_tIdx === tIdx)){
        return
      }
      if (tIdx !== -1){
        let _currList = contentInfo[index].list[tIdx].dataList
        this.setData({
          TabCur: index,
          currList: _currList
        })
      } else {
        this.setData({
          TabCur: index,
          currList: []
        })
      }
    },

    // 切换二级 tab
    clickCategory(e = {}) {
      const {TabCur = 0, contentInfo} = this.data || {}
      const {index = 0} = e.detail || {}

      this.data.initNextIdxs[TabCur].idx = index
      this.data.currList = contentInfo[TabCur].list[index].dataList
      this.setData({
        initNextIdxs: this.data.initNextIdxs,
        currList: this.data.currList,
        tIdx: index
      })
    },

    async installCardInfo(data = {}) {
      const {TabCur = 0, cardId = '', showViolations = true} = this.data || {}
      const _resCardInfo = await breakIn({
        name: 'doInstallCardInfo',
        elementName: 'CONTENT',
        data,
        state: {
          TabCur,
          cardId,
          showViolations
        }
      })

      this.setData({..._resCardInfo})
    },

    // 刷新监听
    onHandleRefresh(newVal = {}, oldVal = {}) {
      const {finalConf: newFC = ''} = newVal
      const {finalConf: oldFC = ''} = oldVal

      const diffVal = md5.hexMD5(JSON.stringify(newFC)) === md5.hexMD5(JSON.stringify(oldFC))
      if (!diffVal && getLoadingMoment()){
        return this.installCardInfo(newVal)
      }
    }
  },
});

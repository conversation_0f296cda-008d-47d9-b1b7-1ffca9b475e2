<nb-card wx:if="{{list.length>0}}" isGridCard="{{true}}">
  <titleBar data="{{titleInfo}}" isHeaderBlock="{{isHeaderBlock}}"/>
  <view class="content-nav" style="margin-top: {{signalCard ? 40 : 0}}rpx">
    <scrollable-tabview
        wx:if="{{contentInfo && contentInfo.length>1}}"
        class=".current-item {{index==TabCur?'text-black':''}}"
        tabs='{{contentInfo}}'
        bind:tabNavClick='tabSelect'
        backgroundColor="#fff"
        activeColor="#333"
        color="#999"
        showTabLine="{{true}}"
        barBgColor="{{$state.themeColor}}"
        currentIndex="{{TabCur}}"/>
  </view>

  <view class="subNav"
        wx:if="{{initNextIdxs[TabCur].idx!==-1 && contentInfo[TabCur].list && contentInfo[TabCur].list.length>1}}"
        style="border-top-left-radius: {{!(contentInfo && contentInfo.length>1)?12:0}}rpx;border-top-right-radius: {{!(contentInfo && contentInfo.length>1)?12:0}}rpx">
    <!--  <view class="subNav" wx:if="{{initNextIdxs[TabCur].idx!==-1}}">-->
    <category
        tabs="{{contentInfo[TabCur].list}}"
        bind:tabBarClick='clickCategory'
        currentIndex="{{initNextIdxs[TabCur].idx}}"
    />
  </view>

  <view
      class="content-list"
      wx:if="{{currList.length>0}}"
      wx:for="{{currList}}"
      wx:for-item="item"
      wx:for-index="id"
      wx:key="id">
    <template is="{{item.realm}}" data="{{item}}">
    </template>
  </view>
  <view wx:if="{{initNextIdxs[TabCur].idx===-1 || !currList ||!currList.length}}">
    <emptyBlock tips="暂无内容" isContent="{{true}}}"/>
    <vertical-space/>
  </view>
</nb-card>

<template name="news" data="{{item}}">
  <news
      data="{{item}}"
      cName="{{item.cName}}"
      cId="{{item.cId}}"
      mName="{{item.mName}}"
      isFromCard="true"
      signalBlock="{{item.signalBlock}}"
  />
</template>

<template name="live" data="{{item}}">
  <liveItem
      data="{{item}}"
      cName="{{item.cName}}"
      cId="{{item.cId}}"
      mName="{{item.mName}}"
      signalBlock="{{item.signalBlock}}"
  />
</template>

<template name="marketPlan" data="{{item}}">
  <marketPlan
      data="{{item}}"
      cName="{{item.cName}}"
      cId="{{item.cId}}"
      mName="{{item.mName}}"
      isFromCard="true"
      signalBlock="{{item.signalBlock}}"
  />
</template>

<template name="multimedia" data="{{item}}">
  <multimedia
      data="{{item}}"
      cName="{{item.cName}}"
      cId="{{item.cId}}"
      mName="{{item.mName}}"
      isFromCard="true"
      signalBlock="{{item.signalBlock}}"
  />
</template>

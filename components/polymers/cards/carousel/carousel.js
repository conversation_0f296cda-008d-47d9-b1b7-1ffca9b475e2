import { md5, util } from "../../../../common/index";
import { CHANNEL_REALM } from "../../../../common/const/enum";
import { getLoadingMoment } from "../../../../common/utils/userStorage";

const carouselTemp = {
  template1: 'carousel1', // 单图大图
  template3: 'carousel2', // 单图小图
  template5: 'carousel3', // 轮播大图
  template7: 'carousel4', // 双图小图
  template8: 'Banner' // 轮播
}

const fromTabStr = {
  HOME: '首页',
  PRODUCT: '产品',
  ACCOMPANY: '陪伴',
  VIEWPOINT: '观点',
  MINE: '我的'
}

// BI看板模块
const BI_BLOCK = 'AppAdvBiBlock';

const { isEmptyObject } = util

Component({
  properties: {
    data: {
      type: Object,
      value: {},
      observer: 'onHandleRefresh'
    },
    isHeaderBlock: {
      type: Boolean,
      value: false
    },
    cardId: {
      type: String | Number,
      value: ''
    }
  },
  data: {
    list: [],
    carouselInfo: {},
    titleInfo: {},
    aggRecCardIds: '',
    template: '',
    titleAreaVisible: 0,
    itemInfo: {}
  },

  attached() {
    const { data = {} } = this.properties || {}
    return this.installCardInfo(data)
  },

  methods: {
    onHandleAction(e) {
      const { titleInfo } = this.data
      const { detail } = e
      getApp().sensors.track('click_banner', {
        title: titleInfo.title.text,
        url: detail.action,
        bannerindex: detail.index,
        content_category: detail.actionObject?.categoryName,
        content_category_id: detail.actionObject?.categoryId || '',
      })
    },
    onHandleCarouselAction(e = {}) {
      const { detail = {} } = e || {}
      const { actionObject = {} } = detail || {}
      let passProps = {
        ...detail
      }

      if (!isEmptyObject(actionObject)) {
        const { categoryType = '' } = actionObject || {}
        passProps = {
          ...detail,
          pageType: CHANNEL_REALM[categoryType]
        }
      }

      this.triggerEvent('onItemClick', passProps, { bubbles: true, composed: true })
    },

    installCardInfo(data = {}) {
      const { cardName = '', aggRecCardIds = '', finalConf = {}, fromTab } = data || {}
      const { cardId = '' } = this.data || {}
      let cardInfo = {}
      if (typeof finalConf === 'string') {
        cardInfo = JSON.parse(finalConf)
      }
      if (typeof finalConf === 'object') {
        cardInfo = Object.assign(cardInfo, finalConf)
      }

      let list = []
      const {
        dataSource = {},
        interaction = {},
        subTitle = {},
        title = {},
        template = '',
        titleAreaVisible = 1,
        imageUrl = ''
      } = cardInfo || {}
      let _cardName = title?.text || ''
      if (!isEmptyObject(dataSource)) {
        const { data } = dataSource || {}
        list = [].concat(data)
        list = list.map((item) => {
          const { actionValue = '' } = item || {}
          return {
            ...item,
            cName: _cardName,
            cId: cardId,
            headerTitle: title?.text || "",
            isBiBlock: actionValue.includes(BI_BLOCK)
          }
        })
      }

      const titleInfo = {
        interaction,
        subTitle,
        title,
        template: carouselTemp[template],
        cardType: cardName,
        cardSurround: false,
        titleAreaVisible,
        hasIcon: imageUrl,
      }

      const itemInfo = {
        list,
        signalCard: titleAreaVisible == 2
      }

      /**
       * 单图BI看板调整
       */
      if (template === 'template1') {
        const { data: tData = [] } = dataSource || {}
        const { actionValue = '' } = tData && tData[0] || {}
        if (actionValue.includes('AppAdvBiBlock') || actionValue.includes('AppAdvActivity')) {
          itemInfo.signalCard = false
        }
      }

      this.setData({
        titleInfo,
        list,
        template: carouselTemp[template],
        aggRecCardIds,
        carouselInfo: cardInfo,
        itemInfo,
        fromTab,
      })
    },

    onHandleRefresh(newVal = {}, oldVal = {}) {
      const { finalConf: newFC = '' } = newVal
      const { finalConf: oldFC = '' } = oldVal
      const diffVal = md5.hexMD5(JSON.stringify(newFC)) === md5.hexMD5(JSON.stringify(oldFC))
      if (!diffVal && getLoadingMoment()) {
        return this.installCardInfo(newVal)
      }
    }
  }
});

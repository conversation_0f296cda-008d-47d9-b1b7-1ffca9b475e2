<block wx:if="{{list && list.length}}">
  <view class="carousel-template2">
    <view class="carousel-temp2-item"
          wx:for="{{list}}"
          wx:key="index"
          id="{{index}}"
          data-content="{{item.action}}"
            data-name="{{item.headerTitle}}"
            data-type="banner"
          wx:for-item="item"
          wx:for-index="index"
          data-item="{{item}}"
          bind:tap="onCarouselClick">
      <image class="carousel-item {{imgAnimate}}" src="{{item.pic}}" mode="aspectFill"/>
    </view>
  </view>
</block>

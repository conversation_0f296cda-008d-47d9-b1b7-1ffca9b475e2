<block wx:if="{{list && list.length}}">
  <view class="carousel-template4">
    <view class="carousel-temp4-item" wx:for="{{list}}" wx:key="index" wx:for-item="item" wx:for-index="index" id="{{index}}" data-content="{{item.action}}" data-name="{{item.headerTitle}}" data-type="banner" data-item="{{item}}" bind:tap="onCarouselClick">
      <image class="carousel-item {{imgAnimate}}" src="{{item.pic}}" mode="widthFix" />
    </view>
  </view>
</block>
<block wx:if="{{list && list.length}}">
  <scroll-view class="carousel-temp3-block" scroll-x scroll-with-animation="true" bind:scroll="tempScroll" style="width: 100%; margin-top: {{isSignalCard?28:0}}rpx;" scroll-into-view="{{intoView}}">
    <view class="carousel-template3" style="margin-right: {{idx===list.length-1?0:30}}rpx" wx:key="idx" wx:for-index="idx" wx:for-item="item" wx:for="{{list}}">
      <image class="temp3-image {{imgAnimate}}" src="{{item.pic}}" mode="heightFix" id="{{index}}" data-content="{{item.action}}" data-name="{{item.headerTitle}}" data-type="banner" data-item="{{item}}" bindtap="onCarouselClick" />
    </view>
  </scroll-view>
</block>
import { global, md5, storage } from "../../../../../../common/index.js";

Component({
  properties: {
    data: {
      type: Array || Object,
      value: [],
      observer: 'onHandleRefresh'
    },
    intoView: {
      type: String,
      value: 'poster0',
    },
    isSignalCard: {
      type: Boolean,
      value: false
    }
  },
  data: {
    list: [],
    imgAnimate: 'fade_null',
  },

  attached() {
    // console.log('========= TEMP3 this.properties', this.properties)
    const { data = [] } = this.properties || {}

    let hashPool = storage.getStorage(global.STORAGE_GLOBAL_HASH_POOL_LIST)
    if (!hashPool) {
      hashPool = []
    }

    let tempId3 = md5.hexMD5(JSON.stringify(data))
    // console.log('======== hashPool >>>>', !hashPool.includes(tempId3))
    if (!hashPool.includes(tempId3) && !!tempId3) {
      this.setData({
        imgAnimate: 'fade_in',
      })
      hashPool.push(tempId3)
      storage.setStorage(global.STORAGE_GLOBAL_HASH_POOL_LIST, hashPool)
    }

    this.setData({
      list: data,
    })
  },

  methods: {
    onCarouselClick(e) {
      // console.log('====== onCarouselClick e >>>', e)
      const {
        currentTarget: {
          dataset: {
            item = {}
          }
        }
      } = e || {}


      //提供给事件监听函数
      this.triggerEvent('onCarouselAction', item, { bubbles: true, composed: true })
    },

    installCardInfo(list = []) {
      if (list && list.length) {
        this.setData({
          list
        })
      }
    },

    onHandleRefresh(newVal = []) {
      return this.installCardInfo(newVal)
    },

    tempScroll() {

    }
  }
});

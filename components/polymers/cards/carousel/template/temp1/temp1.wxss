@keyframes fadeIn {
    0% {opacity: 0;}
    100% {opacity: 1;}
}

.carousel-template1{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: 100%;
    border-radius: 0;
}

.carousel-temp1-item{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: 100%;
    border-radius: 8rpx;
}

.carousel-item{
    width: 100%;
}

.fade_in {
    animation: fadeIn 1.2s both;
}

.fade_null{

}

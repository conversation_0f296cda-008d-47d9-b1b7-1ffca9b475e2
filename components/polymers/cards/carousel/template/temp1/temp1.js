import { global, md5, storage } from "../../../../../../common/index";

Component({
  properties: {
    data: {
      type: Array || Object,
      value: [],
      observer: 'onHandleRefresh'
    },
    isSignalCard: {
      type: Boolean,
      value: false
    }
  },
  data: {
    list: [],
    imgAnimate: 'fade_null',
  },

  attached() {
    // console.log('========= TEMP1 this.properties', this.properties)
    const { data = [] } = this.properties || {}

    let hashPool = storage.getStorage(global.STORAGE_GLOBAL_HASH_POOL_LIST)
    if (!hashPool) {
      hashPool = []
    }

    let tempId1 = md5.hexMD5(JSON.stringify(data))
    // console.log('======== hashPool >>>>', !hashPool.includes(tempId1))
    if (!hashPool.includes(tempId1) && !!tempId1) {
      this.setData({
        imgAnimate: 'fade_in',
      })
      hashPool.push(tempId1)
      storage.setStorage(global.STORAGE_GLOBAL_HASH_POOL_LIST, hashPool)
    }
    this.setData({
      list: data,
    })
  },

  methods: {
    onCarouselClick(e) {
      // console.log('====== onCarouselClick e >>>', e)
      const {
        currentTarget: {
          dataset: {
            item = {}
          }
        }
      } = e || {}

      //提供给事件监听函数
      this.triggerEvent('onCarouselAction', item, { bubbles: true, composed: true })
    },

    installCardInfo(list = []) {
      if (list && list.length) {
        this.setData({
          list
        })
      }
    },

    onHandleRefresh(newVal = []) {
      return this.installCardInfo(newVal)
    }
  }
});

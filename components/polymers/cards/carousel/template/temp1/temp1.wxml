<block wx:if="{{list && list.length}}">
  <view class="carousel-template1">
    <view class="carousel-temp1-item" wx:for="{{list}}" wx:key="index" id="{{index}}" data-content="{{item.action}}" data-name="{{item.headerTitle}}" data-type="banner" wx:for-item="item" wx:for-index="index" data-item="{{item}}" bind:tap="onCarouselClick">
      <image class="carousel-item {{imgAnimate}}" src="{{item.pic}}" style="border-radius:16rpx" mode="widthFix" />
    </view>
  </view>
</block>
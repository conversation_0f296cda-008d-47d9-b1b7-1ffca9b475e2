<view wx:if="{{list.length>0}}" class="carousel-card {{template=='Banner'?'banner_line':''}}" style="margin-left: {{template=='Banner'?0:28}}rpx;margin-right: {{template=='Banner'?0:28}}rpx">
  <titleBar data="{{titleInfo}}" noNbCard="{{template=='Banner'}}" />

  <template is="{{template}}" data="{{itemInfo}}">
  </template>

</view>

<template name="carousel1" data="{{itemInfo}}">
  <temp1 data="{{itemInfo.list}}" isSignalCard="{{itemInfo.signalCard}}" bind:onCarouselAction="onHandleCarouselAction" />
</template>

<template name="carousel2" data="{{itemInfo}}">
  <temp2 data="{{itemInfo.list}}" isSignalCard="{{itemInfo.signalCard}}" bind:onCarouselAction="onHandleCarouselAction" />
</template>

<template name="carousel3" data="{{itemInfo}}">
  <temp3 data="{{itemInfo.list}}" isSignalCard="{{itemInfo.signalCard}}" bind:onCarouselAction="onHandleCarouselAction" />
</template>

<template name="carousel4" data="{{itemInfo}}">
  <temp4 data="{{itemInfo.list}}" isSignalCard="{{itemInfo.signalCard}}" bind:onCarouselAction="onHandleCarouselAction" />
</template>

<template name="Banner" data="{{itemInfo}}">
  <banner data="{{itemInfo.list}}" isSignalCard="{{itemInfo.signalCard}}" bind:onItemClick="onHandleAction" />
</template>
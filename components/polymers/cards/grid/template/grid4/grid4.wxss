.grid-template2 {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  /*justify-content: space-between;*/
  /*justify-content: space-around;*/
  width: 100%;
  border-radius: 8rpx;
  padding: 12rpx;
  background-color: #fff;
}

.grid-temp2-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 20.5vw;
  margin: 14rpx calc((12vw - 52rpx) / 6);
  /*height: 18vw;*/
}

.grid-item4-block {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.grid-item-txt {
  /*width: 10vw;*/
  font-size: 14Px;
  /*font-weight: bold;*/
  text-overflow: ellipsis;
  display: -webkit-box;
  /** 对象作为伸缩盒子模型显示 **/
  -webkit-box-orient: vertical;
  /** 设置或检索伸缩盒对象的子元素的排列方式 **/
  /*-webkit-line-clamp: 2; !** 显示的行数 **!*/
  overflow: hidden;
  /** 隐藏超出的内容 **/
  /*font-size: 40rpx;*/
  /*font-family: PingFangSC-Medium;*/
}

.grid-item-icon {
  width: 84rpx;
  height: 84rpx;
  margin-bottom: 10rpx;
  border-radius: 8rpx;
}
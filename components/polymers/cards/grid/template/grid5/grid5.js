Component({
  properties: {
    data: {
      type: Array || Object,
      value: [],
      observer: 'onHandleRefresh'
    },
    isSignalCard: {
      type: Boolean,
      value: false
    }
  },
  data: {
    list: []
  },

  attached() {
    const { data = [] } = this.properties || {}
    this.setData({
      list: data
    })
  },

  methods: {
    onGridClick(e) {
      const {
        currentTarget: {
          dataset: {
            item = {}
          }
        }
      } = e || {}

      //提供给事件监听函数
      this.triggerEvent('onItemClick', item, { bubbles: true, composed: true })
    },

    installCardInfo(list = []) {
      if (list && list.length) {
        this.setData({
          list
        })
      }
    },

    onHandleRefresh(newVal = []) {
      return this.installCardInfo(newVal)
    }
  }
});

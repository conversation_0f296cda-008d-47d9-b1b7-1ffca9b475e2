.grid-template3 {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  width: 100%;
  border-radius: 8rpx;
  padding: 12rpx;
  background-color: #fff;
}

.grid-temp3-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 12rpx 0;
  width: 16vw;
  /*background-color: #ededed;*/
  margin: 14rpx calc((12vw - 48rpx) / 6);
  /*height: 18vw;*/
  border-radius: 10rpx;
}

.grid-item-txt {
  /*width: 10vw;*/
  max-width: 16vw;
  font-size: 12Px;
  /*font-weight: bold;*/
  text-overflow: ellipsis;
  display: -webkit-box;
  /** 对象作为伸缩盒子模型显示 **/
  -webkit-box-orient: vertical;
  /** 设置或检索伸缩盒对象的子元素的排列方式 **/
  -webkit-line-clamp: 1;
  /** 显示的行数 **/
  overflow: hidden;
  /** 隐藏超出的内容 **/
  /*font-family: PingFangSC-Medium;*/
}

.grid-item-icon {
  width: 72rpx;
  height: 72rpx;
  margin-bottom: 10rpx;
  border-radius: 8rpx;
}
<block wx:if="{{list && list.length}}">
  <view class="grid-template3">
    <view class="grid-temp3-item" wx:for="{{list}}" wx:key="index" wx:for-item="item" wx:for-index="index" id="{{index}}" data-content="{{item.action}}" data-name="{{item.headerTitle}}" data-type="navigation" data-item="{{item}}" bind:tap="onGridClick">
      <image class="grid-item-icon" src="{{item.icon}}" mode="aspectFill" />
      <view class="grid-item5-block">
        <text class="grid-item-txt">{{item.title || '-'}}</text>
      </view>
    </view>
  </view>
</block>
<block wx:if="{{list && list.length}}">
  <view class="grid-template1">
    <view class="grid-temp1-item" wx:for="{{list}}" wx:key="index" wx:for-item="item" id="{{index}}" data-content="{{item.action}}" data-name="{{item.headerTitle}}" data-type="navigation" wx:for-index="index" data-item="{{item}}" bind:tap="onGridClick">
      <image class="grid-item-icon" src="{{item.icon}}" mode="aspectFill" />
      <view class="grid-item3-block">
        <text class="grid-item-txt">{{item.title || '-'}}</text>
      </view>
    </view>
  </view>
</block>
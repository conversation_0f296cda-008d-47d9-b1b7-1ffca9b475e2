import { enums, md5, util } from "../../../../common/index.js";
import { getLoadingMoment } from "../../../../common/utils/userStorage";
const { CARD_SURROUND } = enums
const { isEmptyObject } = util

const fromTabStr = {
  HOME: '首页',
  PRODUCT: '产品',
  ACCOMPANY: '陪伴',
  VIEWPOINT: '观点',
  MINE: '我的'
}

Component({
  properties: {
    data: {
      type: Object,
      value: {},
      observer: 'onHandleRefresh'
    },
    isHeaderBlock: {
      type: Boolean,
      value: false
    },
    cardId: {
      type: String | Number,
      value: ''
    }
  },
  data: {
    list: [],
    gridInfo: [],
    titleInfo: {},
    aggRecCardIds: '',
    template: '',
    itemInfo: {}
  },

  attached() {
    const { data = {} } = this.properties || {}
    return this.installCardInfo(data)
  },

  methods: {
    onHandleGridAction(e) {
      // const { fromTab } = this.data
      const { detail } = e || {}
      getApp().sensors.track('tool', {
        title: detail.title,
        url: detail.action,
        content_category: detail.actionObject?.categoryName,
        content_category_id: detail.actionObject?.categoryId || '',
      })
    },

    installCardInfo(params = {}) {
      const { cardName = '', aggRecCardIds = '', finalConf = {}, fromTab } = params || {}
      const { cardId = '' } = this.data || {}
      let cardInfo = {}
      if (typeof finalConf === 'string') {
        cardInfo = JSON.parse(finalConf)
      }
      if (typeof finalConf === 'object') {
        cardInfo = Object.assign(cardInfo, finalConf)
      }

      let list = []
      const {
        dataSource = {},
        interaction = {},
        subTitle = {},
        title = {},
        template = '',
        area = 1,
        titleAreaVisible = 1,
        imageUrl = ''
      } = cardInfo || {}

      let _cardName = title?.text || ''
      if (!isEmptyObject(dataSource)) {
        const { data } = dataSource || {}
        list = [].concat(data)
        list = list.map((item) => {
          return {
            ...item,
            cName: _cardName,
            cId: cardId,
            headerTitle: title?.text || "",
            mName: item?.title
          }
        })
      }

      const titleInfo = {
        interaction,
        subTitle,
        title,
        template,
        cardType: cardName,
        hasIcon: imageUrl,
        titleAreaVisible,
        cardSurround: CARD_SURROUND[area * 1],
        contentHasScrollTab: true
      }

      const itemInfo = {
        list,
        signalCard: titleAreaVisible == 2
      }

      this.setData({
        list,
        aggRecCardIds,
        titleInfo,
        template,
        gridInfo: cardInfo,
        itemInfo,
        fromTab
      })

    },

    onHandleRefresh(newVal = {}, oldVal = {}) {
      const { finalConf: newFC = '' } = newVal
      const { finalConf: oldFC = '' } = oldVal

      const diffVal = md5.hexMD5(JSON.stringify(newFC)) === md5.hexMD5(JSON.stringify(oldFC))
      if (!diffVal && getLoadingMoment()) {
        return this.installCardInfo(newVal)
      }
    }
  }
});

<nb-card wx:if="{{list.length>0}}" isGridCard="{{true}}">
  <titleBar data="{{titleInfo}}" isHeaderBlock="{{isHeaderBlock}}" />

  <template is="{{template}}" data="{{itemInfo}}">
  </template>

</nb-card>

<!--产品 1行3列-->
<template name="template1" data="{{itemInfo}}">
  <grid3 data="{{itemInfo.list}}" isSignalCard="{{itemInfo.signalCard}}" bind:onItemClick="onHandleGridAction" />
</template>

<!--产品 1行4列-->
<template name="template2" data="{{itemInfo}}">
  <grid4 data="{{itemInfo.list}}" isSignalCard="{{itemInfo.signalCard}}" bind:onItemClick="onHandleGridAction" />
</template>

<!--产品 1行5列-->
<template name="template3" data="{{itemInfo}}">
  <grid5 data="{{itemInfo.list}}" isSignalCard="{{itemInfo.signalCard}}" bind:onItemClick="onHandleGridAction" />
</template>
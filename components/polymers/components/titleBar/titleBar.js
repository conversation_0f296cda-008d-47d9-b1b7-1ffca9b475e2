import {enums} from "../../../../common/index.js";

const {CARD_SURROUND} = enums

Component({
  properties: {
    data: {
      type: Object,
      value: {},
      observer: 'onHandleRefresh'
    },
    noNbCard: {
      type: Boolean,
      value: false
    },
    isHeaderBlock: {
      type: Boolean,
      value: false
    }
  },
  data: {
    title: '',
    subTitle: '',
    moreTxt: '',
    icon: '',
    next: '',
    cardSurround: false,
    hasIcon: false,
    visibleTitleBar: false,
    contentHasScrollTab: false,
    contentHasSubNav: false,

    insideClazz: 'origin-margin'
  },

  attached() {
    const {data = {}} = this.properties || {}
    let insideClazz = 'origin-margin'
    let {
      cardType = '',
      cardSurround, // 真=> 卡片内 假=> 卡片外
    } = data || {}

    if (cardSurround){
      switch (cardType) {
        case 'Posters':
          insideClazz = 'less-margin'
          break

        default:
          break
      }
    }
    this.setData({
      insideClazz,
    })

    return this.installCardInfo(data)
  },

  methods: {
    onMoreAction(e={}) {
      const {
        dataset: {
          info
        }
      } = e.currentTarget || {}

      const {interaction = {}} = info
      const passInfo = {
        ...info,
        ...interaction
      }

      //提供给事件监听函数 bubbles: true, composed: true ===> 绑定穿透
      this.triggerEvent('onItemClick', passInfo, {bubbles: true, composed: true})
    },

    installCardInfo(data = {}) {
      let {
        cardType = '',
        interaction: {
          text: moreTxt = '',
          action,
        },
        subTitle: {
          text: subTitle
        },
        title: {
          text: title
        },
        cardSurround = false,
        hasIcon = false,
        titleAreaVisible = 1,
        contentHasScrollTab = false,
        contentHasSubNav = false
      } = data || {}

      if (cardType === 'Posters'){
        contentHasScrollTab = false
      }

      this.setData({
        title,
        subTitle,
        moreTxt,
        cardType,
        action,
        cardSurround,
        hasIcon,
        visibleTitleBar: CARD_SURROUND[titleAreaVisible * 1],
        contentHasScrollTab,
        contentHasSubNav
      })
    },

    onHandleRefresh(newVal = {}) {
      return this.installCardInfo(newVal)
    }
  }
});

<!--卡片内-->
<block wx:if="{{cardSurround}}">
  <view class="title-block {{insideClazz}}"
        wx:if="{{visibleTitleBar}}"
        style="margin-bottom: {{contentHasScrollTab?-12:0}}px;">
    <view class="title-left-block">
      <block wx:if="{{hasIcon}}">
        <image class="title-icon" src="{{hasIcon}}" mode="aspectFill"></image>
      </block>

      <text class="title-txt" style="margin-left:{{hasIcon?10:0}}rpx;">{{title || ''}}</text>
      <text wx:if="{{subTitle}}" class="subTitle-txt">{{subTitle || ''}}</text>
    </view>

    <view
        class="title-right-block"
        bind:tap="onMoreAction"
        data-info="{{data}}"
        wx:if="{{!!moreTxt}}">
      <text class="more-txt">{{moreTxt || '全部'}}</text>
      <van-icon size="24rpx" color="#999" class="title-next-icon" name="arrow"/>
    </view>
  </view>
</block>

    <!--卡片外-->
<block wx:else>
  <block wx:if="{{contentHasScrollTab}}">
    <view
        class="float-title-block"
        wx:if="{{visibleTitleBar}}"
        style="margin-top: {{isHeaderBlock?40:20}}rpx">
      <view class="title-left-block"
            style="margin-left:{{noNbCard?28:0}}rpx;margin-right:{{noNbCard?28:0}}rpx;">
        <block wx:if="{{hasIcon}}">
          <image class="title-icon" src="{{hasIcon}}" mode="aspectFill"></image>
        </block>

        <text class="title-txt" style="margin-left:{{hasIcon?10:0}}rpx;">{{title || ''}}</text>
        <text wx:if="{{subTitle}}" class="subTitle-txt">{{subTitle || ''}}</text>
      </view>

      <view class="title-right-block" bind:tap="onMoreAction" data-info="{{data}}" wx:if="{{!!moreTxt}}">
        <text class="more-txt">{{moreTxt || '全部'}}</text>
        <van-icon size="24rpx" color="#999" class="title-next-icon" name="arrow"/>
      </view>
    </view>
  </block>

  <block wx:else>
    <view
        class="float-title-block"
        wx:if="{{visibleTitleBar}}"
        style="margin-bottom: {{noNbCard?0:28}}rpx;margin-top: {{isHeaderBlock?40:20}}rpx">
      <view class="title-left-block"
            style="margin-left:{{noNbCard?28:0}}rpx;margin-right:{{noNbCard?28:0}}rpx;">
        <block wx:if="{{hasIcon}}">
          <image class="title-icon" src="{{hasIcon}}" mode="aspectFill"></image>
        </block>

        <text class="title-txt" style="margin-left:{{hasIcon?10:0}}rpx;">{{title || ''}}</text>
        <text wx:if="{{subTitle}}" class="subTitle-txt">{{subTitle || ''}}</text>
      </view>

      <view
          class="title-right-block"
          style="margin-right: {{noNbCard?28:0}}rpx"
          bind:tap="onMoreAction"
          data-info="{{data}}"
          wx:if="{{!!moreTxt}}">
        <text class="more-txt">{{moreTxt || '全部'}}</text>
        <van-icon size="24rpx" color="#999" class="title-next-icon" name="arrow"/>
      </view>
    </view>
  </block>
</block>

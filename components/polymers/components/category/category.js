Component({
  properties: {
    tabs: {
      type: Array,
      value: []
    },
    currentIndex: {
      type: Number,
      value: 0
    },
    intoView: {
      type: String,
      value: 'tabbar0',
    },
    color: {
      type: String,
      value: '#999'
    },
    activeColor: {
      type: String,
      value: '#fff'
    },
    showBorder: {
      type: Boolean,
      value: false
    },
    hasMoreTab: {
      type: Boolean,
      value: false
    }
  },
  data: {
    moreOneLine: false,
    insideClazz: ''
  },

  attached() {
    const {intoView, hasMoreTab} = this.properties || {}
    let insideClazz = ''
    if (intoView && `${intoView}`.includes('poster') && hasMoreTab){
      insideClazz = 'club-bar'
    }
    this.setData({insideClazz})
    this.countLineLength()
  },

  methods: {
    clickTab(e = {}) {
      const {intoView: iView} = this.data || {}
      const {
        dataset: {
          index,
          value
        }
      } = e.currentTarget || {}

      this.triggerEvent('tabBarClick', {index, value}, {})
      let intoView = `${iView}`.startsWith('poster') ? 'poster' : 'tabbar'

      this.setData({
        currentIndex: index,
        intoView: `${intoView}${index ? index - 1 : index}`
      })
    },

    countLineLength() {
      const {tabs = []} = this.properties || {}
      let nameLength = ''
      if (tabs && tabs.length){
        tabs.forEach((tab) => {
          const {name} = tab || {}
          nameLength += name
        })

        this.setData({
          moreOneLine: nameLength.length >= 20,
        })
      }
    }
  }
})

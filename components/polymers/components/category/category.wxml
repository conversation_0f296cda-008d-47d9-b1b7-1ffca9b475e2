<view class='container {{insideClazz}}'>
  <scroll-view scroll-into-view="{{intoView}}" scroll-x scroll-with-animation>
    <block wx:for='{{tabs}}' wx:key='index'>
      <view id="tabbar{{index}}"
            class='tab'
            style='font-weight:{{currentIndex == index ? "500": "normal"}};border:{{showBorder?"1rpx solid #f04b28ff":0}};color: {{currentIndex == index ? activeColor : color}};background-color:{{currentIndex == index ? $state.themeColor : "#fff"}};margin-left: {{index===0?28:10}}rpx;margin-right: {{(index===tabs.length-1 && !moreOneLine)?28:10}}rpx'
            bindtap='clickTab'
            data-index='{{index}}'
            data-value="{{item.value ? item.value : index}}">
        {{item.secondName || item.name}}
      </view>
    </block>
  </scroll-view>
</view>

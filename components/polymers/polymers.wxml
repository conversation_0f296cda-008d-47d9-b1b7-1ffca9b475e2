<block wx:if="{{list.length>0}}">
  <view
      class="polymers-block"
      wx:for-index="idx"
      wx:for-item="item"
      wx:for="{{list}}"
      wx:key="idx">

    <template
        is="{{item.cardName}}"
        data="{{item}}">
    </template>
  </view>

  <view class="page-footer"/>
</block>

<template name="Rank" data="{{item}}">
  <rank
      data="{{item}}"
      cardId="{{item.id}}"
      isHeaderBlock="{{!item.cardIndex}}"
      bind:onItemClick="onHandleAction"
  />
</template>

<template name="SingleContent" data="{{item}}">
  <singleContent
      data="{{item}}"
      cardId="{{item.id}}"
      isHeaderBlock="{{!item.cardIndex}}"
      bind:onItemClick="onHandleAction"
  />
</template>

<template name="Content" data="{{item}}">
  <content
      data="{{item}}"
      cardId="{{item.id}}"
      isHeaderBlock="{{!item.cardIndex}}"
      bind:onItemClick="onHandleAction"
  />
</template>

<template name="Posters" data="{{item}}">
  <posters
      data="{{item}}"
      cardId="{{item.id}}"
      isHeaderBlock="{{!item.cardIndex}}"
      bind:onItemClick="onHandleAction"
  />
</template>

<template name="Carousel" data="{{item}}">
  <carousel
      data="{{item}}"
      cardId="{{item.id}}"
      isHeaderBlock="{{!item.cardIndex}}"
      bind:onItemClick="onHandleAction"
  />
</template>

<template name="Grid" data="{{item}}">
  <grid
      data="{{item}}"
      cardId="{{item.id}}"
      isHeaderBlock="{{!item.cardIndex}}"
      bind:onItemClick="onHandleAction"
  />
</template>

<template name="Profile" data="{{item}}">
  <profile
      data="{{item}}"
      cardId="{{item.id}}"
      isHeaderBlock="{{!item.cardIndex}}"
      hasLogin="{{item.hasLogin}}"
      bind:onProfileAction="onProfileAction"
      bind:onItemClick="onHandleAction"
      bind:onChangeRole="onChangeRole"
  />
</template>

<template name="Search" data="{{item}}">
  <search
      data="{{item}}"
      cardId="{{item.id}}"
      bind:onSearchAction="onSearchAction"
  />
</template>

<template name="Version" data="{{item}}">
  <version data="{{item}}"/>
</template>

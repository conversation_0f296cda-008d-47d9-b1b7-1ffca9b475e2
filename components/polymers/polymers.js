import {
  getUnionID,
  getOpenId,
  getToken,
  getWechatInfoId,
  setAppHoldStatus,
  getLoadingMoment,
  getUser,
  getCustomerTypeInt,
  getCurrRoleType,
  getUserLogin,
  getTabBarList,
} from "../../common/utils/userStorage";

import {
  breakIn,
  enums,
  eventName,
  global,
  interaction,
  md5,
  qs,
  storage,
  util,
  vLog,
  wbs
} from "../../common/index.js";

import {
  CHANNEL_REALM,
  EMPLOYEE_LOGIN_STATUS
} from "../../common/const/enum";

import {
  getMarketingPlanInfo,
  employeeCheck,
  getSceneParam
} from "../../common/nb/home";

const {
  RealmType,
  PAGE_LINK_TYPE,
  SEARCH_CHANNEL_EDF,
  CHANNEL_ID
} = enums

const {
  analyzeAction,
  isEmptyObject,
  formatUrlObject
} = util;

const {
  SEND_EVENT_TO_POLYMERS,
} = eventName

const app = getApp()

const PRODUCT_PAGE_TYPE = {
  SCROLL_TAB_VIEW: 'advProductDataDetail', // 分页切换
  SCROLL_ANCHOR_VIEW: 'advProductDataNewDetail' // 瀑布流
}

Component({
  properties: {
    data: {
      type: Object,
      value: {},
      observer: 'onPolymersRefresh'
    },
    fromTab: {
      type: String,
      value: 'HOME'
    }
  },
  data: {
    list: [],
    hasLogin: false,
    clueParams: [],
  },

  attached() {
    vLog.log('POLYMERS attached this.props >>>', this.properties)
    const hasLogin = getUserLogin()
    this.setData({
      hasLogin
    })

    app.globalData.emitter.on(SEND_EVENT_TO_POLYMERS, (e = {}) => {
      vLog.log('POLYMERS SEND_EVENT_TO_POLYMERS e >>>', e)
      if (!isEmptyObject(e)) {
        return this.onHandleAction(e)
      }
    })

    const { data = [] } = this.properties
    return this.installCardInfo(data)
  },

  detached() {
    setAppHoldStatus(false)
  },

  methods: {
    async onHandleAction(e = {}) {
      vLog.log('POLYMERS onHandleAction e,this.data  >>>', e, this.data)
      const { fromTab = '' } = this.properties
      const { hasLogin } = this.data

      const {
        detail: {
          title = '',
          id = '',
          action = '',
          actionObject = {},
          actionValue = '',
          relation = {},
          courseId = '',
          categoryId: _categoryId = '',
          categoryIds: _categoryIds = '',
          uri = '',
          style: pageStyle = 'SCROLL_ANCHOR_VIEW',
          pageType = '',
          cId = '',
          cName = '',
          mName = '',
          fundname = '',
          fundcode = '',
          fundCode = '',
          fundName = '',
          dateValue = '',
          banShare = 0,
          targetTabId: tId = ''
        },
        targetTabId = '',
      } = e
      let _categoryType = ''
      let _planId = ''
      let _pageStyle = pageStyle

      const clueParams = {
        fromTab,
        cId,
        cName,
        mName
      }

      let baseParams = {
        token: getToken(),
        unionid: getUnionID(),
        openid: getOpenId(),
        wechatInfoId: getWechatInfoId(),
        customerType: getCurrRoleType(),
        fromTab,
      }

      let _customType = getCurrRoleType()
      let _clues = app.getCluesInfo(clueParams)
      console.log('POLYMERS _clues >>>', _clues)
      _clues = decodeURIComponent(JSON.stringify(_clues))

      // 跳转外部链接
      if (!isEmptyObject(actionObject)) {
        const {
          url = '',
          linkType = 0,
          categoryType = '',
          pageName = '',
          gatherId = '',
          contentName = '',
          style: aPageStyle = '',
          contentName: planId = ''
        } = actionObject || {}

        _categoryType = categoryType
        if (aPageStyle) {
          _pageStyle = aPageStyle
        }
        if (planId) {
          if (planId.includes('ANCHOR')) {
            _planId = planId.split('_')[0]
          } else {
            _planId = planId
          }
        }

        // 普通外链
        if (linkType === PAGE_LINK_TYPE.WEB_PAGE) {
          if ((url.startsWith('http') || url.startsWith('https'))) {
            // if (!hasLogin) {
            //   const params = {
            //     ...baseParams,
            //     TAG: 'outLinks',
            //     pageType: 'outLinks',
            //     fromTab,
            //     targetPath: '/pages/common/webview/webPage',
            //     url: encodeURIComponent(url),
            //     clueParams: _clues,
            //   }
            //   return breakIn({ name: 'doCheckJumpIn', params })
            // }
            return wx.navigateTo({
              url: "/pages/common/webview/webPage?url=" + encodeURIComponent(url) + `&clueParams=${_clues}`
            })
          }
        } else if (linkType === PAGE_LINK_TYPE.POLYMERS_LIST) {
          // 聚合列表
          if (gatherId) {
            return wx.navigateTo({
              url: `/pages/home/<USER>/list?id=${gatherId}&name=${pageName}&fromTab=${fromTab}&customerType=${_customType}&categoryId=${contentName}`
            })
          }
        }
      }

      const { type = '', analyzeRes = {} } = analyzeAction(action)
      vLog.log('POLYMERS type,analyzeRes >>>', type, analyzeRes).report()
      const { categoryId = '' } = actionObject
      const wInfo = getUser()

      let params = {
        ...baseParams,
        pageType: type,
        banShare,
        cName,
        cId,
      }

      if (_clues) {
        params.clueParams = _clues
      }

      if (categoryId || _categoryId) {
        params.categoryId = categoryId || _categoryId
      }

      if ((targetTabId || tId) && !categoryId) {
        params.categoryId = targetTabId || tId
      }

      let loginParams = {
        TAG: `${type}`,
        fromTab,
        targetPath: '/pages/common/webview/webPage'
      }

      switch (type) {
        // 海报列表
        case "AppAdvPosterList":
        // 直播列表
        case "AppAdvLiveList":
        // 媒体列表
        case "AppAdvTrainListCom":
        //营销列表
        case "AppAdvProductData":
        // 资讯列表
        case "AppAdvNewsList": {
          return wx.navigateTo({
            url: `/pages/home/<USER>/list?type=${RealmType[pageType] || CHANNEL_REALM[_categoryType]}&categoryIds=${analyzeRes.categoryIds || analyzeRes.categoryId || ''}&name=${actionObject.pageName || actionValue}&fromTab=${fromTab}&customerType=${_customType}`
          })
        }

        // 资讯详情
        case "AppAdvNewsDetail": {
          params.perfix = `${wbs.gfH5}/share/${type}`
          const { value = '' } = analyzeRes || {}
          if (id) {
            params.value = id
          }
          if (value) {
            params.value = value
          }

          // if (!hasLogin) {
          //   params = {
          //     ...params,
          //     ...loginParams
          //   }
          //   return breakIn({ name: 'doCheckJumpIn', params })
          // }
          return breakIn({ name: 'doRouteWebPage', params })
        }

        // 定投解套器
        case "advFixedCaster":
        // 盈利概率测算
        case "wechatFixedInvestmentCalculator":
        // 定投策略测算
        case "wechatProfitProbabilityCalculator": {
          params.perfix = `${wbs.gfH5}/share/${type}`
          params.title = title || ''
          if (!hasLogin) {
            params = {
              ...params,
              ...loginParams
            }
            return breakIn({ name: 'doCheckJumpIn', params })
          }
          return breakIn({ name: 'doRouteWebPage', params })
        }

        // 课程详情
        case "AppAdvInvestorTrainCom": {
          params.perfix = `${wbs.gfH5}/share/${type}`
          if (_categoryId || id) {
            params.value = id
            params.shareConfig = true
            params.categoryId = _categoryId || _categoryIds || ''
          }
          if (courseId) {
            const { categoryId, categoryIds = '', shareConfig = true, } = relation || {}
            params.value = courseId
            params.shareConfig = shareConfig
            params.categoryId = categoryId || categoryIds
          }
          const { value = '', shareConfig = true, categoryId = '', categoryIds = '', } = analyzeRes || {}
          if (value) {
            params.value = value
            params.shareConfig = shareConfig
            params.categoryId = categoryId || categoryIds
          }

          // if (!hasLogin) {
          //   params = {
          //     ...params,
          //     ...loginParams
          //   }
          //   return breakIn({ name: 'doCheckJumpIn', params })
          // }
          return breakIn({ name: 'doRouteWebPage', params })
        }

        // (营销资料)产品详情
        case 'advProductDataNewDetail':
        case 'advProductDataDetail': {
          const { value = '' } = analyzeRes || {}
          if (value || id) {
            params.value = value || id
          }

          if (_planId) {
            const { success, param = {} } = await getMarketingPlanInfo({ planId: _planId })
            if (success && !isEmptyObject(param)) {
              const { style = '' } = param || {}
              _pageStyle = style
            }
          }

          params.perfix = `${wbs.gfH5}/share/${PRODUCT_PAGE_TYPE[_pageStyle]}`
          params.title = '营销资料'
          // if (!hasLogin) {
          //   params = {
          //     ...params,
          //     ...loginParams,
          //     TAG: 'advProductData',
          //   }
          //   return breakIn({ name: 'doCheckJumpIn', params })
          // }
          return breakIn({ name: 'doRouteWebPage', params })
        }

        // 基金解读
        case 'advFundScram': {
          params.perfix = `${wbs.gfH5}/share/${type}`
          params = {
            ...params,
            fundname: fundName || fundname || '',
            fundcode: fundCode || fundcode || '',
            dateValue,
            title: fundname || '基金解读'
          }

          if (!hasLogin) {
            params = {
              ...params,
              ...loginParams,
            }
            return breakIn({ name: 'doCheckJumpIn', params })
          }
          return breakIn({ name: 'doRouteWebPage', params })
        }

        // 运作报告详情
        case 'reportDetail': {
          params.perfix = `${wbs.gfH5}/share/advFundOperate`
          params = {
            ...params,
            fundname: fundName || fundname || '',
            fundcode: fundCode || fundcode || '',
            title: fundname || '运作报告'
          }

          if (!hasLogin) {
            params = {
              ...params,
              ...loginParams,
              TAG: 'reportDetail'
            }
            return breakIn({ name: 'doCheckJumpIn', params })
          }
          return breakIn({ name: 'doRouteWebPage', params })
        }

        // 定投海报
        case 'calcPosterMaker': {
          params.perfix = `${wbs.gfH5}/share/${type}`
          if (!hasLogin) {
            params = {
              ...params,
              ...loginParams
            }
            return breakIn({ name: 'doCheckJumpIn', params })
          }
          return breakIn({ name: 'doRouteWebPage', params })
        }

        // 喜报
        case 'calcGladPosterMaker': {
          params.perfix = `${wbs.gfH5}/share/${type}`
          if (!hasLogin) {
            params = {
              ...params,
              ...loginParams
            }
            return breakIn({ name: 'doCheckJumpIn', params })
          }
          return breakIn({ name: 'doRouteWebPage', params })
        }

        // 专题页面
        case 'Topic': {
          const _tParams = formatUrlObject(analyzeRes)
          const { shouldLogin } = _tParams || {}
          const { customerType: followRoleType = '' } = _tParams || {}
          const customerType = getCurrRoleType()
          // 站点不同 专题页403
          if (customerType !== followRoleType) {
            return wx.navigateTo({
              url: `/pages/common/topic/topic?${qs.stringify(_tParams)}`
            })
          }

          if (shouldLogin && !hasLogin) {
            params = {
              ...params,
              ...loginParams,
              ..._tParams,
              targetPath: '/pages/common/topic/topic'
            }
            return breakIn({ name: 'doCheckJumpIn', params })
          }

          return wx.navigateTo({
            url: `/pages/common/topic/topic?${qs.stringify(_tParams)}`
          })
        }

        //TEST
        case "UnionList": {
          return wx.navigateTo({
            url: `/pages/home/<USER>/index?${qs.stringify(analyzeRes)}`
          })
        }

        // 消息触达模拟
        case "tempMsg": {
          return wx.reLaunch({
            url: `/pages/loginAndRegist/startUp/index?${qs.stringify(analyzeRes)}`
          })
        }

        // 企业微信分享好友
        case "shareQY": {
          return wx.navigateTo({
            url: `/pages/common/shareQY/index?${qs.stringify(analyzeRes)}`
          })
        }

        // 内容库
        case "ContentLibrary": {
          return wx.navigateTo({
            url: '/pages/loginAndRegist/startUp/index?contentLibrary=true'
          })
        }

        // 活动中转页
        case "INTRODUCE": {
          return wx.navigateTo({
            url: `/pages/common/introduce/index`
          })
        }

        // AI教练
        case "AI": {
          const { phone = '' } = wInfo || {}
          let auth2ParamsS = {
            phone,
            authed: true
          }

          let auth2ParamsF = {
            phone,
            authed: false
          }

          let authCodeS = md5.hexMD5(`${qs.stringify(auth2ParamsS)}`)
          let authCodeF = md5.hexMD5(`${qs.stringify(auth2ParamsF)}`)
          interaction.showLoading('加载中...')
          const [authResS, authResF] = await Promise.all([
            getSceneParam({ paramKey: `${authCodeS}`.toUpperCase() }),
            getSceneParam({ paramKey: `${authCodeF}`.toUpperCase() })
          ])
          interaction.hideLoading()
          const extraData = {
            token: getToken() || "",
            channel: CHANNEL_ID
          }

          const { success } = authResS || {}
          if (success) {
            return wx.navigateToMiniProgram({
              appId: 'wx0dcc15bfb6b826a8',
              path: `pages/thirdParty/index?${qs.stringify(extraData)}`,
              extraData,
              envVersion: 'release', //体验版本,线上版本('release')
            })
          }
          const { success: Succ } = authResF || {}
          if (Succ) {
            delete extraData.token
            return wx.navigateToMiniProgram({
              appId: 'wx0dcc15bfb6b826a8',
              path: `pages/thirdParty/index?${qs.stringify(extraData)}`,
              extraData,
              envVersion: 'release', //体验版本,线上版本('release')
            })
          }
          return wx.navigateTo({
            url: `/packages-common/pages/common/auth/index`
          })
        }

        // 直播详情
        case "AppAdvLive": {
          params.perfix = `${wbs.gfH5}/share/${type}`
          const { value = '' } = analyzeRes || {}
          if (id) {
            params.id = id
          }
          if (value) {
            params.value = value
          }
          if (categoryId) {
            params.categoryId = categoryId
          }
          params.title = '直播详情'

          // if (!hasLogin) {
          //   params = {
          //     ...params,
          //     ...loginParams
          //   }
          //   return breakIn({ name: 'doCheckJumpIn', params })
          // }
          return breakIn({ name: 'doRouteWebPage', params })
        }

        // 邀请注册
        case "AppAdvRegister": {
          if (!hasLogin) {
            params = {
              ...params,
              ...loginParams,
              TAG: 'AppAdvRegister',
              targetPath: '/packages-user/pages/invite/index'
            }
            return breakIn({ name: 'doCheckJumpIn', params })
          }

          return wx.navigateTo({
            url: `/packages-user/pages/invite/index?fromTab=${fromTab}`,
          })
        }

        // 海报详情
        case "AppAdvPosterInfo": {
          if (uri) {
            params.perfix = `${wbs.gfH5}/share/advBoutiquePosterDetail`
            params.value = id
            params.url = uri
            params.title = '海报详情'
          }

          if (!hasLogin) {
            params = {
              ...params,
              ...loginParams
            }
            return breakIn({ name: 'doCheckJumpIn', params })
          }
          return breakIn({ name: 'doRouteWebPage', params })
        }

        // 营销线索
        case 'AppAdvMarketingClue': {
          params.perfix = `${wbs.gfH5}/share/${type}`
          params.title = '营销线索'

          if (!hasLogin) {
            params = {
              ...params,
              ...loginParams
            }
            return breakIn({ name: 'doCheckJumpIn', params })
          }
          return breakIn({ name: 'doRouteWebPage', params })
        }

        // 智能名片
        case 'AppAdvCard': {
          params.perfix = `${wbs.gfH5}/share/${type}`
          params.title = '智能名片'

          if (!hasLogin) {
            params = {
              ...params,
              ...loginParams
            }
            return breakIn({ name: 'doCheckJumpIn', params })
          }
          return breakIn({ name: 'doRouteWebPage', params })
        }

        // 半屏小程序
        case 'AppAdvOpenMini': {
          const { appId = '', path = '', envVersion = '', openWay = '', id = '' } = analyzeRes || {}
          let _targetPath = '/pages/home/<USER>'
          const tabList = (getApp().globalData.tabList)?.length ? getApp().globalData.tabList : getTabBarList() || []
          if (tabList && tabList.length) {
            let bTarget = tabList.find(item => item && item.tabName == fromTab)
            if (bTarget && !isEmptyObject(bTarget)) {
              _targetPath = bTarget?.tabPath
            }
          }

          if (!hasLogin) {
            params = {
              ...params,
              ...loginParams,
              targetPath: _targetPath || '/pages/mine/mine'
            }
            return breakIn({ name: 'doCheckJumpIn', params })
          }

          if (openWay === 'jump') {
            return wx.navigateToMiniProgram({
              appId,
              shortLink: `${path}`,
              extraData: {
                unionId: getUnionID()
              },
              envVersion,
              complete() {
                setAppHoldStatus(true)
              }
            })
          }

          if (openWay === 'half') {
            return wx.openEmbeddedMiniProgram({
              appId,
              shortLink: `${path}`,
              envVersion,
              extraData: {
                unionId: getUnionID()
              },
              complete() {
                setAppHoldStatus(true)
              }
            })
          }

          if (openWay === 'jumpOthers') {
            return wx.navigateToMiniProgram({
              appId,
              path,
              extraData: {
                id,
              },
              envVersion,
              complete() {
                setAppHoldStatus(true)
              }
            })
          }

          break
        }

        // Bi看板
        case 'AppAdvBiBlock': {
          interaction.showLoading('加载中...')
          const { code, data, success } = await employeeCheck({})
          interaction.hideLoading()
          if (!success) {
            switch (code * 1) {
              case EMPLOYEE_LOGIN_STATUS.EMPLOYEE_NOT_EXIST: {
                let pagePath = getCurrentPages().pop();
                return wx.navigateTo({
                  url: `/packages-user/pages/employees/index?currPagePath=${encodeURIComponent(pagePath?.route)}`
                })
              }

              case EMPLOYEE_LOGIN_STATUS.EMPLOYEE_HAS_DEACTIVATED: {
                return wx.navigateTo({
                  url: '/packages-user/pages/applyFail/applyFail',
                })
              }

              default:
                break
            }
          }

          if (success && code == 0) {
            if (Object.keys(data).length) {
              storage.setStorage(global.STORAGE_GLOBAL_EMPLOYEE_INFO, data)
              const { token } = data || {}
              const { userId = '' } = wInfo || {}

              let params = {
                perfix: `${wbs.gfH5}/share/wxStaffData`,
                title: 'Bi看板',
                token,
                pageType: type,
                banShare: 0,
                userId
              }
              return breakIn({ name: 'doRouteWebPage', params })
            }
          }
          break
        }

        // 邀请有礼
        case 'AppAdvActivity': {
          params = {
            ...params,
            fromTab,
            routerStatus: 'PAGE',
            targetPath: '/pages/loginAndRegist/activity/activity'
          }

          return wx.navigateTo({
            url: `/pages/loginAndRegist/activity/activity?${qs.stringify(params)}`,
          })
        }

        // 定投罗盘
        case 'AppAdvCompass': {
          params = {
            ...params,
            targetPath: '/pages/mine/compass/index'
          }

          if (!hasLogin) {
            params = {
              ...params,
              ...loginParams,
              targetPath: '/pages/mine/compass/index'
            }
            return breakIn({ name: 'doCheckJumpIn', params })
          }

          return wx.navigateTo({
            url: `/pages/mine/compass/index?${qs.stringify(params)}`
          })
        }

        // 搜索列表(基金解读，运作报告，股票，也可传多种类型)
        case 'SearchList': {
          const { channelIds = '', placeholder = '', keyword = '', } = analyzeRes || {}

          const channelArray = JSON.parse(channelIds) || []
          let channelList = []
          if (channelArray && channelArray.length) {
            channelArray.forEach((name) => {
              if (SEARCH_CHANNEL_EDF.hasOwnProperty(name)) {
                const props = {
                  value: name,
                  ...SEARCH_CHANNEL_EDF[name],
                }
                channelList.push(props)
              }
            })
          }
          const searchType = channelList[0]?.type || 'STOCK'

          return wx.navigateTo({
            url: `/packages-common/pages/common/search/search?type=${searchType}&channelList=${JSON.stringify(channelList)}&placeholder=${placeholder}&keyword=${keyword}&fromTab=${fromTab}`,
          })
        }

        // 排行榜列表
        case 'advRankingList': {
          params.perfix = `${wbs.gfH5}/share/${type}`
          if (!hasLogin) {
            params = {
              ...params,
              ...loginParams
            }
            return breakIn({ name: 'doCheckJumpIn', params })
          }
          return breakIn({ name: 'doRouteWebPage', params })
        }

        default:
          break
      }
    },

    /**
     * 搜索响应
     */
    onSearchAction(e = {}) {
      const { fromTab } = this.data
      const { detail = {} } = e
      const { channelIds = [], placeholder } = detail || {}

      let channelList = []
      if (channelIds && channelIds.length) {
        channelIds.forEach((name) => {
          if (SEARCH_CHANNEL_EDF.hasOwnProperty(name)) {
            const props = {
              value: name,
              ...SEARCH_CHANNEL_EDF[name],
            }
            channelList.push(props)
          }
        })
      }
      const type = channelList[0]?.type || 'MARKET_PLAN'
      return wx.navigateTo({
        url: `/packages-common/pages/common/search/search?type=${type}&channelList=${JSON.stringify(channelList)}&placeholder=${placeholder}&fromTab=${fromTab}`,
      })
    },

    /**
     * 个人详情
     */
    async onProfileAction() {
      const { fromTab, hasLogin } = this.data
      let params = {
        token: getToken(),
        openid: getOpenId(),
        unionid: getUnionID(),
        wechatInfoId: getWechatInfoId(),
        pageType: 'AppAdvProfileInfo',
      }
      // 个人名片
      params.perfix = `${wbs.gfH5}/share/AppAdvCard/editPersonCard`

      let loginParams = {
        TAG: 'ProfileAction',
        fromTab,
        targetPath: '/pages/common/webview/webPage'
      }

      if (!hasLogin) {
        params = {
          ...params,
          ...loginParams
        }
        return breakIn({ name: 'doCheckJumpIn', params })
      }
      return breakIn({ name: 'doRouteWebPage', params })
    },

    async onChangeRole(e = {}) {
      const { type = '' } = e.detail
      let staffType = getCustomerTypeInt()

      const roleParams = {
        pageType: 'CHANGE',
        currRole: staffType
      }

      if (type && type == 'CHANGE_ROLE') {
        return wx.navigateTo({
          url: `/packages-user/pages/role/index?${qs.stringify(roleParams)}`,
        })
      }
    },

    installCardInfo(list = []) {
      const { hasLogin } = this.data || {}
      const _list = []
      if (list && list.length) {
        list.forEach((item) => {
          const props = {
            ...item,
            hasLogin
          }
          _list.push(props)
        })
      }

      this.setData({
        list: _list
      })
    },

    onPolymersRefresh(newVal, oldVal) {
      // vLog.log('POLYMERS onPolymersRefresh newVal,oldVal >>>', newVal, oldVal)
      if (newVal && newVal.length && getLoadingMoment()) {
        return this.installCardInfo(newVal)
      }
    }
  }
});

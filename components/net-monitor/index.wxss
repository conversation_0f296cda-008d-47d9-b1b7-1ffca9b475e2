@keyframes fadeIn{
    0%{
        opacity: 0;
    }
    100%{
        opacity: 1;
    }
}

@keyframes fadeOut{
    0%{
        opacity: 1;
    }
    100%{
        opacity: 0;
    }
}

.monitor-page{
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    z-index: 999999;
    width: 100vw;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.65);
}

.monitor-top{
    width: 100vw;
    height: 72vh;
    margin-bottom: -2vh;
}

.monitor-bottom{
    display: flex;
    flex-direction: column;
    width: 100vw;
    height: 30vh;
}

.sheet-title-bar{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;
    width: 100vw;
    border-top-left-radius: 16rpx;
    border-top-right-radius: 16rpx;
    background-color: #fff;
    padding: 16px 30rpx;
}

.sheet-title-txt{
    margin-right: -50vw;
}

.cross-icon{
    margin-bottom: -6rpx;
    margin-left: 36vw;
}

.monitor-content{
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100vw;
    height: 100%;
    background-color: #fff;
}

.action-item{
    display: flex;
    flex-direction: column;
    flex: 1;
    align-items: center;
    margin-bottom: 50rpx;
}

.action-icon{
    width: 90rpx;
    height: 90rpx;
    margin-bottom: 30rpx;
}

.share-btn{
    border: none;
    border-radius: 0;
    font-size: 14Px;
    line-height: normal;
}

.fade_in{
    animation: fadeIn 0.4s both;
}

.fade_out{
    animation: fadeOut 0.2s both;
}

.fade_null{

}

<!--component/nb-page/nb-page.wxml-->
<view class='container {{markStyle}}' style="{{containerBgColor && 'background-color:' + containerBgColor+';'}}">

  <!-- 标题栏 -->
  <navigation
      wx:if="{{showNavBar}}"
      show-loading="{{navShowLoading}}"
      title="{{navTitle}}"
      title-style="{{navTitleStyle}}"
      bg-style="{{navBgStyle}}"
      delta="{{navDelta}}"
      text-style='{{navTextStyle}}'
      show-back="{{navShowBack}}"
      showLeftCustomIcon="{{showLeftCustomIcon}}"
      customLeftIconUrl="{{customLeftIconUrl}}"
      showHomeIcon="{{showHomeIcon}}"
      bind:onPressNavLeft="onPressNavLeft"
  />

  <!--logo-->
  <cover-view
      wx:if="{{inHomePage}}"
      class="header-block"
      style="height: {{titleHeight}}rpx; min-height: {{titleHeight}}rpx">
    <cover-image
        mode="heightFix"
        src="{{logoIcon}}"
        style="height:{{logoHeightSize}}px;margin-bottom: {{navBottomFloat}}px;width: 120px;"
        class="logo-icon"/>
  </cover-view>

  <!-- 内容区域 -->
  <scroll-view
      wx:if="{{!contentUseView}}"
      scroll-y="{{scrollEnable}}"
      class="{{scrollStyle}}"
      enhanced="{{true}}"
      bounces="{{false}}"
      style='height:{{contentHeight}}rpx;margin-top:{{showNavBar?titleHeight:0}}rpx; margin-bottom:{{showTabBar?footHeight:0}}rpx;'
  >
    <slot></slot>
  </scroll-view>

  <!-- 包含canvas等原生组件的内容区域 -->
  <view
      style='height:{{contentHeight}}rpx;margin-top:{{showNavBar?titleHeight:0}}rpx; padding-bottom:{{showTabBar?footHeight:0}}rpx;'
      wx:if="{{contentUseView}}">
    <slot></slot>
  </view>

  <!-- 底部TabBar -->
  <tabbar
      wx:if="{{showTabBar}}"
      currentSelectedType="{{tabBarType}}"
      bind:tabChange="onTabChange"
  />
</view>

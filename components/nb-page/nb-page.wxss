/* component/nb-page/nb-page.wxss */
.container{
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    /*filter: grayscale(100%);*/
}

.normal-page{

}

.able-scroll{

}

.enable-scroll{
    position: fixed;
    left: 0;
}

.commemoration-page{
    filter: grayscale(100%);
}

.header-block-img{
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
}

.header-block{
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    z-index: 99999;
    overflow: hidden;
    background-color: transparent;
}

.logo-icon{
    max-height: 68px;
    margin-left: 20rpx;
    z-index: 99999;
}

.modal-footer{
    display: flex;
    height: 100rpx;
    flex-direction: row;
    align-items: center;
    font-size: 32rpx;
}

.modal-btn{
    width: 50%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
}

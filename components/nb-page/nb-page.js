// component/nb-page/nb-page.js
// 页面通用组件，可将全局UI弹窗放置于此，并且通过store进行全局控制
import {
  titleHeight,
  navigationBarHeight,
  navBottomFloat,
  footHeight,
  bottomHeightPx,
  getContentHeight,
  getRegisterPageHeight
} from '../../common/const/systeminfo.js'

import {
  getMarkDayStatus,
  getTabBarList,
} from "../../common/utils/userStorage"

const pic_logo_fill_hash = 'https://aim-pic-dev.gffunds.com.cn/image/course/2023-02-10/d86f0e07-8f78-457e-94df-12aec3bda966.png'

Component({
  options: {
    addGlobalClass: true,
  },

  /**
   * 组件的属性列表
   */
  properties: {
    // container背景色
    containerBgColor: {
      type: String,
      value: '#f7f7f7' // 默认 #f7f7f7
    },
    // 可传入改变navbar样式
    navBgStyle: {
      type: String,
      // value: getApp().store.$state.config.color.primary
      value: '#fff'
    },
    // 可传入改变navbar title样式
    navTitleStyle: {
      type: String,
      value: 'color: black;'
    },
    navTitle: {
      type: String,
      value: ''
    },
    // 可传入改变nav back页面数
    navDelta: {
      type: Number,
      value: 1
    },
    // 决定是否显示loading
    navShowLoading: {
      type: Boolean,
      value: false
    },
    navTextStyle: {
      type: String,
      value: 'black'
    },
    // 是否显示返回按钮
    navShowBack: {
      type: Boolean,
      value: true
    },
    inHomePage: {
      type: Boolean,
      value: false
    },
    showTabBar: {
      type: Boolean,
      value: false
    },
    showNavBar: {
      type: Boolean,
      value: true
    },
    contentUseView: {
      type: Boolean,
      value: false
    },
    scrollEnable: {
      type: Boolean,
      value: true
    },
    tabBarType: {
      type: Number,
      value: 0
    },
    showLeftCustomIcon: {
      type: Boolean,
      value: false
    },
    customLeftIconUrl: {
      type: String,
      value: ''
    },
    showHomeIcon: {
      type: Boolean,
      value: false
    },
    shareTxt: {
      type: String,
      value: ''
    },
    hasPageContainer: {
      type: Boolean,
      value: false
    },
    floatMarginSize: {
      type: Number,
      value: 0
    },
    scrollEnabled: {
      type: Boolean,
      value: true
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    titleHeight,
    footHeight,
    logoIcon: './images/<EMAIL>',
    navigationBarHeight,
    logoHeightSize: 30,
    navBottomFloat,
    tabBarType: 0,
    authorizationBackground: {
      type: 0,
      color: '#fff'
    },
    markStyle: 'normal-page',
    scrollStyle: 'able-scroll',
    showBottomBar: bottomHeightPx
  },

  attached() {
    const {tabBarType, hasPageContainer, floatMarginSize, scrollEnabled = true} = this.properties || {}
    const {
      showTabBar,
      showNavBar,
      inHomePage,
      showPageShareBar
    } = this.data
    let contentHeight = getContentHeight(showNavBar, showTabBar, showPageShareBar)
    if (hasPageContainer){
      contentHeight = getRegisterPageHeight()
    }
    if (floatMarginSize){
      contentHeight -= floatMarginSize
    }

    let isMarkDay = (getApp().globalData.isMarkDayStatus || getMarkDayStatus()) === 'LIVE'
    // console.log('===== PAGE  isMarkDay >>>>', isMarkDay)
    if (isMarkDay){
      this.setData({
        markStyle: "commemoration-page",
        logoIcon: pic_logo_fill_hash,
      })
    }

    let scrollStyle = 'able-scroll'
    if (!scrollEnabled){
      scrollStyle = 'enable-scroll'
    }

    // console.log('======= NB-PAGE contentHeight >>>>', contentHeight)
    this.setData({
      contentHeight,
      inHomePage,
      tabBarType,
      scrollStyle
    })
  },

  ready() {
    let timer = setTimeout(() => {
      clearTimeout(timer)
      let isMarkDay = (getApp().globalData.isMarkDayStatus || getMarkDayStatus()) === 'LIVE'
      if (isMarkDay){
        this.setData({
          markStyle: "commemoration-page",
          logoIcon: pic_logo_fill_hash,
        })
      }
    }, 2000)
  },

  /**
   * 组件的方法列表
   */
  methods: {
    onTabChange(e = {}) {
      const {
        tabBar: {
          tabPath = '',
        },
        type = 0
      } = e.detail || {}

      let tList = (getApp().globalData.tabList)?.length ? getApp().globalData.tabList : getTabBarList() || []
      let _RES_PATH = tList.find(item => item && item.type == type)?.tabPath || ''
      // console.log('====== TAB_BAR page onTabChange tabPath,_RES_PATH,tabName >>>>', tabPath, _RES_PATH, tabName)
      let tabTimer = setTimeout(() => {
        clearTimeout(tabTimer)
        return wx.switchTab({
          url: `${tabPath || _RES_PATH}`
        })
      }, 100)
    },

    onPressNavLeft() {
      this.triggerEvent('onPressNavLeft', {})
    }
  }
})

.tabbar-container {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 999;
  width: 100%;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.1);
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.tabbar-container.iphoneX-height {
  padding-bottom: 66rpx;
}

.tabbar-item {
  display: flex;
  flex: 1;
}

.tabbar-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  align-items: center;
  justify-content: center;
  padding-top: 10rpx;
  padding-bottom: 10rpx;
}

.tabbar-text {
  font-size: 28rpx;
  margin-top: 4rpx;
}

.tabbar-icon {
  width: 42rpx;
  max-height: 42rpx;
  margin-top: 12rpx;
}
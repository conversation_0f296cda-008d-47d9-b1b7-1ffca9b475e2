<!--当tab只剩一个的时候 不显示-->
<block wx:if="{{list && list.length > 1}}">
  <view class="tabbar-container" style="background-color:{{$state.tabBar.backgroundColor||'#ffffff'}};">
    <view class="tabbar-item" wx:for="{{list}}" wx:key="type" data-index="{{index}}" data-item='{{item}}' bindtap="onChangeTab">
      <view class="tabbar-wrapper">
        <image class="tabbar-icon" src="{{currentSelectedType == item.type ? item.icon.iconNormal || '../../imgs/icon/<EMAIL>' : item.icon.iconSelector || '../../imgs/icon/<EMAIL>'}}" mode="aspectFill" />
        <view class="tabbar-text" style="color:{{currentSelectedType == item.type ? $state.tabBar.selectorColor||'#f04b28ff' : $state.tabBar.color}}">
          {{item.pageName || item.name || ''}}
        </view>
      </view>
    </view>
  </view>
</block>
import {
  eventName,
  vLog,
} from '../../common/index.js'

import {
  footHeight,
  isX
} from '../../common/const/systeminfo.js'
import {
  getTabBarList
} from "../../common/utils/userStorage";

const {
  DISMISS_TAB_BAR,
  TAB_LISTENER_EVENT,
} = eventName

Component({
  options: {
    addGlobalClass: true,
  },
  properties: {
    currentSelectedType: {
      type: Number,
      value: 0
    },
    tabMinHeight: {
      type: String,
      value: '10.5vh'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    isIphoneX: !!isX,
    footHeight: footHeight,
    list: []
  },

  async attached() {
    let list = (getApp().globalData.tabList)?.length ? getApp().globalData.tabList : getTabBarList() || []
    vLog.log('TAB_BAR list >>>>>', list)
    vLog.info('TAB_BAR list.length >>>>>', list.length).report()
    this.setData({
      list
    }, () => getApp().globalData.tabList = list)
  },

  ready() {
    const { list } = this.data || {}
    if (!list || list.length <= 1) {
      getApp().event.emit(TAB_LISTENER_EVENT, { key: DISMISS_TAB_BAR })
    }
  },
  /**
   * 组件的方法列表
   */
  methods: {
    onChangeTab(e = {}) {
      const { currentSelectedType = 0 } = this.data || {}
      const {
        dataset: {
          item
        }
      } = e.currentTarget || {}
      const { type } = item || {}

      if (type * 1 - currentSelectedType !== 0) {
        this.triggerEvent('tabChange', { type, tabBar: item }, {})
      }
    },
  }
})

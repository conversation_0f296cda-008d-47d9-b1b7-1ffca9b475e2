Component({
  properties: {
    tabs: {
      type: Array,
      value: []
    },
    currentIndex: {
      type: Number,
      value: 0
    },
    intoView: {
      type: String,
      value: 'tabbar0',
    },
    backgroundColor: {
      type: String,
      value: getApp()?.store.$state.themeColor || '#f04b28ff'
    },
    barBgColor: {
      type: String,
      value: '#fff'
    },
    color: {
      type: String,
      value: '#fff'
    },
    activeColor: {
      type: String,
      value: '#fff'
    },
    showTabLine: {
      type: Boolean,
      value: false
    },
    isSingleContent: {
      type: Boolean,
      value: true
    },
    isFromList: {
      type: <PERSON>ole<PERSON>,
      value: false
    },
    fontSize: {
      type: Number,
      value: 16
    },
    scrollLeft: {
      type: Number,
      value: 0
    }
  },
  data: {},
  methods: {
    clickTab(e = {}) {
      const index = e.currentTarget.dataset.index
      const value = e.currentTarget.dataset.value
      this.triggerEvent('tabNavClick', {index, value}, {})
      this.setData({
        currentIndex: index,
        intoView: `tabbar${index ? index - 1 : index}`
      })
    }
  }
})

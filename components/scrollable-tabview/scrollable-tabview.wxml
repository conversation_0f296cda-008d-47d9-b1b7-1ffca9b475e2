<view class='container navBar' style='background-color:{{backgroundColor}};'>
  <scroll-view
      scroll-into-view="{{intoView}}"
      scroll-x
      enhanced
      scroll-with-animation
      scroll-animation-duration="100"
      scroll-left="{{scrollLeft}}">
    <block wx:for='{{tabs}}' wx:key='index'>
      <view id="tabbar{{index}}"
            class='tab'
            style='font-weight:{{currentIndex == index ? "500": "normal"}};color: {{currentIndex == index ? activeColor : color}};background-color:{{backgroundColor}};padding-top: {{isSingleContent?20:14}}rpx;font-size: {{fontSize}}Px;'
            bindtap='clickTab'
            data-index='{{index}}'
            data-value="{{item.value?item.value:index}}">
        {{item.name}}
        <view
            wx:if="{{showTabLine}}"
            class='indicator'
            style='background-color:{{currentIndex == index ? barBgColor : ""}};margin-top:{{isFromList?"10px":"10rpx"}}'
        />
      </view>
    </block>
  </scroll-view>

  <view wx:if="{{showTabLine}}" class="border-line"/>
</view>

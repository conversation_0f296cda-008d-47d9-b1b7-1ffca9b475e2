import {systemtInfo, util} from "../../common/index";

const {
  titleHeight,
  screenHeight,
} = systemtInfo

const {rpx2px} = util

Component({
  properties: {
    contentHeight: {
      type: Number,
      value: rpx2px(screenHeight - titleHeight),
    },
    disabled: {
      type: Boolean,
      value: false
    },
  },

  methods: {
    onFloatAction(e = {}) {
      console.log('FLOAT action e >>>>', e)
      let params = {
        disabled: !this.data.disabled
      }

      this.triggerEvent('onFloatClick', params, {bubbles: true, composed: true})
    }
  }
});

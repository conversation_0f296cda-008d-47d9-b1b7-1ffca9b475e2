import {titleHeightPx, getContentHeightPx} from '../../common/const/systeminfo.js'
import {eventName, vLog} from '../../common/index.js'

const {platform} = wx.getSystemInfoSync()
const {SET_SCROLL_NAV_BAR_SIZE} = eventName

const app = getApp()

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    /**
     * 默认启动下拉刷新
     */
    enableRefresh: {
      type: Boolean,
      value: true,
    },
    /**
     * 默认启动加载更多
     */
    enableLoadMore: {
      type: Boolean,
      value: true,
    },
    /**
     * 定位top
     */
    layerTop: {
      type: Number,
      value: titleHeightPx
    },
    /**
     * 元素高度
     */
    elementHeight: {
      type: Number,
      value: getContentHeightPx()
    },
    elementMarginTop: {
      type: Number,
      value: 0
    },
    // 外部调用接口
    refreshing: {
      type: Boolean,
      value: false,
      observer: 'watchRefreshFinished',
    },
    loadmoring: {
      type: Boolean,
      value: false,
      observer: 'watchloadmoreFinished',
    },
    contentLibrary: {
      type: Boolean,
      value: false,
    },
    /**
     * 重置复位
     * 列表刷新的时候
     * 如果有加载更多的处理
     * 必须有溢出的数据
     */
    resetting: {
      type: Boolean,
      value: false,
      observer: 'watchReset',
    },
    /**
     * 数据上拉加载完毕
     * 没有更多的数据
     * 修改状态
     */
    nomore: {
      type: Boolean,
      value: false
    },
    scrollTop: {
      type: Number,
      value: 0
    },
    refreshTopDistance: {
      type: Number,
      value: 1
    },
    hasTabBarInfo: {
      type: Boolean,
      value: false
    },
    floatMarginSize: {
      type: Number,
      value: 0
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    /**
     * 1.devtools-android 与开发模式都使用 模式 安卓 开发板通过
     * 2.模式 ios独立使用，因为ios的反弹，重做
     */
    modePlat: platform == "ios" ? "ios" : "devtools-android",
    // modePlat: "ios",

    loadingHeight: 48, //正在加载时高度
    loading: false, //是否在加载中
    pull: true, //true 下拉刷新状态或者上拉加载更多状态   false 释放
    refreshingText: "下拉刷新",
    pullText: "下拉刷新",
    releaseText: "释放立即刷新",
    loadingText: "正在刷新...",
    finishText: "刷新完成",

    // 加载更多
    showLoadMore: false, //因为数据关系，下拉更多需要再lower才能出发
    loadmoreText: "正在加载...",
    nomoreText: "数据全部加载完毕",

    //ios
    pullDownStatus: 0,

    // 刷新完成
    refreshFinished: false,
    elementMarginTop: 0,

    //安卓
    refreshHeight: 0, //刷新布局高度
    loadMoreHeight: 0, //加载更多布局高度
    scrolling: false, //滚动中,不处理事件，只有头尾处理
    isUpper: true,
    isLower: false,
    topBarHeight: 60,
    downY: 0, //触摸时Y轴坐标
    end: true, //touchEnd
    animationData: {},
  },

  attached() {
    app.globalData.emitter.on(SET_SCROLL_NAV_BAR_SIZE, (e) => {
      vLog.log('SCROLL-VIEW-X SET_SCROLL_NAV_BAR_SIZE e >>>>  ', e)
      this.setData({
        elementMarginTop: e * 1,
      })
    })
  },

  ready() {
    const {elementMarginTop, floatMarginSize, elementHeight} = this.properties
    //ios模式下
    if (this.data.modePlat === "ios" || this.data.modePlat === 'devtools-android'){
      let _elementHeight = elementHeight + 48
      if (floatMarginSize){
        _elementHeight -= floatMarginSize
      }
      this.setData({
        elementHeight: _elementHeight
      });
    }

    //设置加载的高度，是当前页面包裹区域的0.8%
    this.data.loadingHeight = elementHeight * 0.08;

    this.setData({
      elementMarginTop
    })
  },

  /**
   * 组件的方法列表
   */
  methods: {

    //======================
    // 通用刷新后处理
    //======================

    /**
     * 下拉：刷新，请求
     */
    sendRefreshWatch() {
      this.properties.refreshing = true; //必须
      let timer = setTimeout(() => {
        clearTimeout(timer);
        this.triggerEvent('pulldownrefresh');
      }, 500);
    },

    /**
     * 上拉：加载更多，请求
     */
    sendLoadMoreWatch() {
      this.properties.loadmoring = true; //必须
      let timer = setTimeout(() => {
        clearTimeout(timer)
        this.triggerEvent('pulluploadmore');
      }, 500);
    },

    /**
     * 下拉更新后处理
     */
    watchRefreshFinished(newVal, oldVal) {
      if (oldVal === true && newVal === false){
        if (this.data.modePlat === "ios"){
          this.onRefreshFinished_i()
        } else {
          this.onRefreshFinished_a()
        }
      }
    },

    /**
     * 上拉加载更多
     */
    watchloadmoreFinished(newVal, oldVal) {
      if (oldVal === true && newVal === false){
        this.properties.resetting = false
      }
    },

    /**
     * 观察数据重置
     */
    watchReset(newVal, oldVal) {
      if (newVal === true && oldVal === false){
        this.setData({
          showLoadMore: false
        })
      }
    },

    /**
     * 上拉  滚动条 滚动到底部时触发
     * @param {*} e
     */
    onLoadmore() {
      this.data.end = true;
      this.data.isLower = true;
      this.data.scrolling = false;

      if (this.data.enableLoadMore){
        //每次刷新都还原，必须重新设置，因为数据的列表是可以变化的
        this.setData({
          showLoadMore: true
        })

        //上拉到越界的时候，才启动加载更多
        if (!this.data.nomore){
          this.sendLoadMoreWatch()
        }
      }
    },

    //======================
    // ios下处理
    //======================

    /**
     * 到顶部，越界
     */
    onUpper_i() {
      this.data.isUpper = true; //标记下拉越界
    },

    onScroll_i(e = {}) {
      this.triggerEvent("viewscroll", e.detail.scrollTop)
      if (this.data.enableRefresh && this.data.isUpper){
        const status = this.data.pullDownStatus;
        if (status === 3 || status == 4) return;
        const scrollTop = e.detail.scrollTop;

        // 默认值
        let targetStatus = 1;
        let pull = false
        let refreshingText = this.data.pullText

        // 超过
        if (Math.abs(scrollTop) > this.data.loadingHeight){
          targetStatus = 2;
          pull = true
          refreshingText = this.data.releaseText
        }

        if (status != targetStatus){
          this.setData({
            pull: pull,
            refreshingText: refreshingText,
            pullDownStatus: targetStatus,
          })
        }
        this.data.isUpper = false
      }
    },

    /**
     * 下拉刷新
     * @param {*} e
     */
    onTouchEnd_i() {
      if (this.data.pullDownStatus === 2){
        this.setData({
          loading: true,
          pull: false,
          refreshingText: this.data.loadingText,
          pullDownStatus: 3, //正在刷新数据中
        })
        let timer = setTimeout(() => {
          clearTimeout(timer)
          this.sendRefreshWatch()
        }, 500)
      }
    },

    /**
     * 只有上拉刷新处理
     * 所以直接加载数据的nomore
     */
    onRefreshFinished_i() {
      //往上刷新，重置nomore
      this.setData({
        nomore: false,
        refreshFinished: true,
        refreshingText: this.data.finishText
      })
      let timer = setTimeout(() => {
        clearTimeout(timer)
        // 返回动画
        this.setData({
          pullDownStatus: 4
        })

        // 复位
        let _timer = setTimeout(() => {
          clearTimeout(_timer)
          this.setData({
            pull: true,
            loading: false,
            refreshFinished: false,
            pullDownStatus: 0,
          })
        }, 600);
      }, 1000);
    },

    /**
     * 滚动到顶部/左边，会触发 scrolltoupper 事件
     * 下拉  滚动条 滚动顶底部时触发
     * @param {*} e
     */
    upper() {
      this.data.end = true;
      this.data.isUpper = true; //标记下拉越界
      this.data.scrolling = false; //标记不是自动滚动了
    },

    scroll(e = {}) {
      this.triggerEvent("viewscroll", e.detail.scrollTop)
      if (this.data.end && this.data.isLower){
        //如果快速拖动 然后释放 会在end后继续scroll
        //可能出现scroll到顶点后依然走scroll方法
        return;
      }
      if (this.data.end && this.data.isUpper){
        return;
      }
      this.data.scrolling = true;
      this.data.isUpper = false;
      this.data.isLower = false;
    },

    start(e = {}) {
      this.data.end = false;
      if (this.data.scrolling || this.data.loading){
        return;
      }
      //触摸目标在视口中的y坐标。
      let clientY = e.touches[0].clientY;
      this.setData({
        downY: clientY,
        refreshHeight: 0,
        loadMoreHeight: 0,
        pull: true,
        refreshingText: this.data.pullText
      });
    },

    move(e={}) {
      if (!this.data.enableRefresh || this.data.scrolling || this.data.loading){
        return
      }
      //表示自上次触摸以来发生了什么改变的Touch对象的数组。
      let movePoint = e.changedTouches[0];
      //降速,防抖
      let moveY = (movePoint.clientY - this.data.downY) * 0.4;
      //上拉不需要
      if (moveY < 0){
        return
      }
      //高度的最大值，不能超过3倍加载view
      if (moveY > this.data.loadingHeight * 3){
        return;
      }
      //必须越界
      if (this.data.isUpper){
        this.setData({
          refreshHeight: moveY
        })
        if (this.data.refreshHeight > this.data.loadingHeight){
          if (this.data.refreshingText !== this.data.releaseText){
            this.setData({
              pull: false,
              refreshingText: this.data.releaseText
            })
          }
        } else {
          if (this.data.refreshingText !== this.data.pullText){
            this.setData({
              pull: true,
              refreshingText: this.data.pullText
            })
          }
        }
      }
    },

    end() {
      this.data.end = true;
      this.data.scrolling = false;
      //释放开始刷新
      // console.log('释放开始刷新')
      let height = this.data.loadingHeight;
      if (this.data.refreshHeight > this.data.loadingHeight){
        this.setData({
          refreshHeight: height,
          loading: true,
          pull: false,
          refreshingText: this.data.loadingText
        });
        this.sendRefreshWatch()
      } else {
        //复位下拉刷新的属性
        // console.log('复位下拉刷新的属性')

        this.setData({
          refreshHeight: 0,
          loadMoreHeight: 0,
          loading: false,
          pull: true
        })
      }
    },

    /**
     * 设置高度
     */
    setTop(time, heightValue) {
      let animation = wx.createAnimation({
        duration: time,
        timingFunction: 'linear'
      })
      animation.top(heightValue).step()
      this.setData({
        animationData: animation.export()
      })
    },

    /**
     * 加载完毕
     */
    onRefreshFinished_a() {
      // console.log('加载完毕')
      if (this.data.refreshHeight){
        this.setData({
          refreshFinished: true,
          refreshingText: this.data.finishText
        });
        let timer = setTimeout(() => {
          clearTimeout(timer)
          this.setTop(150, this.data.layerTop)
          let _timer = setTimeout(() => {
            clearTimeout(_timer)
            this.setData({
              refreshFinished: false,
              refreshHeight: 0,
              loadMoreHeight: 0,
              loading: false,
              animationData: wx.createAnimation({
                duration: 0
              }).step()
            })
          }, 200)
        }, 600);
      }
    }
  }
})

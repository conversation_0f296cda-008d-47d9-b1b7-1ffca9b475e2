<!-- 加载更多模板 -->
<template name="load-more">
  <view class="loadMore-view x-common">
    <view>
      <image
          wx:if="{{!nomore}}"
          class="roate loading"
          src="./images/loading-i.png"
      />
    </view>
    <view class="middle-view">
      <!-- 完成 -->
      <block wx:if="{{nomore}}">
        <!-- <image class="loading loading-finish" src="./images/finish-i.png"></image>
        <text class="loading-text">{{nomoreText}}</text> -->
      </block>
      <!-- 加载 -->
      <block wx:else>
        <text class="loading-text">{{loadmoreText}}</text>
      </block>
    </view>
    <view></view>
  </view>
</template>

    <!-- ios -->
<block wx:if="{{modePlat==='ios'}}">
  <scroll-view
      id="scroll-view"
      style="position:fixed;width:100%;left:0;height:{{elementHeight}}px;top:{{loadMoreHeight == 0 ? refreshHeight*refreshTopDistance+layerTop : -loadMoreHeight+layerTop}}px;bottom:{{loadMoreHeight}}px;margin-top: {{elementMarginTop}}px;"
      class="scroll-view-ios translateY {{pullDownStatus === 3 ? 'refresh' : ''}} {{pullDownStatus === 4 ? 'finish' : ''}}"
      scroll-y="true"
      scroll-with-animation
      enable-back-to-top
      bindscroll="onScroll_i"
      bindtouchend="onTouchEnd_i"
      bindscrolltolower="onLoadmore"
      bindscrolltoupper="onUpper_i"
      scroll-top="{{scrollTop}}">
    <!-- 下拉 -->
    <view class="pulldown x-common" style="margin-top: {{hasTabBarInfo?refreshTopDistance>3?(contentLibrary?160:100):55:0}}px;">
      <block wx:if="{{enableRefresh}}">
        <view>
          <block wx:if="{{!refreshFinished}}">
            <image
                wx:if="{{loading}}"
                class="roate loading"
                src="./images/loading-i.png"
            />
            <image
                wx:else
                class="{{(pull?'pull':'')}} refresh"
                src="./images/arrow-i.png"
            />
          </block>
        </view>
        <view>
          <image
              wx:if="{{refreshFinished}}"
              class="loading loading-finish"
              src="./images/finish-i.png"
          />
          <text>{{refreshingText}}</text>
        </view>
        <view></view>
      </block>
    </view>
    <!-- 如果没有内容，也要撑开足够的高度 -->
    <view style="min-height:100%;position: absolute;width:100%;">
      <slot></slot>
      <!-- 加载更多 -->
      <template
          wx:if="{{showLoadMore}}"
          is="load-more"
          data="{{nomore,nomoreText,loadmoreText}}"
      />
    </view>
  </scroll-view>
</block>

    <!-- 安卓或开发 -->
<block wx:if="{{modePlat==='devtools-android'}}">
  <!-- 下拉刷新 -->
  <view class="refresh-block x-common" style="height:{{hasTabBarInfo?refreshHeight*refreshTopDistance:refreshHeight}}px;">
    <view>
      <block wx:if="{{!refreshFinished}}">
        <image
            wx:if="{{loading}}"
            class="roate loading"
            src="./images/loading-i.png"
        />
        <image
            wx:else
            class="{{(pull?'':'pull')}} refresh"
            src="./images/arrow-i.png"
        />
      </block>
    </view>
    <view>
      <image
          wx:if="{{refreshFinished}}"
          class="loading loading-finish"
          src="./images/finish-i.png"
      />
      <text>{{refreshingText}}</text>
    </view>
    <view></view>
  </view>
  <!-- slot -->
  <scroll-view
      class="scroll_container"
      scroll-y="true"
      style="position:fixed;width:100%;left:0;height:{{elementHeight}}px;top:{{loadMoreHeight == 0 ? refreshHeight+layerTop : -loadMoreHeight+layerTop}}px;bottom:{{loadMoreHeight}}px;margin-top: {{elementMarginTop}}px;"
      bindscroll="scroll"
      bindscrolltolower="onLoadmore"
      bindscrolltoupper="upper"
      bindtouchstart="start"
      bindtouchend="end"
      scroll-top="{{scrollTop}}"
  >
    <view style="min-height:100%;position: absolute;width:100%;" bindtouchmove="move">
      <slot></slot>
      <!-- 加载更多 -->
      <template
          wx:if="{{showLoadMore && loadmoring}}"
          is="load-more"
          data="{{nomore,nomoreText,loadmoreText}}"
      />
    </view>
  </scroll-view>
</block>


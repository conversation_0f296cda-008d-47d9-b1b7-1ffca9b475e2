import config from '../../common/const/config.js'

import {
  validator,
  format
} from '../../common/index.js'

const {
  isUrl: testUrl
} = validator

const {
  formatPixelImage
} = format

Component({
  externalClasses: ['nb-class'],
  /**
   * 组件的属性列表
   */
  properties: {
    mode: {
      type: String,
      value: 'widthFix'
    },
    src: {
      type: String,
      observer: 'handleSrcChange'
    },
    isOSSImg: {
      type: Boolean,
      value: true,
    },
    enablePixelRatio: {
      type: Boolean,
      value: true,
    },
    isCoverImage: {
      type: Boolean,
      value: false,
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    source: ''
  },

  lifetimes: {
    attached: function () {
      const {
        src
      } = this.data
      this.handleSrcChange(src)
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    handleSrcChange(src) {
      const {
        isOSSImg,
        enablePixelRatio
      } = this.properties

      if (testUrl(src)) {
        this.setData({
          source: src,
        })
        return
      }

      if (isOSSImg) {
        this.setData({
          source: enablePixelRatio ? `${config.common.ossPrefix}${formatPixelImage(src)}` : `${config.common.ossPrefix}${src}`
        })
        return
      }

      this.setData({
        source: formatPixelImage(src)
      })
    }
  }
})

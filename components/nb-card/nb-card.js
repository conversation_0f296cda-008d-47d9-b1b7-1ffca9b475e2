Component({
  options: {
    addGlobalClass: true,
  },
  externalClasses: ['card-class'],
  /**
   * 组件的属性列表
   */
  properties: {
    isCard: {
      type: Boolean,
      value: true
    },
    data: {
      type: Array,
      value: []
    },
    style: {
      type: Object,
      value: {}
    },
    isGridCard: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {},

  /**
   * 组件的方法列表
   */
  methods: {}
})

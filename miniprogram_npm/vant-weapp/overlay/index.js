import { VantComponent } from '../common/component';
VantComponent({
    props: {
        show: <PERSON><PERSON><PERSON>,
        mask: <PERSON><PERSON><PERSON>,
        customStyle: String,
        duration: {
            type: [Number, Object],
            value: 300
        },
        zIndex: {
            type: Number,
            value: 1
        }
    },
    methods: {
        onClick() {
            this.$emit('click');
        },
        // for prevent touchmove
        noop() { }
    }
});

import { VantComponent } from '../common/component';
VantComponent({
  field: true,
  classes: ['field-class', 'input-class', 'cancel-class'],
  props: {
    focus: <PERSON><PERSON><PERSON>,
    error: <PERSON><PERSON><PERSON>,
    disabled: <PERSON><PERSON><PERSON>,
    readonly: <PERSON><PERSON><PERSON>,
    inputAlign: String,
    showAction: <PERSON><PERSON><PERSON>,
    useActionSlot: <PERSON><PERSON><PERSON>,
    placeholder: String,
    placeholderStyle: String,
    background: {
      type: String,
      value: '#ffffff'
    },
    marginTop:{
      type:Number,
      value:0
    },
    maxlength: {
      type: Number,
      value: -1
    }
  },
  methods: {
    onChange: function onChange(event) {
      this.set({
        value: event.detail
      });
      this.$emit('change', event.detail);
    },
    onCancel: function onCancel() {
      this.set({
        value: ''
      });
      this.$emit('cancel');
      this.$emit('change', '');
    },
    onSearch: function onSearch() {
      this.$emit('search', this.data.value);
    },
    onFocus: function onFocus() {
      this.$emit('focus');
    },
    onBlur: function onBlur() {
      this.$emit('blur');
    },
    onClear: function onClear() {
      this.$emit('clear');
    }
  }
});

<view class="van-picker custom-class">
  <view
    wx:if="{{ showToolbar }}"
    class="van-picker__toolbar van-hairline--top-bottom toolbar-class"
  >
    <view
      class="van-picker__cancel"
      hover-class="van-picker__cancel--hover"
      hover-stay-time="70"
      data-type="cancel"
      bindtap="emit"
    >
      {{ cancelButtonText }}
    </view>
    <view wx:if="{{ title }}" class="van-picker__title van-ellipsis">{{ title }}</view>
    <view
      class="van-picker__confirm"
      hover-class="van-picker__confirm--hover"
      hover-stay-time="70"
      data-type="confirm"
      bindtap="emit"
    >
      {{ confirmButtonText }}
    </view>
  </view>
  <view wx:if="{{ loading }}" class="van-picker__loading">
    <loading color="#1989fa"/>
  </view>
  <view
    class="van-picker__columns"
    style="height: {{ itemHeight * visibleItemCount }}px"
    catch:touchmove="noop"
  >
    <picker-column
      class="van-picker__column"
      wx:for="{{ isSimple(columns) ? [columns] : columns }}"
      wx:key="{{ index }}"
      data-index="{{ index }}"
      custom-class="column-class"
      value-key="{{ valueKey }}"
      initial-options="{{ isSimple(columns) ? item : item.values }}"
      default-index="{{ item.defaultIndex || defaultIndex }}"
      item-height="{{ itemHeight }}"
      visible-item-count="{{ visibleItemCount }}"
      active-class="active-class"
      bind:change="onChange"
    />
    <view
      class="van-picker__frame van-hairline--top-bottom"
      style="height: {{ itemHeight }}px"
    />
  </view>
</view>

<wxs module="isSimple">
  function isSimple(columns) {
    return columns.length && !columns[0].values;
  }
  module.exports = isSimple;
</wxs>

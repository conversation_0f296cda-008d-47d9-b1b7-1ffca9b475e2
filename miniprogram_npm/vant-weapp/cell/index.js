import {link} from '../mixins/link';
import {VantComponent} from '../common/component';

VantComponent({
  classes: [
    'title-class',
    'label-class',
    'value-class',
    'right-icon-class',
    'hover-class'
  ],
  mixins: [link],
  props: {
    title: null,
    value: null,
    icon: String,
    size: String,
    label: String,
    center: Boolean,
    isLink: Boolean,
    required: Boolean,
    clickable: Boolean,
    titleWidth: String,
    customStyle: String,
    arrowDirection: String,
    useLabelSlot: Boolean,
    border: {
      type: Boolean,
      value: true
    },
  },
  data: {
    crashLIB: ['请选择渠道', '请选择您的机构名称'],
    showDK: false,
  },
  watch: {
    title: 'updateDKColor'
  },
  methods: {
    onClick(event) {
      this.$emit('click', event.detail);
      this.jumpLink();
    },
    updateDKColor() {
      if (!this.data.crashLIB.includes(this.data.title)){
        this.set({
          showDK: true
        })
      } else {
        this.set({
          showDK: false
        })
      }
    },
  }
});

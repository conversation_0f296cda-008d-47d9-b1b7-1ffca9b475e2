<wxs src="../../../../wxs/common.wxs" module="tools"/>
<nb-page
    showNavBar="{{true}}"
    navShowBack="{{true}}"
    showHomeIcon="{{false}}"
    navTitle="{{'关注服务号'}}"
    showTabBar="{{false}}">

  <scroll-view
      id="scroll-view"
      style="position:fixed;width:100%;left:0;height:{{contentHeight}}px;;"
      class="list-container"
      scroll-y="true"
      scroll-with-animation
      enable-back-to-top>

    <view class="guide-title">
      {{guideInfo.title || ""}}
    </view>

    <view
        class="content-list"
        wx:if="{{guideInfo.content.length>0}}"
        wx:for="{{guideInfo.content}}"
        wx:for-item="item"
        wx:for-index="id"
        wx:key="id">
      <view>
        {{(id + 1) + '.' + item}}
      </view>
    </view>

    <view class="qr-block">
      <image
          src="{{guideInfo.qrCodeUrl}}"
          mode="aspectFill"
          class="qr-code"
          show-menu-by-longpress="{{true}}"
      />
    </view>

    <view class="qr-bottom">{{'长按扫码，关注服务号'}}</view>
  </scroll-view>
  <van-toast id="van-toast"/>
</nb-page>

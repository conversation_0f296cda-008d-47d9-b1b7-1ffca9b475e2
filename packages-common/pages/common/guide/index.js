import {
  util,
  systemtInfo,
  vLog,
} from "../../../../common/index.js";
import {
  getSystemInfo
} from "../../../../common/utils/userStorage";

const {
  rpx2px
} = util

const {
  screenHeight,
  titleHeight
} = systemtInfo

Page({
  data: {
    contentHeight: rpx2px(screenHeight - titleHeight),
    guideInfo: {}
  },

  onLoad() {
    const sysInfo = getSystemInfo()
    vLog.log('GUIDE sysInfo >>>', sysInfo)
    const guideInfo = sysInfo?.staff?.clientParams?.guideInfo || {}
    this.setData({
      guideInfo
    })
  },

  onHide() {
    return wx.navigateBack({})
  }
});

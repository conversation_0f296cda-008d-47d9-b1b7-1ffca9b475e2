import {
  util,
  qs,
  systemtInfo,
  interaction,
  enums,
  vLog
} from "../../../../common/index.js";

import {getToken, getUser} from "../../../../common/utils/userStorage";
import {addSceneParam} from "../../../../common/nb/home";

const {rpx2px} = util
const {CHANNEL_ID} = enums
const {screenHeight, titleHeight} = systemtInfo

const LOGO = 'https://aim-pic-dev.gffunds.com.cn/image/course/2022-12-09/16a8b324-b637-422e-815c-07a673d6127e.png'

Page({
  data: {
    contentHeight: rpx2px(screenHeight - titleHeight),
    logo: LOGO,
    wInfo: {},
    doJump: false
  },
  onLoad(options) {
    vLog.log('AUTH options >>>', options)
    this.initAuth()
  },

  onShow() {
    const {doJump} = this.data || {}
    if (doJump){
      return wx.navigateBack({})
    }
  },

  initAuth() {
    const wInfo = getUser()
    this.setData({
      wInfo
    })
  },

  async onApplyAction(e = {}) {
    vLog.log('AUTH e >>>', e)
    const {
      dataset: {
        type = ''
      }
    } = e.currentTarget || {}
    const {wInfo} = this.data
    let auth2Params = {
      phone: wInfo?.phone,
    }

    let extraData = {
      token: getToken() || "",
      channel: CHANNEL_ID
    }

    switch (type) {
      case 'AGREE':
        auth2Params['authed'] = true
        break

      case 'CANCEL':
        auth2Params['authed'] = false
        delete extraData.token
        break

      default:
        break
    }
    let authParams = {
      params: `${qs.stringify(auth2Params)}`,
      day: -1,
    }
    interaction.showLoading('加载中...')
    const {success, param, msg} = await addSceneParam(authParams)
    interaction.hideLoading()
    vLog.log('AUTH success, param, msg >>>', success, param, msg).report()
    vLog.log('AUTH extraData >>>', extraData).report()

    this.setData({
      doJump: true
    })
    return wx.navigateToMiniProgram({
      appId: 'wx0dcc15bfb6b826a8',
      path: `pages/thirdParty/index?${qs.stringify(extraData)}`,
      extraData,
      envVersion: 'release', //体验版本,线上版本('release')
      success(res) {
        // 打开成功
        vLog.info('AUTH 打开成功 >>>', res).report()
      }
    })
  }
});

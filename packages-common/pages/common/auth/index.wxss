.list-container{
    background-color: #f2f2f2;
}

.content-header{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100vw;
    height: 50%;
    max-height: 50vh;
}

.content-footer{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;
    width: 100vw;
    height: 50%;
    max-height: 50vh;
    padding: 0;
}

.logo-icon{
    width: 80px;
    height: 80px;
    border-radius: 8rpx;
    margin-bottom: 10rpx;
}

.logo-tips{
    font-size: 14Px;
    color: #333;
}

.apply-block{
    display: flex;
    flex-direction: column;
    width: 90vw;
    justify-content: center;
    align-items: flex-start;
    padding: 20px 0;
    margin-top: 10vw;
}

.apply-block-title{
    font-size: 16Px;
    color: #333;
    font-weight: bold;
    margin-bottom: 4rpx;
}

.apply-block-content{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    font-size: 14Px;
    color: #696969;
}

.apply-icon{
    margin-bottom: -6px;
    margin-right: 6rpx;
}

.apply-submit{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 90vw;
    height: 46px;
    border-radius: 30px;
    font-size: 16Px;
    color: #fff;
    margin-bottom: 14px;
}

.apply-cancel{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #cecece;
    width: 90vw;
    height: 46px;
    border-radius: 30px;
    font-size: 16Px;
    color: #666;
    margin-bottom: 40px;
}

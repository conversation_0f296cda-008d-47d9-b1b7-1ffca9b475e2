class PosterCard {
  constructor(props) {
    this.data = {...props}
  }

  palette() {
    const {
      width,
      canvasHeight,
      contentImgHeight,
      qrCode,
      currCoverUrl,
      opts = {},
      wInfo = {},
      leftSize,
      showUserInfo = true,
    } = this.data || {}
    const {name = ''} = opts || {}

    return ({
      width: `${width}px`,
      height: `${canvasHeight}px`,
      background: '#ffffff',
      borderRadius: '20px',
      views: [
        {
          id: `image-top`,
          type: 'image',
          url: `${currCoverUrl}`,
          css: {
            maxHeight: '240px',
            width: `${width}px`,
            height: `${contentImgHeight}px`,
            borderRadius:'20px 20px 0 0',
          },
        },
        {
          id: 'text_title',
          type: 'text',
          text: `${name}`,
          css: {
            fontSize: '36rpx',
            color: '#333',
            top: `${contentImgHeight + 20}px`,
            maxLines: 1,
            fontWeight: 'bold',
            textAlign: 'center',
            left: `${leftSize}px`
          }
        },
        showUserInfo ? _textDecoration(`${wInfo.name}`, 0, contentImgHeight + 80) : {},
        showUserInfo ? _textDecoration(`${wInfo.companyName}`, 1, contentImgHeight + 115) : {},
        showUserInfo ? _textDecoration(`邀请你观看${name}`, 2, contentImgHeight + 150) : {},
        {
          type: 'image',
          id: 'qr-image',
          url: `data:image/png;base64,${qrCode}`,
          css: {
            top: `${contentImgHeight + 60}px`,
            right: showUserInfo ? '20px' : '105px',
            width: '100px',
            height: '100px'
          },
        },
      ]
    })
  }
}

function _textDecoration(decoration, index, sTop) {
  return ({
    type: 'text',
    id: `text_content_${index}`,
    text: `${decoration}`,
    css: {
      top: `${sTop}px`,
      color: '#333',
      fontSize: '32rpx',
      maxLines: 1,
      textAlign: 'center',
      left: `20px`
    },
  });
}

export default PosterCard

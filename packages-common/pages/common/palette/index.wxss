@keyframes fadeIn{
    0%{
        opacity: 0;
    }
    100%{
        opacity: 1;
    }
}

.qr-box{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100vw;
    height: 100%;
}

.canvas-block{
    display: flex;
    flex-direction: column;
}

.qr-content{
    display: flex;
    flex-direction: column;
    border-bottom-left-radius: 20px;
    border-bottom-right-radius: 20px;
    background-color: #ffffff;
    padding-bottom: 20px;
}

.content-title{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px 0;

    font-size: 18Px;
    font-weight: bold;
    color: #333;
}

.content-body{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-around;
}

.content-info{
    display: flex;
    flex-direction: column;
    font-size: 16px;
    color: #333;
    line-height: 1.85;
}

.img-header{
    width: 84vw;
    height: 100%;
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
}

.page-block{
    width: 100%;
    position: relative;
}

.hideCanvas{
    position: absolute;
    left: 999999px;
}

.page-block{
    width: 100%;
    position: relative;
}

.qr-control-bar{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-around;
    width: 100vw;
    margin-top: 10vw;
}

.download-bar{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.icon-btnn{
    width: 36px;
    height: 36px;
}

.control-text{
    font-size: 14Px;
    color: #fff;
    margin-top: 6rpx;
}

.draw_canvas{

}

.loading-block{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: absolute;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100%;
    z-index: 9999;
    background-color: rgba(0, 0, 0, 0.55);
}

.show-bar{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.er-code{
    width: 100px;
    height: 100px;
    border-radius: 10px;
}

.fade_in{
    animation: fadeIn 1.2s both;
}

.fade_null{

}

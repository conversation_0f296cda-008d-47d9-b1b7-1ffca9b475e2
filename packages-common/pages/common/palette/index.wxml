<wxs src="../../../../wxs/common.wxs" module="tools"/>
<nb-page
    showNavBar="{{true}}"
    navShowBack="{{true}}"
    containerBgColor="rgba(0,0,0,1)"
    navTitle="生成海报">
  <view class="qr-box" bind:tap="onBgTouch">
    <painter
        customActionStyle="{{customActionStyle}}"
        palette="{{paintPalette}}"
        bind:imgOK="onImgOK"
        widthPixels="1000"
    />

    <view class="canvas-block">
      <image
          class="img-header"
          src="{{currCoverUrl}}"
          style="height: {{contentImgHeight}}px; max-height: 240px; width: 100%"
          mode="aspectFill"/>
      <view
          class="qr-content"
          style="width: {{width}}px;height: {{contentBodyHeight}}px;">
        <view
            class="content-title"
            style="width: {{width}}px">
          <!--          {{'一二三四五六七八九十勾圈凯尖甲乙丙丁'}}-->
          {{opts.name || ''}}
        </view>

        <view class="content-body">
          <view wx:if="{{showUserInfo}}" class="content-info">
            <view>
              {{wInfo.name || ''}}
            </view>
            <view>
              {{wInfo.companyName || ''}}
            </view>
            <view>
              {{'邀请你观看' + opts.name || ''}}
            </view>
          </view>

          <image class="er-code" src="data:image/png;base64,{{qrCode}}"/>
        </view>
      </view>
    </view>

    <view class="qr-control-bar">
      <view class="download-bar" catch:tap="onDownloadImg">
        <image src="../../../../imgs/icon/ic_download.png" class="icon-btnn" mode="aspectFit"/>
        <view class="control-text">{{'保存图片'}}</view>
      </view>

      <view class="show-bar" catch:tap="onChangeShow">
        <image wx:if="{{showUserInfo}}" src="../../../../imgs/icon/ic_disable.png" class="icon-btnn" mode="aspectFit"/>
        <image wx:else src="../../../../imgs/icon/ic_show.png" class="icon-btnn" mode="aspectFit"/>

        <view class="control-text">{{showUserInfo ? '隐藏名片' : '显示名片'}}</view>
      </view>
    </view>
  </view>

  <view class="loading-block" wx:if="{{building}}">
    <van-loading type="spinner" size="40px" color="#FFF"/>
    <view style="color: #FFF;font-size: 16Px; margin-top: 10rpx">{{progress ? progress + '%' : '生成中...'}}</view>
  </view>
</nb-page>

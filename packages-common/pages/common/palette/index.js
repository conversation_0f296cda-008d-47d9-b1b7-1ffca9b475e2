import {
  global,
  util,
  systemtInfo,
  interaction,
  qs,
  enums,
  breakIn,
  layout,
  vLog
} from "../../../../common/index.js";

import PosterCard from "./painter/card";
import {addSceneParam, getWXACode} from "../../../../common/nb/home";

import {
  getUserRole,
  getUser,
  getSystemInfo,
  getUserId,
  getUserLogin
} from "../../../../common/utils/userStorage";

const {windowWidth} = wx.getSystemInfoSync()
const {screenHeight, footHeight, titleHeight} = systemtInfo
const {rpx2px, doUrlDeCode} = util
const {EnterSource, GLOBAL_START_PATH} = enums

function isEmptyObject(obj = {}) {
  return Object.keys(obj).length === 0;
}

const CONTENT_BODY_SIZE = 200

Page({
  imagePath: '',

  data: {
    contentHeight: rpx2px(screenHeight - footHeight - titleHeight),
    qrCode: '',
    src: '',
    saveImg: '',
    envVersion: "",
    type: '',
    opts: {},

    currCoverUrl: '',
    wInfo: {},
    sysInfo: {},

    showUserInfo: true,
    width: windowWidth * 0.84,
    contentBodyHeight: CONTENT_BODY_SIZE,
    contentImgHeight: 0,
    canvasHeight: CONTENT_BODY_SIZE,

    progress: 0,
    building: true,
    title: '',
    leftSize: 0,
    hasLogin: false,
    userRole: null,

    template: {},
    customActionStyle: {},
  },

  onLoad(options) {
    vLog.log('PALETTE options >>>>', options)
    let opts = {}
    for (const item of Object.entries(options)) {
      let [key, value] = item
      key = doUrlDeCode(key)
      value = doUrlDeCode(value)
      opts[key] = value
    }
    vLog.log('PALETTE AAA opts >>>>', opts)
    opts = qs.parse(opts)
    vLog.log('PALETTE BBB opts >>>>', opts)
    let {type = '', currCoverUrl, name = ''} = opts || {}
    if (currCoverUrl){
      this.getImgInfo(currCoverUrl)
    }

    let {title, leftSize} = layout.getLeftSize(name)
    vLog.log('PALETTE BBBB leftSize >>>', leftSize)

    const hasLogin = getUserLogin()
    this.setData({
      currCoverUrl,
      opts,
      type,
      title,
      leftSize,
      sysInfo: getSystemInfo(),
      wInfo: getUser(),
      hasLogin
    }, () => this.initQrBox(hasLogin))
    this.onProbe()
  },

  onProbe() {
    let _ProbeTimer = setTimeout(() => {
      clearTimeout(_ProbeTimer)

      let userRole = getUserRole()
      if (typeof userRole === 'string'){
        userRole = ''
      }

      this.setData({
        userRole
      })
    }, 1500)
  },

  async getImgInfo(url) {
    vLog.log('PALETTE getImgInfo url >>>>', url)
    if (`${url}`.includes('?x-oss-process=image')){
      let [cutUrl] = `${url}`.split('?x-oss-process=image')
      vLog.log('PALETTE cutUrl >>>', cutUrl)
      url = cutUrl
    }
    let that = this
    await wx.request({
      url: `${url}?x-oss-process=image/info`,
      method: 'GET',
      success(res) {
        vLog.log('PALETTE getImgInfo res >>>', res)
        const {
          data: {
            ImageWidth: {
              value: wImg = 1,
            },
            ImageHeight: {
              value: hImg = 1,
            }
          }
        } = res || {}

        vLog.log('PALETTE windowWidth / wImg * hImg >>>', (windowWidth * 0.9) / wImg * hImg)
        let contentImgHeight = (windowWidth * 0.9) / wImg * hImg
        if (contentImgHeight >= 240){
          contentImgHeight = 240
        }

        that.setData({
          contentImgHeight,
          canvasHeight: contentImgHeight + CONTENT_BODY_SIZE
        })
      }
    })
  },

  initQrBox(showUInfo = true) {
    const {envVersion = ''} = wx.getAccountInfoSync().miniProgram || {}
    breakIn({name: 'initVersionInfo'})

    this.setData({
      showUserInfo: showUInfo,
      envVersion
    }, () => {
      return this.getWXACodeImg()
    })
  },

  async getWXACodeImg() {
    vLog.log('PALETTE getWXACodeImg >>>>', this.data)
    const {envVersion = '', opts = {}} = this.data || {}
    const {
      fromTab,
      listPath
    } = opts || {}

    const posterParams = {
      routerPage: `${listPath}`,
      shareFrom: EnterSource.POSTER,
      params: opts,
      fromTab,
      floatShare: true,
      LIST_POSTER: 'LIST_POSTER',
    }

    const channelParams = {
      params: `${qs.stringify(posterParams)}`,
      day: -1,
    }
    let _scene = `${getUserId() || ''}`
    vLog.log('PALETTE channelParams  >>>>>', channelParams)
    const {success: pSucc, param: pData, msg: pMsg} = await addSceneParam(channelParams)
    vLog.log('PALETTE addSceneParam  >>>>>', pSucc, pData, pMsg)
    if (!pSucc){
      interaction.hideLoading()
      interaction.showToast(pMsg || '')
      return
    }
    _scene = pData

    const params = {
      sourceCode: global.SOURCE_CODE,
      scene: `${_scene}`,
      page: GLOBAL_START_PATH,
      env_version: envVersion
    }

    const {msg, success, param} = await getWXACode(params)
    interaction.hideLoading()
    vLog.log('PALETTE  getWXACodeImg >>>>', msg, success, param)
    if (!success){
      interaction.hideLoading()
      interaction.showToast(msg || '')
      return
    }
    this.setData({
      qrCode: param
    }, () => this.doDrawCanvas())
  },

  doDrawCanvas() {
    vLog.log('PALETTE doDrawCanvas this.data >>>', this.data)
    this.setData({
      paintPalette: new PosterCard({...this.data}).palette(),
      building: false
    });
  },

  onImgOK(e = {}) {
    vLog.log('PALETTE onImgOK e >>>', e)
    const {path: saveImg = ''} = e.detail || {}
    this.setData({
      saveImg
    });
  },

  onDownloadImg(e = {}) {
    vLog.log('PALETTE onDownloadImg e >>>>', e, this.data)
    const {saveImg = ''} = this.data || {}
    let self = this
    if (!saveImg){
      return interaction.showToast('二维码加载中，请稍后再试')
    }

    wx.getSetting({
      success(res) {
        if (!res.authSetting['scope.writePhotosAlbum']){
          wx.authorize({
            scope: 'scope.writePhotosAlbum',
            success() {
              // 这里是用户同意授权后的回调
              self.saveImage()
            },
            fail() { // 这里是用户拒绝授权后的回调
              self.openSetting()
            }
          })
        } else { // 用户已经授权过了
          self.saveImage()
        }
      }
    })
  },

  // 前往设置页面
  openSetting() {
    wx.showModal({
      title: '提示',
      content: '是否前往设置页面开启相册授权？',
      cancelText: '否',
      confirmText: '是',
      success(res) {
        if (res.confirm){
          wx.openSetting({})
        }
      }
    })
  },

  // 保存图片到本地
  saveImage() {
    const {saveImg = ''} = this.data
    wx.saveImageToPhotosAlbum({
      filePath: saveImg,
      success: function() {
        interaction.showToast('保存成功')
      },
    })
  },

  onChangeShow(e = {}) {
    const {wInfo} = this.data
    if (!wInfo || isEmptyObject(wInfo)){
      return interaction.showToast('当前为游客，请登录后再试')
    }

    vLog.log('PALETTE onChangeShow e >>>>', e)
    this.setData({
      showUserInfo: !this.data.showUserInfo,
      building: true,
      progress: 0,
    }, () => this.initQrBox(this.data.showUserInfo))
  },

  onBgTouch(e = {}) {
    vLog.log('PALETTE onBgTouch e >>>', e)
    const {y} = e.detail || {}

    if (180 <= y && y <= 620){
      return
    } else {
      return wx.navigateBack({})
    }
  }
});

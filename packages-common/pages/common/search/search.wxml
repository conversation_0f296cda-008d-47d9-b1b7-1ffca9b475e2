<!--pages/common/search.wxml-->
<nb-page
    showNavBar="{{true}}"
    navShowBack="{{true}}"
    navTitle="{{title || '搜索'}}"
    showTabBar="{{false}}">
  <view class="content-nav" wx:if="{{contentInfo.length>1}}">
    <scrollable-tabview
        class=".current-item {{index==TabCur?'text-black':''}}"
        tabs='{{contentInfo}}'
        bind:tabNavClick='tabSelect'
        backgroundColor="#fff"
        activeColor="#333"
        color="#999"
        isFromList="true"
        barBgColor="{{$state.themeColor}}"
        currentIndex="{{TabCur}}"/>
  </view>
  <x-scroll-view
      enableRefresh="{{true}}"
      elementHeight="{{contentHeight}}"
      refreshing="{{refreshing}}"
      loadmoring="{{loadmoring}}"
      nomore="{{nomore}}"
      enableLoadMore="{{true}}"
      resetting="{{true}}"
      refreshTopDistance="{{contentInfo.length>1?3:1}}"
      hasTabBarInfo="{{contentInfo.length>1}}"
      bindpulluploadmore="handleLoadMore"
      bindpulldownrefresh="handlePullDownRefresh">
    <search placeholder="请输入关键词"
            focus="{{onFocus}}"
            readonly="readonly"
            showAction="{{type != 'STOCK'}}"
            value="{{searchInput}}"
            bind:search="onSearch"
            bind:change="onChange"
            bind:cancel="onCancel"
            field-class="field-class"
            input-class="{{type != 'STOCK' ? 'input-class' : 'input-class-widthout-cancel' }}"
            marginTop="{{contentInfo.length>1?modePlat?'-6':'50':'0'}}"
    />
    <view wx:if="{{type === 'STOCK'}}" class="picker-view">
      <view class="picker" bind:tap="onPickerShow">
        <text class="picker-text" style="color: {{ pickerIndex === -1? '#999999' : '#333333'}};">{{enddate}}</text>
        <image src="../../../../imgs/icon/<EMAIL>" class="picker-icon" mode="aspectFill"/>
      </view>
      <view class="search-button" bind:tap="onSearchAction">
        <text class="search-text">搜索</text>
      </view>
    </view>
    <view class="box">
      <view class="content-list"
            style="margin-top: 14px"
            wx:if="{{currList.length>0 && type === 'ARTICLE'}}">
        <view
            wx:for="{{currList}}"
            wx:for-item="item"
            wx:for-index="id"
            wx:key="id">
          <news
              data="{{item}}"
              bind:onItemClick="onHandleAction"
              isLast="{{id === currList.length-1}}"
              elementType="LIST"
              nomore="{{nomore}}"
          />
        </view>
      </view>
      <view class="content-media-list"
            wx:if="{{currList.length>0 && type === 'LIVE'}}"
            wx:for="{{currList}}"
            wx:for-item="item"
            wx:for-index="id"
            wx:key="id">
        <live
            item="{{item}}"
            data-type="{{'AppAdvLive'}}"
            data-item="{{item}}"
            bind:tap="onHandleAction"
            isLast="{{id === currList.length-1}}"
            elementType="LIST"
            nomore="{{nomore}}"
        />
      </view>
      <view class="content-media-list"
            wx:if="{{currList.length>0 && type === 'MULTI_COURSE'}}"
            wx:for="{{currList}}"
            wx:for-item="item"
            wx:for-index="id"
            wx:key="id">
        <multimedia
            data="{{item}}"
            bind:onItemClick="onHandleAction"
            isLast="{{id === currList.length-1}}"
            elementType="LIST"
            nomore="{{nomore}}"
        />
      </view>
      <view class="content-poster-list"
            wx:if="{{currList.length>0 && type === 'POSTER'}}">
        <view
            wx:for="{{currList}}"
            wx:for-item="item"
            wx:for-index="id"
            wx:key="id">
          <poster
              data="{{item}}"
              data-type="{{'AppAdvPosterInfo'}}"
              data-item="{{item}}"
              bind:tap="onHandleAction"
              isLast="{{id === currList.length-1}}"
              elementType="LIST"
              nomore="{{nomore}}"
          />
        </view>
      </view>
      <view class="content-media-list"
            wx:if="{{currList.length>0 && type === 'MARKET_PLAN'}}"
            wx:for="{{currList}}"
            wx:for-item="item"
            wx:for-index="id"
            wx:key="id">
        <marketPlan
            data="{{item}}"
            bind:onItemClick="onHandleAction"
            isLast="{{id === currList.length-1}}"
            elementType="LIST"
            nomore="{{nomore}}"
        />
      </view>
      <view class="content-media-list"
            wx:if="{{currList.length>0 && type === 'UNSCRAMBLE'}}"
            wx:for="{{currList}}"
            wx:for-item="item"
            wx:for-index="id"
            wx:key="id">
        <unscramble
            data="{{item}}"
            bind:onItemClick="onHandleAction"
            isLast="{{id === currList.length-1}}"
            elementType="LIST"
            nomore="{{nomore}}"
        />
      </view>
      <view class="content-stock-list"
            wx:if="{{currList.length>0 && type === 'STOCK'}}"
            wx:for="{{currList}}"
            wx:for-item="item"
            wx:for-index="id"
            wx:key="id">
        <stock
            data="{{item}}"
            bind:onItemClick="onHandleAction"
            isFirst="{{id == 0}}"
            isLast="{{id === currList.length-1}}"
            elementType="LIST"
            nomore="{{nomore}}"
        />
      </view>
      <view class="content-media-list"
            wx:if="{{currList.length>0 && type === 'REPORT'}}"
            wx:for="{{currList}}"
            wx:for-item="item"
            wx:for-index="id"
            wx:key="id">
        <report
            data="{{item}}"
            bind:onItemClick="onHandleAction"
            isLast="{{id === currList.length-1}}"
            elementType="LIST"
            nomore="{{nomore}}"
        />
      </view>
    </view>
  </x-scroll-view>
  <view
      wx:if="{{isShowPicker}}"
      class="picker-modal"
      style="height: {{contentHeight}}px;">
    <view class="picker-empty" bind:tap="onPickerCancel"/>
    <searchPicker
        class="search-picker-view"
        showToolbar="{{true}}"
        title="{{'持仓日期'}}"
        columns="{{ fundDateList }}"
        defaultIndex="{{ pickerIndex }}"
        value="{{ enddate }}"
        bind:confirm="onPickerConfirm"
        bind:cancel="onPickerCancel"
    />
  </view>
  <view
      wx:if="{{currList.length == 0}}"
      class="empty-class"
      style="padding-top:{{contentInfo.length>1?'300px':'200px'}}">
    <emptyBlock wx:if="{{!refreshing}}" tips="暂无搜索结果"/>
  </view>
  <polymers></polymers>
  <van-toast id="van-toast"/>
</nb-page>

// pages/common/search.js
import {
  systemtInfo,
  global,
  util,
  enums,
  eventName,
  storage,
  interaction,
  vLog
} from '../../../../common/index';

import {
  getNewsList,
  getMulitList,
  getMulitListLogin,
  getMarketPlanList,
  getHomeMarketPlanList,
  searchFunds,
  searchFundReport,
  getFundDateList,
  searchByStock
} from "../../../../common/nb/home";

import {
  getFirstPath,
  getSystemInfo,
  getTabBarList,
  getUser,
  getUserLogin,
  getUserRole,
  getVersionInfo
} from "../../../../common/utils/userStorage";

import { getLiveList } from "../../../../common/network/api";
import { loadFont } from '../../../config/font.config';

const { platform } = wx.getSystemInfoSync()

const dayJs = require('dayjs')

const {
  screenHeight,
  footHeight,
} = systemtInfo

const {
  SEND_EVENT_TO_POLYMERS,
  SEND_EVENT_TO_SHARE_LIST
} = eventName

const {
  rpx2px,
  isEmptyObject
} = util;

const { RealmType, LOGIN_VISITOR } = enums

const app = getApp()

function doDeCode(str = '') {
  if (str && `${str}`.startsWith('%')) {
    return decodeURIComponent(str)
  }

  return str
}

const SHOW_LIST_BY_EMPTY_KWS = [RealmType.UNSCRAMBLE, RealmType.REPORT]

Page({
  /**
   * 页面的初始数据
   */
  data: {
    modePlat: platform === "ios",

    searchInput: '',
    readonly: false,
    currList: [],
    categoryId: '',

    page: 0,
    pageSize: 50,
    type: '',
    contentInfo: [],
    defCode: "",

    contentHeight: rpx2px(screenHeight - footHeight),
    refreshing: false,
    loadmoring: false,
    nomore: false,
    canShowRes: '',

    fromTab: '',
    fromPage: '',
    redirectBack: '',

    TabCur: 0,
    envVersion: '',
    title: "",
    fundDateList: [],
    pickerIndex: -1,
    enddate: '请选择持仓日期',
    isShowPicker: false,
    onFocus: true,
    fromShareList: false,

    sysInfo: {},
    wInfo: {},
    hasLogin: false,
    userRole: null
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options = {}) {
    vLog.log('SEARCH options >>', options)
    loadFont()

    let {
      channelList = '',
      categoryId = '',
      type = '',
      orgId = '',
      isNeedOrgid = false,
      placeholder = '',
      fromTab = '',
      fromPage = '',
      redirectBack = '',
      keyword,
      fromShareList = false,
      canShowRes = ''
    } = options || {}

    if (!options.hasOwnProperty('fromShareList')) {
      storage.setStorage(global.STORAGE_GLOBAL_PREVIOUS_PARAMS, options)
    }
    channelList = doDeCode(channelList)
    placeholder = doDeCode(placeholder)

    const sysInfo = getSystemInfo()
    const wInfo = getUser()
    const userRole = getUserRole()
    const hasLogin = getUserLogin()
    let defCode = sysInfo?.staff?.clientParams?.defFundCode || ''

    if (channelList) {
      let contentInfo = []
      JSON.parse(channelList)
        .map(item => {
          if (!item.name) {
            item.name = item.label
          }
          contentInfo.push(item)
        })
      this.setData({
        contentInfo
      })
    }
    this.setData({
      categoryId,
      type,
      orgId,
      isNeedOrgid,
      title: placeholder || '',
      fromTab,
      redirectBack,
      onFocus: keyword == undefined || !SHOW_LIST_BY_EMPTY_KWS.includes(type),
      searchInput: keyword || '',
      fromShareList,
      sysInfo,
      wInfo,
      hasLogin,
      userRole,
      canShowRes,
      fromPage,
      defCode
    }, () => {
      if (keyword != undefined && SHOW_LIST_BY_EMPTY_KWS.includes(type)) {
        this.onSearch()
      }
      this.requestFundDateList(defCode)
    })

    this.getCurrentVersion()
  },

  getCurrentVersion() {
    const _versionInfo = getVersionInfo()
    if (_versionInfo && !isEmptyObject(_versionInfo)) {
      const { envVersion = '', } = _versionInfo
      this.setData({
        envVersion
      })
    }
  },

  async requestFundDateList(fundcode = '') {
    const { data } = await getFundDateList({ fundcode })
    if (data && data.length > 0) {
      this.setData({
        fundDateList: data,
        pickerIndex: -1,
      })
    }
  },

  onChange(e = {}) {
    this.setData({
      searchInput: e.detail
    })
  },

  /**
   * 点击搜索结果
   */
  onHandleAction(e = {}) {
    vLog.log('SEARCH onHandleAction e,this.data >>>', e, this.data)
    vLog.log('SEARCH app.globalData  >>>', app.globalData)
    // const {fromShareList} = this.data

    // if (fromShareList && fromShareList === 'true'){
    //   const {detail} = e || {}
    //   return app.globalData.emitter.emit(SEND_EVENT_TO_SHARE_LIST, {...detail, fromShareList: true})
    // }

    if (!isEmptyObject(e)) {
      const {
        dataset: {
          item = {},
          type = ''
        }
      } = e.currentTarget || {}
      if (type) {
        const passParams = {
          ...item,
          action: `action://share/${type}`
        }
        return app.globalData.emitter.emit(SEND_EVENT_TO_POLYMERS, { detail: passParams })
      }
      return app.globalData.emitter.emit(SEND_EVENT_TO_POLYMERS, e)
    }
  },

  onSearch() {
    const { searchInput = "", categoryId = "", type = "" } = this.data
    vLog.log('SEARCH onSearch this.data >>>', this.data)
    getApp().sensors.track('search', {
      keyword: searchInput
    })
    return this.getDetail(type, categoryId, searchInput)
  },

  async getDetail(type = '', id = '', searchInput = '') {
    vLog.log('SEARCH getDetail type, id, searchInput >>>', type, id, searchInput)
    const { page, pageSize, orgId, isNeedOrgid, enddate, pickerIndex, canShowRes = '', categoryId = '' } = this.data
    const wInfo = storage.getStorage(global.STORAGE_GLOBAL_WECHAT_INFO) || {}

    if (page == 0) {
      this.setData({
        currList: []
      });
    }

    let params = {
      page,
      pageSize,
      title: searchInput
    }

    const gfParams = { page: page + 1, pageSize }
    let returnEmpty = false
    if (canShowRes && canShowRes == 'false') {
      returnEmpty = true
    }

    //资讯
    if (type === RealmType.ARTICLE) {
      params.localCategoryId = id
      const { msg, param, success } = await getNewsList(params)
      this.getData(msg, returnEmpty ? [] : param?.content, success, true)
    }

    //直播
    else if (type === RealmType.LIVE) {
      const { firstOrgId = '' } = wInfo || {}
      params = {
        start: page,
        limit: pageSize,
        title: searchInput,
        channelIds: [`${firstOrgId}`]
      }
      if (!params.start) {
        params.start = 1
      }
      if (!firstOrgId) {
        delete params.channelIds
      }
      if (categoryId) {
        params.wbsCategoryId = categoryId
      }
      const { msg, rows, code } = await getLiveList(params)
      this.getData(msg, returnEmpty ? [] : rows, code === 0, false)
    }

    //音视频
    else if (type === RealmType.MULTI_COURSE) {
      const userRole = getUserRole()
      const hasLogin = !LOGIN_VISITOR.includes(userRole * 1)
      const getList = hasLogin ? getMulitListLogin : getMulitList
      params.categoryId = id
      const { msg, data, success } = await getList(params)
      this.getData(msg, returnEmpty ? [] : data.content, success, true)
    }

    //营销方案
    else if (type === RealmType.MARKET_PLAN) {
      params.categoryId = id
      if (isNeedOrgid === 'true' && orgId) {
        params.orgId = orgId
      }
      const api = id ? getMarketPlanList : getHomeMarketPlanList
      const { msg, param, success } = await api(params)
      this.getData(msg, returnEmpty ? [] : param.content, success)
    }

    //基金解读
    else if (type === RealmType.UNSCRAMBLE) {
      gfParams.keywords = searchInput
      vLog.log('SEARCH gfParams >>>>>', gfParams)
      const { msg, data } = await searchFunds(gfParams)
      this.getData(msg, data, data, true)
    }

    //股票
    else if (type === RealmType.STOCK) {
      if (pickerIndex === -1) {
        return interaction.showToast('请选择持仓日期')
      }
      gfParams.fundcode = searchInput
      gfParams.enddate = dayJs(enddate).format('YYYYMMDD')
      vLog.log('SEARCH gfParams >>>>>', gfParams)
      const { msg, data } = await searchByStock(gfParams)
      this.getData(msg, data, data, true)
    }

    //运作报告
    else if (type === RealmType.REPORT) {
      gfParams.keywords = searchInput
      const { msg, data } = await searchFundReport(gfParams)
      if (typeof searchInput === 'string' && !`${searchInput}`.trim()) {
        this.setData({
          searchInput: ''
        })
      }

      this.getData(msg, data, data, true)
    }
  },

  getData(msg = '', _currList = [], success = '', showTime = '') {
    vLog.log('SEARCH getData msg >>>', msg)
    vLog.log('SEARCH getData _currList = [] >>>', _currList)
    vLog.log('SEARCH getData success >>>>', success)
    vLog.log('SEARCH getData showTime >>>', showTime)

    let { pageSize, page, currList } = this.data || {}
    vLog.log('SEARCH getData this.data  >>>', this.data)
    this.setData({
      refreshing: false,
      loadmoring: false
    });

    if (!success) {
      return interaction.showToast(msg || '暂无搜索结果')
    }

    if (_currList && _currList.length < pageSize) {
      this.setData({
        nomore: true
      });
    }

    let _cList = []
    if (showTime) {
      _currList.forEach((cItem) => {
        const {
          timeCreated = '',
          netDate = '',
          fundcode = '',
          fundname = '',
          f_prt_enddate = '',
          fundName = '',
          fundCode = ''
        } = cItem || {}

        const props = {
          ...cItem,
          timeCreatedStr: dayJs(timeCreated || new Date()).format('YYYY-MM-DD'),
          enddateStr: netDate ? dayJs(netDate).format('MM-DD') : '-',
          isTotal: fundcode == '合计' && fundname == '合计',
          dateValue: f_prt_enddate || '',
          fundname: fundname || fundName,
          fundcode: fundcode | fundCode
        }
        _cList.push(props)
      })
    } else {
      _cList = [].concat(_currList)
    }

    let _rcList = _cList.reverse()
    for (let i = 0; i < _rcList.length; i++) {
      const { isTotal } = _rcList[i] || {}
      if (i === 0) {
        _rcList[0]['isItemLast'] = true
      }

      if ((isTotal && i + 1 < _rcList.length)) {
        _rcList[i + 1]['isItemLast'] = true
      }
    }
    _cList = _rcList.reverse()

    if (page > 0) {
      currList = currList.concat(_cList);
      this.setData({ currList });
    } else {
      this.setData({
        currList: _cList
      });
    }
  },

  onCancel() {
    wx.navigateBack({})
  },

  //加载更多
  handleLoadMore: function () {
    const { contentInfo = [], type, searchInput, categoryId = '' } = this.data || {}
    if (!contentInfo.length) {
      this.setData({
        refreshing: false
      })
      return
    }

    this.data.page++;
    this.setData({
      nomore: false,
      loadmoring: true,
      page: this.data.page
    }, () => {
      return this.getDetail(type, categoryId, searchInput)
    });
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  handlePullDownRefresh: function () {
    const { type, contentInfo = [], searchInput, categoryId } = this.data || {}
    if (!contentInfo.length) {
      return this.setData({ refreshing: false })
    }

    this.data.page = 0;
    this.setData({
      nomore: false,
      currList: [],
      page: this.data.page
    }, () => {
      if (!searchInput) {
        this.setData({
          refreshing: false,
        })
        return
      }

      return this.getDetail(type, categoryId, searchInput)
    })
  },

  // 切换一级 tab
  tabSelect(e = {}) {
    const { contentInfo, defCode, fundDateList, isShowPicker } = this.data
    if (isShowPicker) {
      this.onPickerCancel()
    }
    const { index = 0 } = e.detail || {}

    const type = contentInfo[index]?.type || ''
    this.data.page = 0;

    this.setData({
      TabCur: index,
      currList: [],
      type,
      page: this.data.page,
      searchInput: '',
      nomore: false
    })

    vLog.log('SEARCH tabSelect this.data >>>', fundDateList.length)
    if (fundDateList.length <= 0) {
      return this.requestFundDateList(defCode)
    }
  },

  onUnload() {
    const { fromTab, redirectBack } = this.data
    let backPath = getFirstPath()
    const tabList = (getApp().globalData.tabList)?.length ? getApp().globalData.tabList : getTabBarList() || []
    if (tabList && tabList.length) {
      let bTarget = tabList.find(item => item && item.tabName == fromTab)
      if (bTarget && !isEmptyObject(bTarget)) {
        backPath = bTarget?.tabPath
      }
    }

    if (redirectBack) {
      return wx.switchTab({
        url: `${backPath}`
      })
    }
  },

  //针对股票 带日期搜索，不支持搜空数据
  onSearchAction: function () {
    const { searchInput = "", categoryId = "", type = "" } = this.data
    if (searchInput.length > 0) {
      return this.getDetail(type, categoryId, searchInput)
    }
  },

  onPickerShow() {
    this.setData({
      isShowPicker: true,
    })
  },

  onPickerConfirm(e = {}) {
    const { detail } = e || {}
    this.setData({
      pickerIndex: detail.index || 0,
      enddate: detail.value || '',
      isShowPicker: false
    })
  },

  onPickerCancel() {
    this.setData({
      isShowPicker: false
    })
  }
})

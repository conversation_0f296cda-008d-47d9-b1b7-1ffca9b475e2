/* pages/common/search.wxss */
.list-container{
    background-color: #fff;
}

.content-nav{
    width: 100%;
    z-index: 9999;
    position: fixed;
    height: 100rpx;
    overflow: hidden;
}

.search-block{
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;
    background-color: #fff;
    max-height: 15vw;
    padding: 12rpx 24rpx;
    margin: 28rpx 0;
    border-radius: 8vw;
    border: 1px solid #EEEEEE;
}

.search-content-tips{
    font-size: 14Px;
    color: #999999;
    font-weight: 400;
    margin-right: 10rpx;
    font-family: PingFangSC-Regular;
}

.search-icon{
    margin-bottom: -6rpx;
}

.field-class{
    display: flex;
    height: 30px;
    border-radius: 15px;
    background-color: #fff;
    align-items: center;
}

.input-class{
    width: 65vw !important;
}

.input-class-widthout-cancel{
    width: 73vw !important;
}

.empty-class{
    width: 100%;
    height: 100%;
    padding-top: 200px;
    background-color: #fff;
}

.content-list{
    display: flex;
    flex-direction: column;
    background-color: #fff;
    border-radius: 8rpx;
    margin: 0 24rpx 48rpx;
}

.content-poster-list{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}

.content-media-list{
    display: flex;
    flex-direction: column;
    background-color: #fff;
    border-radius: 20rpx;
    margin: 28rpx;
    overflow: hidden;
}

.content-stock-list{
  display: flex;
  flex-direction: column;
  /* background-color: #fff; */
}

.box{
    margin-bottom: 40px;
}
.picker-view{
  padding: 0px 14px;
  background: #ffffff;
  border: 1pt solid transparent;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.picker{
  width: 100%;
  display: flex;
  flex-direction: row;
  height: 60rpx;
  border-radius: 30rpx;
  border: 1pt solid #EEEEEE;
  align-items: center;
  padding: 0px 14px; 
  background-color: #fff;
}
.picker-text{
  font-size: 26rpx;
  color: #333333;
  font-family: PingFangSC-Regular;
  flex: 1;
}

.search-text{
  font-size: 28rpx;
  color: #fff;
  font-family: PingFangSC-Medium;
  /* flex: 1; */
}
.picker-icon{
  width: 18rpx;
  height: 10rpx;
}
.search-button{
  width: 500rpx;
  display: flex;
  height: 60rpx;
  background: #E8340F;
  border-radius: 30rpx;
  margin: 14px;
  align-self: center;
  align-items: center;
  justify-content: center;
}
.picker-modal{
  position: absolute;
  left: 0;
  display: flex;
  flex-direction: column;
  width: 100vw;
  align-items: center;
  justify-content: space-between;
  z-index: 999;
}

.picker-empty{
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100vw;
  height: calc(100% + 30px);
  background-color: rgba(0, 0, 0, 0.75);
  margin-bottom: -20px;
}

.search-picker-view{
  width: 100%;
  z-index: 9999;
}


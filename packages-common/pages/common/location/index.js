import {global, interaction, storage, util, vLog} from "../../../../common/index.js";
import {getContentHeight} from "../../../../common/const/systeminfo";
import {searchPlaceByPoint} from "../../../../common/nb/home";
import {setChannelNameTips, setChannelParamsInfo} from "../../../../common/utils/userStorage";

const {rpx2px} = util

const SEARCH_BAR_HEIGHT = 50
const pageSize = 20

const app = getApp()

Page({
  data: {
    tabInfo: [
      {label: '关键字搜索', name: "关键字搜索", action: 'MAP'},
      {label: '自定义添加', name: "自定义添加", action: 'SELF'}
    ],
    location: {
      latitude: 0,
      longitude: 0
    },
    loading: true,
    showTabLine: true,
    currentIndex: 0,
    activeColor: getApp()?.store.$state.themeColor || '#f04b28ff',
    color: '#696969',
    contentHeight: rpx2px(getContentHeight(true, true, false)),
    searchBarSize: SEARCH_BAR_HEIGHT,
    searchBlockSize: rpx2px(getContentHeight(true, true, false)) - SEARCH_BAR_HEIGHT,

    doAuth: false,
    addChannelName: '',
    keyword: '',
    pageNo: 1,
    currList: [],
    isLastPage: true,
    loadingMore: false,
    totalElements: 0,
    totalPages: 0,
    searchBtn: 'location-search-btn-normal',

    submitWay: ''
  },

  onLoad(options) {
    vLog.log('LOCATION options >>>>', options)
    let doAuth = !!storage.getStorage(global.STORAGE_GLOBAL_AUTH_LOCATION)
    this.setData({
      doAuth
    }, () => this.onGetFuzzyLocation())
  },

  onShow() {
    vLog.log('LOCATION onShow')
    const {doAuth} = this.data
    let that = this
    if (!doAuth){
      vLog.log('LOCATION onShow wx.getSetting')
      wx.getSetting({
        success(res) {
          console.log('LOCATION getSetting  res >>>', res)
          const {authSetting = {}, errMsg = ''} = res || {}
          if (`${errMsg}`.includes('ok')){
            if (authSetting.hasOwnProperty('scope.userFuzzyLocation') && authSetting['scope.userFuzzyLocation']){
              that.setData({
                doAuth: true
              }, () => that.onGetFuzzyLocation())
            }
          }
        }
      })
    }
  },

  onClickTab(e = {}) {
    vLog.log('LOCATION onClickTab e >>>>', e)
    const {currentIndex} = this.data || {}
    const {
      dataset: {
        index,
      }
    } = e.currentTarget || {}
    if (currentIndex === index){
      return
    }

    this.setData({currentIndex: index})
  },

  bindTextAreaInput(e = {}) {
    const {value = ''} = e.detail || {}
    let addChannelName = `${value}`.trim()
    this.setData({
      addChannelName
    })
  },

  bindTextAreaBlur(e = {}) {
    vLog.log(e.detail.value)
  },

  onAddChannelTips() {
    const {addChannelName} = this.data
    setChannelNameTips(addChannelName)
    this.setData({
      submitWay: 'WRITE'
    }, () => this.onCallback())
  },

  async onGetFuzzyLocation() {
    interaction.showLoading('加载中...')
    let that = this
    await wx.getFuzzyLocation({
      type: 'wgs84',
      success(res) {
        vLog.log('LOCATION res >>>>', res).report()
        const {latitude, longitude} = res || {}
        storage.setStorage(global.STORAGE_GLOBAL_LATITUDE, latitude)
        storage.setStorage(global.STORAGE_GLOBAL_LONGITUDE, longitude)
        storage.setStorage(global.STORAGE_GLOBAL_AUTH_LOCATION, true)
        that.setData({
          doAuth: true
        })
      },
      fail(err) {
        vLog.error('LOCATION err >>>>', err).report()
        interaction.showToast('获取位置信息失败')
        storage.setStorage(global.STORAGE_GLOBAL_AUTH_LOCATION, false)
        that.setData({
          doAuth: false
        })
      },
      complete() {
        that.setData({loading: false})
        interaction.hideLoading()
      }
    })
  },

  /**
   * 渠道输入清除
   */
  onCClear() {
    this.setData({
      keyword: '',
      searchBtn: 'location-search-btn-normal'
    })
  },

  /**
   * 渠道搜索输入
   */
  onCChange(e = {}) {
    let keyword = `${e?.detail}`.trim()
    this.setData({
      keyword,
      searchBtn: keyword ? 'location-search-btn' : 'location-search-btn-normal'
    })
  },

  /**
   * 渠道搜索
   */
  async onCSearch(e = {}) {
    vLog.log('LOCATION onCSearch e,this.data >>>', e, this.data)
    let {keyword, doAuth} = this.data
    keyword = `${keyword}`.trim()

    if (!keyword){
      return
    }

    if (!doAuth){
      return interaction.showToast('请授权位置信息')
    }

    let _currList = []
    interaction.showLoading('加载中...')
    const {code, msg, data, success} = await searchPlaceByPoint({
      keyword,
      lat: storage.getStorage(global.STORAGE_GLOBAL_LATITUDE),
      lng: storage.getStorage(global.STORAGE_GLOBAL_LONGITUDE),
      page: 1,
      pageSize
    })
    vLog.log('LOCATION onCSearch code, msg, data, success >>>', code, msg, data, success)
    if (!success){
      interaction.hideLoading()
      this.setData({
        currList: [],
        pageNo: 1,
        isLastPage: true,
        loadingMore: false,
        totalElements: 0,
        totalPages: 0,
      })
      return interaction.showToast(msg || '')
    }

    const {content = [], totalElements, last, totalPages} = data || {}
    if (success){
      _currList = [].concat(content)

      this.setData({
        currList: _currList,
        isLastPage: last,
        totalElements,
        totalPages,
        loadingMore: false,
      })
    }
    interaction.hideLoading()
  },

  onHandleLoadMore(refresh = false) {
    const {isLastPage, totalPages, currList, totalElements, pageNo} = this.data
    //已全部显示
    if (isLastPage){
      this.setData({
        loadingMore: false,
      })
      return
    }

    if (pageNo >= totalPages){
      this.setData({
        loadingMore: false,
      })
      return
    }

    if (currList.length >= totalElements){
      this.setData({
        loadingMore: false,
      })
      return
    }

    this.data.pageNo++
    if (typeof refresh === 'boolean' && Boolean(refresh)){
      this.data.pageNo = 0
    }
    this.setData({
      loadingMore: true,
      pageNo: this.data.pageNo
    }, () => this.loadNextPage())
  },

  async loadNextPage() {
    const {pageNo, currList, keyword} = this.data
    vLog.log('LOCATION loadNextPage this.data >>>>', this.data)

    let _currList = [].concat(currList)
    let params = {
      page: pageNo,
      pageSize,
      keyword,
      lat: storage.getStorage(global.STORAGE_GLOBAL_LATITUDE),
      lng: storage.getStorage(global.STORAGE_GLOBAL_LONGITUDE),
    }

    interaction.showLoading('加载中...')
    const {success, data, msg, code} = await searchPlaceByPoint(params)
    vLog.log('LOCATION loadNextPage getQueryOrgNode >>>>', success, data, msg, code)
    interaction.hideLoading()
    if (!success){
      this.setData({
        loadingMore: false,
      })
      return interaction.showToast(msg || '')
    }

    const {content = [], totalElements, last, totalPages} = data || {}
    _currList = _currList.concat(content)
    this.setData({
      loadingMore: false,
      isLastPage: last,
      totalElements,
      totalPages,
      currList: _currList
    })
  },

  onItemClick(e) {
    vLog.log('LOCATION e >>>>', e)
    const {
      dataset: {
        value = ''
      }
    } = e.currentTarget || {}

    const {currList} = this.data || {}
    let targetArea = currList.find(item => item && item.id == value)
    vLog.log('LOCATION targetArea >>>', targetArea)
    setChannelParamsInfo(targetArea)
    this.setData({
      submitWay: 'LOCATION'
    }, () => this.onCallback())
  },

  onUnload() {
    const {submitWay} = this.data || {}
    app.globalData.submitWay = submitWay
  },

  onCallback() {
    return setTimeout(() => {
      wx.navigateBack({})
    }, 300)
  },

  openSetting() {
    vLog.log('LOCATION openSetting')
    return wx.openSetting({})
  }
});

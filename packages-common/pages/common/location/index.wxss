.nav{
    color: #999;
    font-size: 32rpx;
    background-color: #fff;
    white-space: nowrap;
    display: flex;
    flex-direction: row;
    border-bottom: 1px solid #eee;
    border-top-left-radius: 10rpx;
    border-top-right-radius: 10rpx;
}

.nav .current-item{
    display: flex;
    flex: 1;
    flex-direction: column;
    height: 100rpx;
    justify-content: center;
    align-items: center;
    position: relative;
}

.nav .current-item .underline{
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 50rpx;
    height: 4rpx;
}

.tab-block{
    position: absolute;
    left: 0;
    top: 0;
    z-index: 999;
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100vw;
    height: 50Px;
    background-color: #fff;
}

.tab-tips{
    display: flex;
    flex-direction: column;
    width: 50vw;
    height: 50Px;
    align-items: center;
    justify-content: center;
    max-width: 100%;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 32rpx;
}

.indicator{
    margin: -2rpx auto 0 auto;
    width: 64rpx;
    height: 6rpx;
    border-radius: 4rpx;
}

.location-search-bar{
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100vw;
    background-color: #f2f2f2;
}

.location-add-bar{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100vw;
}

.section{
    display: flex;
    flex-direction: column;
    width: 94vw;
    min-height: 200px;
    margin: 3vw 20px;
    padding: 14px 10px;
    background-color: #f3f3f3;
    border-radius: 8rpx;
    align-self: center;
    font-size: 16Px;
    color: #434343;
}

.location-submit-btn{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 94vw;
    height: 40Px;
    border-radius: 10rpx;
    margin-top: 14px;
    font-size: 16Px;
    color: #fff;
}

.mock-block{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100vw;
    height: 15vh;
    font-size: 18Px;
    color: #333;
}

.location-item{
    display: flex;
    flex-direction: column;
    width: 100vw;
    padding: 10px 14px;
    background-color: #fff;
    border-bottom: 1rpx solid #eee;
}

.location-scroll-container{
    background-color: #fff;
}

.area-scroll-container{
    background-color: #fff;
}

.item-block{
    display: flex;
    flex-direction: column;
    width: 100%;
    justify-content: center;
}

.item-title{
    font-size: 14Px;
    color: #333;
    flex: 1;
    font-weight: bold;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
}

.item-content{
    font-size: 14Px;
    color: #696969;
}

.channel-load-more{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 20px 0;
}

.load-more-gif{
    width: 20px;
    height: 20px;
    margin-right: 10rpx;
}

.load-more-tips{
    font-size: 14Px;
    color: #696969;
}

.location-search-btn{
    font-size: 14Px;
    color: #333;
    font-weight: 400;
}

.location-search-btn-normal{
    font-size: 14Px;
    color: #696969;
}

.auth-location-btn{
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 20vw;
    margin-top: 50%;
    align-items: center;
    justify-content: center;
}

.auth-btn{
    width: 100%;
    min-width: 92vw;
    margin-left: 14px;
    margin-right: 14px;
    padding: 14px;
    color: #fff;
    font-size: 14Px;
    border-radius: 50px;
}

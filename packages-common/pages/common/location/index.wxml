<nb-page
    navShowBack="{{true}}"
    contentUseView="{{false}}"
    navTitle="补充网点"
    inHomePage="{{false}}"
    floatMarginSize="{{50}}"
    showTabBar="{{false}}"
    scrollEnabled="{{false}}">

  <view class="tab-block">
    <view wx:for="{{tabInfo}}" wx:key="index">
      <view
          class="tab-tips"
          style='font-weight:{{currentIndex == index ? "500": "normal"}};color: {{currentIndex == index ? activeColor : color}};'
          bindtap='onClickTab'
          data-index='{{index}}'
          data-value="{{item.action?item.action:index}}">
        {{item.label}}
      </view>
      <view
          wx:if="{{currentIndex == index}}"
          class='indicator'
          style='background-color:{{currentIndex == index ? activeColor : ""}};'
      />
    </view>
  </view>

  <scroll-view
      class="location-scroll-container"
      style="position:fixed;width:100%;left:0;height:{{contentHeight}}px;margin-top:50px; "
      enable-flex="{{true}}"
      enable-passive="{{true}}"
      scroll-with-animation
      enable-back-to-top
      scroll-y="true">
    <view wx:if="{{currentIndex==0}}" class="location-search-bar" style="height: {{searchBarSize}}px">
      <van-search
          style="display:flex;flex-direction:column;justify-content:center;width: 100%;height: {{searchBarSize}}px;"
          placeholder="请输入渠道关键字搜索"
          value="{{channelKey}}"
          input-align="left"
          marginTop="0"
          background="#f2f2f2"
          bind:search="onCSearch"
          bind:change="onCChange"
          bind:clear="onCClear"
      />
      <view
          slot="action"
          class="{{searchBtn}}"
          style="min-width: 12vw"
          bind:tap="onCSearch">
        搜索
      </view>
    </view>

    <view wx:if="{{currentIndex==1}}" class="location-add-bar">
      <view class="section">
        <textarea
            style="height: 200px;width: 100%;"
            value="{{addChannelName}}"
            bind:input="bindTextAreaInput"
            bind:blur="bindTextAreaBlur"
            placeholder="请输入支行/营业部名称"
        />
      </view>

      <view
          class="location-submit-btn"
          style="background-color: {{addChannelName?$state.themeColor:'#ccc'}}"
          bind:tap="onAddChannelTips">
        {{'确定'}}
      </view>
    </view>

    <scroll-view
        wx:if="{{currentIndex==0 && currList.length}}"
        class="area-scroll-container"
        style="position:fixed;width:100%;left:0;height:{{searchBlockSize}}px;"
        enable-flex="{{true}}"
        enable-passive="{{true}}"
        scroll-with-animation
        enable-back-to-top
        lower-threshold="{{50}}"
        scroll-top="{{0}}"
        bind:scrolltolower="onHandleLoadMore"
        scroll-y="true">
      <view
          class="location-item"
          wx:for="{{currList}}"
          wx:for-index="index"
          wx:for-item="item"
          wx:key="id">
        <view
            class="item-block"
            data-value="{{item.id?item.id:index}}"
            bind:tap="onItemClick">
          <view class="item-title">
            {{item.title || '-'}}
          </view>
          <view class="item-content">
            {{item.address || '-'}}
          </view>
        </view>
      </view>

      <view wx:if="{{loadingMore}}" class="channel-load-more">
        <image src="../../../../imgs/gif/loading.gif" class="load-more-gif"/>
        <view class="load-more-tips">{{'正在加载更多...'}}</view>
      </view>
    </scroll-view>

    <view wx:if="{{currentIndex==0&&!doAuth && !loading}}" class="auth-location-btn">
      <button class='nb-btn block btn' bindtap="openSetting" style="background-color: transparent;border: none;">
        <view class="auth-btn" style='background-color:{{$state.themeColor}}'>{{'开启定位'}}</view>
      </button>
    </view>
  </scroll-view>
</nb-page>

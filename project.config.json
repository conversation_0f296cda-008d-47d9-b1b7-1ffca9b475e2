{"description": "项目配置文件，详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "cloudfunctionRoot": "cloudFunc/", "packOptions": {"ignore": [{"value": "cloudFunc", "type": "folder"}], "include": []}, "setting": {"urlCheck": false, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": true, "coverView": true, "nodeModules": true, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useIsolateContext": false, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "disableUseStrict": false, "minifyWXML": true, "showES6CompileOption": false, "useCompilerPlugins": false, "ignoreUploadUnusedFiles": true, "useStaticServer": true, "condition": false}, "compileType": "miniprogram", "libVersion": "2.21.4", "appid": "wx3501ac822988bb64", "projectname": "wbs-wechat-investor", "simulatorType": "wechat", "simulatorPluginLibVersion": {"qywx_simulator_plugin": "2.20.3"}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "skeleton-config": {"global": {"loading": "spin", "text": {"color": "#EEEEEE"}, "image": {"shape": "", "color": "#EFEFEF", "shapeOpposite": []}, "button": {"color": "#EFEFEF", "excludes": []}, "pseudo": {"color": "#EFEFEF", "shape": "circle", "shapeOpposite": []}, "excludes": [], "remove": [], "empty": [], "hide": [], "grayBlock": [], "cssUnit": "rpx", "decimal": 4, "showNative": false, "backgroundColor": "transparent", "mode": "auto", "templateName": "skeleton"}, "pages": {"pages/home/<USER>": {}, "pages/product/index": {}, "pages/accompany/index": {}, "pages/viewPoint/index": {}, "pages/mine/mine": {}}}, "condition": {}, "cloudfunctionTemplateRoot": "cloudfunctionTemplate/"}
import {userStorage} from '../../common/index.js'
import config from '../const/config.js'

const Store = require('../../lib/store.js')

let store = new Store({
  state: {
    //以下为自定义的全局状态，用法和页面中的data: {...} 一致。
    user: undefined,
    //全局UI属性
    config,
    // 全局色值
    themeColor: '#f04b28ff',
    // logo
    logo: '',
    // 是否有渠道封面
    hasChannelCover: false,
    // 渠道封面 - 系统配置
    channelCoverUrl: '',
    tabBar: {
      list: [],
      backgroundColor: '#FFFFFF',
      selectorColor: '#f04b28ff',
      color: '#CCCCCC'
    }
  },
  methods: {
    //全局方法。 page中调用 this.xxx() wxml中bindtap="xxx" 非页面js文件getCurrentPages().pop().xxx()
    goAnyWhere(e) {
      console.log('===== goAnyWhere e >>', e)
      wx.navigateTo({
        url: e.currentTarget.dataset.url
      })
    },

    getUser() {
      let user = getApp().store.$state.user
      //当前全局中不存在 分别从storage中取
      if (!user){
        user = userStorage.getUser()
        user && store.setState({
          user
        })
      }
      return user
    },

    goBack(param) {
      let pages = getCurrentPages()
      if (pages.length < 2){

      } else {
        wx.navigateBack({
          delta: param ? 2 : 1,
        })
      }
    }
  },

  pageListener: {
    async onLoad(options = {}) {
      console.log('store onload options >>>>', options)
    }
  }
})
module.exports = store

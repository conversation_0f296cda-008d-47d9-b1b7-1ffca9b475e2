//common
import Toast from '../miniprogram_npm/vant-weapp/toast/toast.js';

//const
import config from './const/config.js'
import * as enums from './const/enum.js'
import {
  openApiConfig,
  application,
  postV2Api,
  wbs,
} from './const/env.js'
import * as global from './const/global.js'
import * as systemtInfo from './const/systeminfo.js'
import * as reviewConfig from "./const/review";
import * as agencyConfig from './const/agency';

// network
import * as api from './network/api.js'
import * as requests from './network/requests.js'

// event
import Event from './event/index.js'
import * as eventName from './event/name.js'
import Emitter from './event/emitter'

// 页面交互
import interaction from './interaction/index.js'

// 产品业务逻辑 business
import convert from './business/convert.js'
import breakIn from "./business/breakin";
import userAction from './business/userCenter.js'
import shapeShift from './business/shapeshift.js'
import vLog from "./business/vLog.js"

// config
import xConfig from '../config/index.js'

// store
const store = require('./store/index.js')

// utils
import * as userStorage from './utils/userStorage.js'
import * as util from './utils/util.js'
import * as validator from './utils/validator.js'
import * as countDown from './utils/countDownUtil.js'
import * as recordClue from './utils/recordClue.js'
import parseUrl from './utils/parseUrl.js'
import storage from './utils/storage.js'

const base64 = require('./utils/base64.js')
const format = require('./utils/format.js')
const md5 = require('./utils/md5.js')
const layout = require('./utils/layout.js')

//获取应用实例
const qs = require('qs')

export {
  Toast,
  config,
  enums,
  openApiConfig,
  application,
  postV2Api,
  wbs,
  systemtInfo,
  reviewConfig,
  agencyConfig,
  global,
  api,
  requests,
  Event,
  Emitter,
  eventName,
  store,
  base64,
  format,
  md5,
  layout,
  storage,
  userStorage,
  util,
  validator,
  countDown,
  interaction,
  qs,
  parseUrl,
  xConfig,
  convert,
  breakIn,
  userAction,
  vLog,
  shapeShift,
  recordClue,
}

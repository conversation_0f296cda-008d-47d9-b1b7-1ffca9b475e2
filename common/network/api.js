import * as request from "./requests"

import {
  postV2Api,
  wbs
} from '../index.js'

/**
 * 获取分享Id
 */
export function getShareUuid(params) {
  return get('api/v1/activity/share/uuid', params, wbs.leadsApi, true)
}

function get(url, data, domain, needServiceChannel = true) {
  return request.get({url: {path: url, prefix: domain}, data, needServiceChannel, domain})
}

//wbs postV2
function postV2(
  name,
  param = {},
  path = postV2Api.sop,
  token = true,
  sign = true,
  dataType = 'json',
  domain = wbs.v2Service) {
  return request.post({
    url: {
      path,
      prefix: domain
    },
    param: {
      ...param,
      name
    },
    token,
    sign,
    dataType,
    signType: 'v2',
  })
}

//获取直播列表
export function getLiveList(params) {
  return postV2('lc.app.live.webLivePage', params, postV2Api.newHttp, true, true, 'json', wbs.gfH5)
}

/**
 * 根据直播分类 ids
 * 查询移动端wbs直播分类列表
 */
export function getCategoryListByIds(params) {
  return postV2('lc.app.live.wbsCategoryListByIds', params, postV2Api.newHttp, true, true, 'json', wbs.gfH5)
}

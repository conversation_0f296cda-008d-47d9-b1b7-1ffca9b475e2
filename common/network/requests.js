import {
  openApiConfig,
  application,
  postV2Api,
  wbs,
} from '../const/env.js'

import {
  userStorage,
  md5,
  base64,
  format,
  interaction,
} from '../index.js'

import {
  getDomain
} from "../utils/util.js"

import config from '../const/config.js'

const domain = wbs.service

const contentType = {
  json: 'application/json;charset=utf-8',
  form: 'application/x-www-form-urlencoded;charset=utf-8'
}

export function post(params = {
  url: {prefix: domain},
  cancelable: false,
  dataType: 'form',
  token: true,
  sign: true,
  signType: 'v1',
}) {
  if (params.param && params.param.name){
    let $symbol = params.url.path.indexOf('?') > -1 ? '&_=' : '?_='
    params.url.path += $symbol + params.param.name
  }

  let {
    token,
    param,
    sign,
    dataType,
    signType = 'v1'
  } = params

  const userToken = popToken(param)
  let headers = header(dataType, userToken, signType)
  if (config.isOpenSaaS){
    const entId = getApp().globalData.entId
    if (entId){
      headers['entId'] = entId
    }
  }

  let data = {
    token: token && signType == 'v1' ? userToken : null,
    pageNo: param.pageNo >= 1 && param.pageSize > 0 ? param.pageNo : undefined,
    pageSize: param.pageNo >= 1 && param.pageSize > 0 ? param.pageSize : undefined,
    orderBy: param.orderBy
  }

  if (signType == 'v1'){
    if (param.pageNo >= 1 && param.pageSize > 0){
      delete param.pageNo
      delete param.pageSize
    }
  } else if (signType == 'v2'){
    if (param.pageNo >= 1 && param.pageSize > 0){
      param = {
        ...param,
        start: param.pageNo,
        limit: param.pageSize,
      }
      delete param.pageNo
      delete param.pageSize
    } else if (signType == 'v3' || signType == 'v4'){
      delete param.pageNo
      delete param.pageSize
    }
  }

  delete param.signType
  delete param.orderBy

  params.param = param
  const url = fetchUrl(params)
  data = signature(data, params, sign, signType)
  const wrapperData = wrapper(data, dataType, signType)

  return new Promise((resolve, reject) => {
    wx.request({
      url: url,
      method: 'post',
      data: wrapperData,
      header: headers,
      success(res) {
        resolve(res)
      },
      fail(res) {
        reject(res)
      }
    })
  }).then(filterStatus)
    .then(filterNginxIntercept)
    .then(filterLogout)
    .catch(e => {
      const msg = e.errMsg ? e.errMsg : '服务器出错,请稍后再试'
      return {
        success: false,
        msg: msg
      }
    })
}

function popToken(param) {
  const token = param.token ? param.token : userStorage.getToken()
  delete param.token
  return token
}

function header(dataType, token, signType = 'v1') {
  switch (signType) {
    case 'v1':
      return {
        'Accept': 'application/json',
        'Content-Type': dataType == 'json' ? contentType.json : contentType.form,
        'X-Requested-With': 'XMLHttpRequest',
        'x-channel': 'miniprogram',
        'version': config.version,
        ...getProviderHeader()
      }
    case 'v2':
      return {
        'Accept': 'application/json',
        'Content-Type': contentType.json,
        'X-Requested-With': 'XMLHttpRequest',
        'token': token || '',
        'x-channel': 'miniprogram',
        'version': config.version,
        ...getProviderHeader()
      }
    case 'v3':
      return {
        'Accept': 'application/json',
        'Content-Type': dataType == 'json' ? contentType.json : contentType.form,
        'X-Requested-With': 'XMLHttpRequest',
        'token': token,
        'serviceChannel': 'WBS',
        'x-channel': 'miniprogram',
        'version': config.version,
        ...getProviderHeader()
      }
    case 'v4':
      return {
        'Accept': 'application/json',
        'Content-Type': dataType == 'json' ? contentType.json : contentType.form,
        'X-Requested-With': 'XMLHttpRequest',
        'Authorization': `bearer ${token}`,
        'x-channel': 'miniprogram',
        'version': config.version,
        ...getProviderHeader()
      }
  }
}

export function get(params = {
  url: {prefix: domain, path: ''},
  withHeader: true,
  cancelable: false,
  dataType: 'form',
}) {
  const {
    url: {
      prefix,
      path
    },
    needServiceChannel
  } = params || {}
  let url = prefix ? `${prefix}/${path}` : `${path}`

  const {data, dataType = 'form'} = params
  let {token} = data ? data : {}

  let headers = header(dataType)
  if (config.isOpenSaaS){
    const entId = getApp().globalData.entId
    if (entId){
      headers['entId'] = entId
    }
  }

  if (!token){
    token = userStorage.getToken()
  }
  headers['X-Authorization'] = `Bearer ${token}`
  headers['Authorization'] = `Bearer ${token}`

  if (needServiceChannel){
    headers['serviceChannel'] = 'WBS'
    headers['token'] = `${token}`
  }

  headers['clientHost'] = config.isProvider ? config.extConfig.request.original : getDomain(wbs.service)
  headers['x-channel'] = 'miniprogram'

  console.log('### = request get url >>>>', url)
  console.log('### = request get data >>>>', data)
  // console.log('======= request get headers >>>>', headers)
  return new Promise((resolve, reject) => {
    wx.request({
      url: url,
      method: 'get',
      data: data,
      header: headers,
      success(res) {
        resolve(res)
      },
      fail(res) {
        reject(res)
      }
    })
  }).then(filterStatus)
    .then(filterNginxIntercept)
    .then(filterLogout)
    .catch(e => {
      const msg = e.errMsg ? e.errMsg : '服务器出错,请稍后再试'
      return {
        success: false,
        msg: msg
      }
    })
}

//get函数结束

function signature(data, params, sign, signType = 'v1') {//resData要缓存的数据
  if (!sign) return
  const {param, url} = params
  switch (signType) {
    case 'v1': {
      const appkey = userStorage.getUserAppKey()
      let decodeAppKey = appkey ? new base64.Base64().decode(appkey) : null
      const signStr = `${JSON.stringify(param)},${decodeAppKey}`
      return {
        ...data,
        param,
        sign: md5.hexMD5(signStr)
      }
    }
    case 'v2': {
      const {appKey, secret} = url && url.path && url.path === postV2Api.openapi ? openApiConfig : application
      const {name} = param
      delete param.name
      const system = {
        name,
        app_key: appKey,
        data: encodeURIComponent(JSON.stringify(param)),
        timestamp: format.formatTime(new Date(), '-'),
        version: '',
        access_token: ''
      }

      const keys = Object.keys(system).sort()
      let signature = ''
      keys.forEach((key) => {
        signature += `${key}${system[key]}`
      })
      return {
        ...system,
        sign: md5.hexMD5(`${secret}${signature}${secret}`).toUpperCase()
      }
    }
    case 'v3':
    case 'v4': {
      return param
    }
  }
  return null
}

//signature函数结束


function fetchUrl(params) {
  const {url} = params
  return `${url.prefix}${url.path}`
}

function wrapper(data, dataType, signType = 'v1') {
  switch (signType) {
    case 'v1':
      return dataType == 'form' ? `data=${JSON.stringify(data)}` : data
    case 'v2':
      return data
    case 'v3':
    case 'v4':
      return dataType == 'json' ? JSON.stringify(data) : data
  }
}

function filterStatus(res) {
  console.info(`api request status: ${res.statusCode}`)
  if (res.statusCode >= 200 && res.statusCode <= 500){
    return res.data
  } else {
    let error = new Error()
    error.res = res
    error.errMsg = errMsg
    error.type = 'Http'
    throw error
  }
}

function filterLogout(data) {
  const {errorCode, code, statusCode} = data //errorCode 是wbs原生返回的错误码 code是postV2返回的错误码
  if ((code && code === 1001) || (errorCode && errorCode === 1001) || (statusCode && statusCode === 1001)){
    interaction.showLoginDialog()
  }
  return data
}

function filterNginxIntercept(res) {
  const {nginxIntercept, msg} = res
  if (nginxIntercept){
    return {
      code: -1,
      success: false,
      msg,
    }
  }
  return res
}

// download upload
export function upload({api, filePath, name, params,}) {
  let headers = {}
  return new Promise((resolve, reject) => {
    wx.uploadFile({
      url: api,
      filePath,
      name,
      formData: params,
      header: {
        'x-channel': 'miniprogram',
        ...getProviderHeader(),
        ...headers
      },
      success: function(res) {
        resolve(res.data);
      },
      fail: function(err) {
        console.log(err)
        reject(new Error('上传附件失败'));
      }
    });
  }).catch(e => {
    const msg = e.errMsg ? e.errMsg : '服务器出错,请稍后再试'
    return {
      success: false,
      msg: msg
    }
  })
}

export function download({url}) {
  const downloadPath = config.isProvider ? `${config.extConfig.request.initial}/file` : url
  const header = config.isProvider ? {'nb-redirect-url': url} : {}

  return new Promise((resolve, reject) => {
    wx.downloadFile({
      url: downloadPath,
      header,
      success: function(res) {
        let filePath = res.tempFilePath
        resolve(filePath)
      },
      fail: function(res) {
        reject(res)
      }
    })
  })
}

function getProviderHeader() {
  if (!config.isProvider){
    return {}
  }
  return {
    'nb-domain': config.extConfig.request.original
  }
}

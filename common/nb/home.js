import {http, doFetch} from '../utils/helper'
import {postV2Api} from "../index.js";

/**
 * 获取系统UC配置
 */
export const getUCSystemApiConfig = (data) => http('/nb/uc-system/mobile/api/v1/config/findByType', data, 'get')

/**
 * 邀请记录
 */
export const getStaffInviteList = (data) => http('/nb/uc-system/mobile/api/v1/org/staff/invite/list', data, 'get')

/**
 * 获取UnionId
 */
export const getUnionId = (data) => doFetch('/v1/mini/wechat/getUnionid', data, 'POST', '', 'json', postV2Api.marketing, true)

/**
 * 获取WeChatInfoId
 */
export const getFindWechatInfo = () => doFetch(`/v1/org/staff/getWechatInfo`, {}, 'GET', '', 'json', postV2Api.uc_system, true)

/**
 * 查询用户上一次的认证记录
 */
export const getStaffAuthRecord = (data) => doFetch('/v1/org/staff/staffAuthRecord', data, 'POST', '', 'json', postV2Api.uc_system, true)

/**
 * 移动端-机构列表
 */
export const getAgencyList = (data) => doFetch('/v1/org/agencyList', data, 'POST', '', 'json', postV2Api.uc_system, true)

/**
 * 检查本地用户是否添加企业微信
 */
export const checkLocalActiveUser = (data) => doFetch('/v1/org/staff/checkIsFollowd', data, 'POST', '', 'json', postV2Api.uc_system, true)

/**
 * 检查用户状态
 */
export const checkLogin = (data) => doFetch('/v3/org/staff/checkLogin', data, 'POST', '', 'json', postV2Api.uc_system, true)

/**
 * 获取渠道节点
 */
export const findLastNode = (data) => doFetch('/v1/org/isLastNode', data, 'GET', '', 'json', postV2Api.uc_system, true)

/**
 * 查询所有渠道节点
 */
export const findAllDisplayNode = (data) => doFetch('/v1/org/orgCategory/findAllDisplay', data, 'GET', '', 'json', postV2Api.uc_system, true)

/**
 * 查询渠道子节点
 */
export const getQueryOrgNode = (data) => doFetch('/v1/org/queryOrgNode', data, 'GET', '', 'json', postV2Api.uc_system, true)

/**
 * 获取活动id
 */
export const activityGetActivity = (data) => doFetch('/v1/draw/lottery/activity/getActivity', data, 'get', '', 'json', '/activity-system/mobile/api', true)

/**
 * 渠道用户注册
 */
export const doRegister = (data) => doFetch('/v1/org/staff/register', data, 'POST', '', 'json', postV2Api.uc_system, true)

/**
 * 机构用户注册
 */
export const doAgencyRegister = (data) => doFetch('/v1/org/staff/agencyUserRegister', data, 'POST', '', 'json', postV2Api.uc_system, true)

/**
 * 普客注册
 */
export const doNormalRegister = (data) => doFetch('/v1/org/staff/commonUserRegister', data, 'POST', '', 'json', postV2Api.uc_system, true)

/**
 * 重新绑定
 */
export const reBinding = (data) => doFetch('/v1/org/staff/reBinding', data, 'POST', '', 'json', postV2Api.uc_system, true)

/**
 * 检测unionid是否已绑定
 */
export const getCheckBing = (data) => doFetch('/v2/org/staff/checkIsBinded', data, 'POST', '', 'json', postV2Api.uc_system, true)

/**
 * 理财师卡片查询
 */
export const getAdvStaffCardInfo = (data) => doFetch('/v1/staff/card/get', data, 'GET', '', 'json', postV2Api.uc_system, true, true)

/**
 * 地点检索
 */
export const searchPlaceByPoint = (data) => doFetch('/v1/org/placeSearch', data, 'POST', '', 'json', postV2Api.uc_system, true)

/**
 * 渠道补充信息保存
 */
export const savePlaceByPoint = (data) => doFetch('/v1/org/suplementaryChannelSave', data, 'POST', '', 'json', postV2Api.uc_system, true)

/**
 * 用户身份切换
 */
export const setIdExchange = (data) => doFetch('/v2/org/staff/identity/exchange', data, 'POST', '', 'json', postV2Api.uc_system, true)

/**
 * 根据token查询用户所属客群
 */
export const getStaffCustomerType = (data) => doFetch('/v1/org/staff/getCustomerType', data, 'GET', '', 'json', postV2Api.uc_system, true)

/**
 * 添加小程序二维码动态参数
 */
export const addSceneParam = (data) => doFetch('/v1/wechat/addString', data, 'GET', '', 'json', postV2Api.marketing, false, false, false)

/**
 * 获取二维码添加的动态参数
 */
export const getSceneParam = (data) => doFetch('/v1/wechat/getParams', data, 'GET', '', 'json', postV2Api.marketing, false, false, false)

/**
 * 获取小程序二维码
 */
export const getWXACode = (data) => http('/nb/marketing-api/api/v1/wechat/wxacode', data, 'POST',)

/**
 * 获取tabList
 */
export const getPageTemplateInfo = (data) => doFetch('/v1/page/custom/find', data, 'GET', '', 'json', postV2Api.mns, false)

/**
 * 获取tab卡片信息
 */
export const getTemplateById = (data) => doFetch('/v1/page/byId/findPageInfo', data, 'GET')

/**
 * 卡片过滤
 */
export const getContentCategoryFilter = (data) => doFetch('/v1/page/searchCategoryList', data)

/**
 * 获取聚合列表数据
 */
export const getFindById = (data) => doFetch('/v1/agg/cate/findById', data, 'GET')


/**
 * 获取聚合列表数据  内容库
 */
export const getContentLibrary = (data) => doFetch('/v1/agg/cate/client', data, 'GET', '','json', postV2Api.mns,)

/**
 * 获取资讯列表
 */
export const getNewsList = (data) => doFetch('/v1/article/list', data, 'GET', '', 'json', postV2Api.marketing, true)

/**
 * 查询此课程分类是否可分享
 */
export const getShareConfig = (data) => doFetch('/v1/h5/course/getShareConfig', data, 'GET', '', 'json', postV2Api.marketing_course, true)

/**
 * 获取海报列表 (登录用户)
 */
export const getPosterList = (data) => doFetch('/v1/poster/list', data, 'GET', '', 'json', postV2Api.marketing, true)

/**
 * 获取海报列表 (游客模式)
 */
export const getH5PosterList = (data) => doFetch('/v1/h5/poster/list', data, 'GET', '', 'json', postV2Api.marketing, true)

/**
 * 获取音频列表 游客
 */
export const getMulitList = (data) => doFetch('/v1/h5/course/list', data, 'POST', '', 'json', postV2Api.marketing_course, true)

/**
 * 获取音频列表
 */
export const getMulitListLogin = (data) => doFetch('/v1/course/list', data, 'POST', '', 'json', postV2Api.marketing_course, true)

/**
 * 获取营销方案列表
 */
export const getMarketPlanList = (data) => doFetch('/v1/marketPlan/list', data, 'POST', '', 'json', postV2Api.marketing, true)

/**
 * 首页获取营销方案列表
 */
export const getHomeMarketPlanList = (data) => doFetch('/v1/h5/marketPlan/lib/list', data, 'GET', '', 'json', postV2Api.marketing, true)

/**
 * 上报用户信息
 */
export const setShareActivityUInfo = (data) => doFetch('/v1/h5/wechat/action', data, 'POST', '', 'json', postV2Api.marketing, true)

/**
 * 上报浏览时长
 */
export const setShareWechatDuration = (data) => doFetch('/v1/h5/wechat/duration', data, 'POST', '', '', postV2Api.marketing, true)

/**
 * 上报信息
 * unionList
 * recordClue
 */
export const setShareActivityClue = (data) => doFetch('/v1/activity', data, 'POST', '', '', postV2Api.marketing, true)

/**
 * 获取资讯分类
 */
export const getArticleClassify = (data) => doFetch('/v1/article/categoryById', data, 'GET', '', 'json', postV2Api.marketing, true)

/**
 * 获取营销资料分类
 */
export const getMarketPlanClassify = (data) => doFetch('/v1/h5/marketPlanCategory/categoryList', data, 'GET', '', 'json', postV2Api.marketing, true)

/**
 * 获取多媒体分类
 */
export const getMulitClassify = (data) => doFetch('/v1/h5/course/category/list', data, 'GET', '', 'json', postV2Api.marketing_course, true)

/**
 * 获取海报分类
 */
export const getPosterClassify = (data) => doFetch('/v1/poster/category/ids', data, 'GET', '', 'json', postV2Api.marketing, true)

/**
 * 企业微信登录
 */
export const qyCode2session = (data) => doFetch('/wxwork/api/v1/code2session', data, 'POST', '', 'json', postV2Api.uc_login, true)

/**
 * 获取推荐营销资料
 */
export const getRecommendList = (data) => doFetch('/v2/marketPlan/list/exclusive', data, 'POST', '', 'json', postV2Api.marketing, true)

/**
 * 广发登录
 */
export const newLogin = (data) => doFetch('/v1/org/staff/login', data, 'POST', '', 'json', postV2Api.uc_system, true)

/**
 * 员工校验
 */
export const employeeCheck = (data) => doFetch('/login/checkEmployeeLogin', data, 'POST', '', 'json', postV2Api.uc_login)

/**
 * 员工获取验证码
 */
export const employeeSMSCode = (data) => doFetch('/login/sendCode', data, 'POST', '', 'form', postV2Api.uc_login)

/**
 * 员工登录
 */
export const employeeLogin = (data) => doFetch('/login', data, 'POST', '', 'json', postV2Api.uc_login)

/**
 * 广发获取验证码
 */
export const gflogincode = (data) => doFetch('/v1/org/staff/sendCode', data, 'POST', '', 'form-data', postV2Api.uc_system, true)

/**
 * 资料详情
 */
export const getMarketingPlanInfo = (data) => doFetch('/v1/h5/marketPlan/get', data, 'GET', '', 'json', postV2Api.marketing)

/**
 * 海报列表
 */
export const getMarketPosterList = (data) => doFetch('/v1/poster/list', data, 'GET', '', 'json', postV2Api.marketing, true)

/**
 * 获取用户关注公众号状态
 */
export const getAttentionStatus = (data) => doFetch('/v1/orgStaff/queryAttentionStatusByUnionId', data, 'GET', '', 'json', postV2Api.uc_admin)

/**
 * 获取活动拉新数据
 */
export const getPromotionalData = (data) => doFetch('/v1/miniapp/promotional/queryPromotionalData', data, 'GET', '', 'json', postV2Api.marketing)


/**
 * 是否可参加活动
 */
export const checkDisqualified = (data) => doFetch('/v1/draw/lottery/activity/checkDisqualified', data, 'GET', '', 'json', '/activity-system/mobile/api')

/**
 * 获取活动剩余名额
 */
export const getInvitationData = (data) => doFetch('/v1/miniapp/promotional/queryInvitationData', data, 'GET', '', 'json', postV2Api.marketing)

/**
 * 广发-搜索->基金解读
 */
export const searchFunds = (data) => doFetch('/fund_search', data, 'GET', '', 'json', '', false, false, true)

/**
 * 广发-搜索->产品运作回顾
 */
export const searchFundReport = (data) => doFetch('/fund_report_search', data, 'GET', '', 'json', '', false, false, true)

/**
 * 日期列表返回接口（股票查询专用）
 */
export const getFundDateList = (data) => doFetch('/getFundUnscrambleDateList', data, 'GET', '', 'json', '', false, false, true)

/**
 * 广发-搜索->股票列表
 */
export const searchByStock = (data) => doFetch('/getholdStockData', data, 'GET', '', 'json', '', false, false, true)

/**
 * 获取普通链接内容
 */
export const getUrl2HtmlInfo = (data) => {
  return new Promise((resolve, reject) => {
    wx.request({
      url: data.url,
      method: 'GET',
      header: {
        'Content-Type': 'text/json;charset=UTF-8',
        'X-Requested-With': 'XMLHttpRequest',
      },
      data: {},
      success(response) {
        const {data, statusCode} = response || {}
        if (statusCode === 200){
          resolve(data)
        } else {
          reject(response)
        }
      }
    })
  })
}

const formatTime = (date, joinStr, format) => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  switch (format) {
    case 'yyyymmdd':
      return [year, month, day].map(formatNumber).join(joinStr || '-')
    case 'mmdd':
      return [month, day].map(formatNumber).join(joinStr || '-')
    case 'yyyymmddhhmm':
      return [year, month, day].map(formatNumber).join(joinStr || '-') + ' ' + [hour, minute].map(formatNumber).join(':')
    case 'yyyymmddhhmmss':
      return [year, month, day].map(formatNumber).join(joinStr || '-') + ' ' + [hour, minute, second].map(formatNumber).join(':')
    case 'hhmmss':
      return [hour, minute, second].map(formatNumber).join(':')
    default:
      return [year, month, day].map(formatNumber).join(joinStr || '-') + ' ' + [hour, minute, second].map(formatNumber).join(':')
  }
}

// 数字转 分钟:秒
const numberToTime = (num) => {
  if (num <= 0){
    return '00:00'
  }
  // 分钟
  const m = parseInt(num / 60)
  // 秒
  const s = num - m * 60
  return `${(m + '').padStart(2, '0')}:${(s + '').padStart(2, '0')}`
}

const formatDate = (date, format) => {
  let dateString = date.split('.')[0]
  let dateArr = dateString.split('T')
  let yyyymmdd = dateArr[0]
  let mmddArr = yyyymmdd.split('-')
  mmddArr.shift()
  let mmdd = mmddArr.join('-')
  let hhmmArr = dateArr[1].split(':')
  hhmmArr.pop()
  let hhmm = hhmmArr.join(':')
  switch (format) {
    case 'yyyymmdd': // 2018-10-10
      return yyyymmdd
    case 'mmdd': // 10-10
      return mmdd
    case 'MMDD': // 10月10日
    {
      let mmddArr = mmdd.split('-')
      return `${mmddArr[0]}月${mmddArr[1]}日`
    }
    case 'hhmm': // 16:16
      return hhmm
    case 'yyyymmddhhmm': // 2018-10-10 16:16
      return `${yyyymmdd} ${hhmm}`
    case 'mmddhhmm': // 10-10 16:16
      return `${mmdd} ${hhmm}`
    case 'cnmmddhh': // 10月10日16点
      let mmddArr = mmdd.split('-')
      return `${mmddArr[0]}月${mmddArr[1]}日${hhmm.split('-')[0]}点`
    case 'cnhhmm': // 今天 16:16
      return getDateDiff(dateArr.join(' '))
    default:
      break;
  }
}

const formatTimestamp = (number, format) => {
  if (!number){
    return ''
  }
  const date = new Date(number);
  const Y = date.getFullYear()
  const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1)
  const D = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();

  const h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours())
  const m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes())

  switch (format) {
    case 'yyyymmdd': // 2018-10-10
      return `${Y}-${M}-${D}`
    case 'mmdd': // 10-10
      return `${M}-${D}`
    case 'MMDD': // 10月10日
      return `${M}月${D}日`
    case 'YYYYMMDD': // 2019年10月10日
      return `${Y}年${M}月${D}日`
    case 'hhmm': // 16:16
      return `${h}:${m}`
    case 'yyyymmddhhmm': // 2018-10-10 16:16
      return `${Y}-${M}-${D} ${h}:${m}`
    case 'mmddhhmm': // 10-10 16:16
      return `${M}-${D} ${h}:${m}`
    case 'cnmmddhh': // 10月10日16点
      return `${M}月${D}日${h}点`
    default:
      break;
  }
}

const formatNumber = n => {
  n = n.toString()
  return n[1] ? n : '0' + n
}

const formatPixelImage = uri => {
  const {
    pixelRatio,
  } = wx.getSystemInfoSync()
  return pixelRatio < 3 ?
    `${uri.substr(0, uri.lastIndexOf('.png'))}@2x.png` :
    `${uri.substr(0, uri.lastIndexOf('.png'))}@3x.png`
}

/**
 * 获取几个月前的输入日期
 * date 输入日期(YYYY-MM-DD)
 * monthNum 月数
 */
const getPreMonthDay = (date, monthNum) => {
  let dateArr = date.split('-');
  let year = dateArr[0]; //获取当前日期的年份
  let month = dateArr[1]; //获取当前日期的月份
  let day = dateArr[2]; //获取当前日期的日
  let year2 = year;
  let month2 = parseInt(month) - monthNum;
  if (month2 <= 0){
    const deltaMonthInt = parseInt(Math.abs(month2) / 12)
    year2 = parseInt(year2) - (deltaMonthInt == 0 ? 1 : deltaMonthInt);
    month2 = 12 - (Math.abs(month2) % 12);
  }
  let day2 = day;
  let days2 = new Date(year2, month2, 0);
  days2 = days2.getDate();
  if (day2 > days2){
    day2 = days2;
  }
  if (month2 < 10){
    month2 = '0' + month2;
  }
  let t2 = year2 + '-' + month2 + '-' + day2;
  return t2;
}

//时间戳转换时间 年月日 时分秒
function toFullDate(number, joinStr) {
  const date = new Date(number);
  const Y = date.getFullYear() + (joinStr || '/');
  const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + (joinStr || '/');
  const D = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();

  const h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
  const m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
  const s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();

  return (Y + M + D + ' ' + h + m + s);
}

//时间戳转换时间 年月日
function toDate(number, joinStr) {
  if (!number) return ''
  if (isNaN(number)){
    return number
  }
  const date = new Date(number);
  const Y = date.getFullYear() + (joinStr || '-');
  const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + (joinStr || '-');
  const D = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
  return (Y + M + D);
}

// 时间戳转换时间 月日
function toMD(number, joinStr) {
  const date = new Date(number);
  const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + (joinStr || '-');
  const D = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
  return (M + D);
}

//时间戳转化时间 年月日 时分
function toYMDhm(number) {
  const date = new Date(number);
  const Y = date.getFullYear() + '-';
  const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
  const D = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();

  const h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
  const m = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();

  return (Y + M + D + ' ' + h + m);
}

// 手机号显示格式转换 131 3232 3232
const formatMobile = phone => {
  return phone.replace(/(\d{3})(\d{4})(\d{4})/, "$1 $2 $3")
}

const formatShareImage = (pic) => {
  return `${pic}?x-oss-process=image/resize,m_fill,h_224,w_280`
}

module.exports = {
  formatTime,
  formatDate,
  formatNumber,
  formatPixelImage,
  getPreMonthDay,
  toFullDate,
  toDate,
  toYMDhm,
  toMD,
  formatMobile,
  formatTimestamp,
  numberToTime,
  formatShareImage
}

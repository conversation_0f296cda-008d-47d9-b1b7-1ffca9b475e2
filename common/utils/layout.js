import {getTextLength} from "./util";

const {windowWidth} = wx.getSystemInfoSync()

const ONE_HAN_ZH_SIZE = Math.floor((windowWidth * 0.9) / 18)

const getLeftSize = (title = '') => {
  let leftSize = 10
  let titleLength = getTextLength(title)

  if (titleLength >= 32){
    title = `${title}`.slice(0, 15) + '...'
    titleLength = 32
  }
  titleLength = (titleLength / 2) * ONE_HAN_ZH_SIZE
  leftSize = Math.floor(((windowWidth * 0.78) - titleLength) / 2)

  return {title, leftSize}
}

export {
  getLeftSize,
}

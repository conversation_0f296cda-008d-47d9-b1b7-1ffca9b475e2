/* 毫秒级倒计时 */

import {eventName} from "../index";
const dayJs = require('dayjs')

const {
  DO_READY_LINE_REFRESH,
  DO_OVER_WAIT_LINE
} = eventName

let timer = null;
let waitMs = null;

export function count_down(that, total_micro_second) {
  if (total_micro_second <= 0){
    that.setData({
      verifyCode: "重新发送",
      sendLoading: false
    });
    // timeout则跳出递归
    return;
  }
  // 渲染倒计时时钟
  that.setData({
    verifyCode: total_micro_second + " 秒后重试",
    sendLoading: true
  });
  timer = setTimeout(function() {
    // 放在最后--
    total_micro_second -= 1;
    count_down(that, total_micro_second);
  }, 1000)
}

export function clear_count(that) {
  if (timer != null){
    clearTimeout(timer);
    that.setData({
      verifyCode: "重新发送",
      sendLoading: false
    });
  }
}


/**
 * 倒计时
 * @param that
 * @param total_micro_second
 */
export function count_dieLine(that, total_micro_second = 0) {
  if (total_micro_second <= 0){
    that.setData({
      deathLineTime: [0, 0, 0, 0],
    })

    return
  }

  if (total_micro_second === 1){
    getApp().event.emit(DO_READY_LINE_REFRESH)
  }

  let duration = dayJs.duration(total_micro_second, 'seconds')
  let {
    _data: {
      months = 0,
      days = 0,
      hours = 0,
      minutes = 0,
      seconds = 0
    }
  } = duration || {}
  days = days * 1

  if (months > 0){
    let start = dayJs(new Date()).format('YYYY-MM-DD')
    let [year, month] = `${start}`.split('-')

    let month2 = (month * 1) + 1
    let year2 = year
    if (month2 === 13){
      month2 = 1;
      year2 = year + 1;
    }

    let nextMonth = dayJs([year2, month2, '01']).endOf('month');
    const {_d = '',} = nextMonth || {}
    let _tMonth = `${_d}`.split(' ')
    let _diffDays = _tMonth[2] * 1 || 0

    if (_diffDays > 0){
      days = days + _diffDays
    }

    if (days > 99){
      days = 99
    }
  }

  that.setData({
    deathLineTime: [days, hours, minutes, seconds],
  })

  timer = setTimeout(function() {
    total_micro_second -= 1;
    count_dieLine(that, total_micro_second);
  }, 1000)
}

export function count_wait_begin(ms) {
  if (ms <= 0){
    return
  }

  if (ms === 1){
    console.log('sendMessage do starting')
    getApp().event.emit(DO_OVER_WAIT_LINE)
  }

  waitMs = setTimeout(() => {
    ms -= 1
    count_wait_begin(ms)
  }, 1000)
}


export function clear_dieLine(that) {
  if (timer !== null){
    clearTimeout(timer);
    that.setData({
      deathLineTime: [0, 0, 0, 0],
    })
  }
}

export function clear_wait() {
  if (waitMs !== null){
    clearTimeout(waitMs);
  }
}

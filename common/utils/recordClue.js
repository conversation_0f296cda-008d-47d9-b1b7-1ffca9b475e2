import {setShareWechatDuration} from '../nb/home'
import {storage, global} from "../index";

let durationTime = 0
let durationTimer = null

// 端内上报浏览时长
export const reportDuration = async function(params, total_second = 1000000) {
  let canLoop = storage.getStorage(global.STORAGE_GLOBAL_REPORT_STATUS)
  if (!canLoop){
    return
  }

  if (total_second === 0){
    return
  }

  durationTimer = setTimeout(async function() {
    // 放在最后--
    total_second -= 1;
    durationTime += 5

    let _params = {
      ...params,
      duration: durationTime,
    }
    console.log('RECORD_CLUE reportDuration _params >>>>', _params)
    await reportDuration(_params, total_second)
    await setShareWechatDuration(params)
  }, 5000)
}

export const reportLeave = function() {
  console.log('RECORD_CLUE reportLeave durationTimer >>>>', durationTimer)
  if (durationTimer !== null){
    clearTimeout(durationTimer)
  }
  storage.setStorage(global.STORAGE_GLOBAL_REPORT_DURATION, durationTime)
  storage.setStorage(global.STORAGE_GLOBAL_REPORT_STATUS, false)
}

export const reportReOpen = async function() {
  console.log('RECORD_CLUE reportReOpen  >>>>')
  storage.setStorage(global.STORAGE_GLOBAL_REPORT_STATUS, true)
  let lastDuration = storage.getStorage(global.STORAGE_GLOBAL_REPORT_DURATION) || 0
  let lastReportParams = storage.getStorage(global.STORAGE_GLOBAL_REPORT_PARAMS) || {}

  let reParams = {
    ...lastReportParams,
    duration: lastDuration,
  }

  console.log('RECORD_CLUE reportReOpen reParams  >>>>', reParams)
  storage.setStorage(global.STORAGE_GLOBAL_REPORT_PARAMS, reParams)
  await reportDuration(reParams)
}

export default class storage {
  static setStorage(key, value) {
    try {
      wx.setStorageSync(key, value)
    } catch (e) {
      console.error('>>set storage e >>', e)
    }
  }

  static getStorage(key) {
    try {
      const value = wx.getStorageSync(key)
      return value
    } catch (e) {
      console.error('>>get storage e >>', e)
      return null
    }
  }

  static removeStorage(key) {
    try {
      wx.removeStorageSync(key)
    } catch (e) {
      console.error('>>remove storage e >>', e)
    }
  }
}

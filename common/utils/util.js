import { qs, requests } from "../index.js"

const { windowWidth } = wx.getSystemInfoSync()

const formatKeys = ['false', 'true', 'flase', 'ture']
const FalseKey = ['false', 'flase']
const TrueKeys = ['true', 'ture']
const NullKeys = [null, undefined, 'null', 'undefined', '', '[object Object]']

function getDomain(url) {
  //https://service-wbs240.newtamp.cn/http/
  if (url) {
    let arr1 = url.split('//')
    let domain = arr1[1] && arr1[1].split('/')[0]
    return domain
  }
  return ''
}

/**
 * 隐藏中间 str：字符串，frontLen：前面保留位数，endLen：后面保留位数。
 */
function plusXing(str, frontLen, endLen) {
  let len = str.length - frontLen - endLen;
  let xing = '';
  for (let i = 0; i < len; i++) {
    xing += '*';
  }
  return str.substring(0, frontLen) + xing + str.substring(str.length - endLen);
}

const toPromise = (wx) => {
  return (method) => {
    return (option) => {
      return new Promise((resolve, reject) => {
        wx[method]({
          ...option,
          success: (res) => {
            resolve(res)
          },
          fail: (err) => {
            reject(err)
          }
        })
      })
    }
  }
}

// 将rpx转为px
const rpx2px = params => {
  return windowWidth / 750 * params
}

const px2rpx = params => {
  return 750 / windowWidth * params
}

const fileNameSuffix = fileName => {
  let index1 = fileName.lastIndexOf(".");
  let index2 = fileName.length;
  let suffix = fileName.substring(index1, index2);//后缀名
  return suffix.toLowerCase()
}

// 解析url query
const parseQuery = function (query) {
  let reg = /([^=&\s]+)[=\s]*([^&\s]*)/g;
  let obj = {};
  while (reg.exec(query)) {
    obj[RegExp.$1] = RegExp.$2;
  }
  return obj;
}

const openFile = (url) => {
  wx.showLoading({
    title: '正在加载...',
  })
  requests.download({ url })
    .then(filePath => {
      wx.hideLoading();
      wx.openDocument({
        filePath: filePath,
        success: function () {
          console.log('打开文档成功')
        },
        fail: function () {
          wx.showToast({
            title: '打开文件失败',
          })
        }
      })
    })
    .catch(() => {
      wx.hideLoading();
      wx.showToast({
        title: '文件下载失败',
        icon: 'none',
      })
    })
}

/**
 * 小程序图片下载并预览
 * @param url 图片地址
 * @returns
 */
const conmmonPreviewIOS = async (url = '') => {
  if (!url) {
    wx.showToast({
      title: '文件地址错误',
      icon: 'none',
      duration: 1000
    })
  }

  wx.showLoading({
    title: '正在加载...',
  })

  const FileSystemManager = wx.getFileSystemManager()
  return (
    await wx.downloadFile({   // 先下载
      url: url,
      header: {
        'content-type': 'application/json',
      },
      success: (res) => {
        const Path = res.tempFilePath  // 拿到临时文件路径
        let suffixIndex = Path.lastIndexOf('.');
        let suffix = Path.slice(suffixIndex,);
        FileSystemManager.saveFile({ // 用系统方法保存临时文件
          tempFilePath: Path,
          filePath: `${wx.env.USER_DATA_PATH}/${new Date().getTime()}${suffix}`,  // 文件名用时间戳
          success: (res) => {
            let savedFilePath = res.savedFilePath;  // 拿到保存成功后的地址
            wx.openDocument({   // 打开文档 * （再分享的时候就会把保存在本地的文件 分享出去 即上一步操作的 文件 文件名以时间戳的形式展示）
              filePath: savedFilePath,
              showMenu: true,
              fileType: 'pdf',
              success: () => {
                console.log('打开文档成功')
              }
            })
          }
        })
      },
      fail: () => {
        wx.showToast({
          title: '文件下载失败',
          icon: 'none',
          duration: 1000
        })
      }
    })
  )
}

function doUrlDeCode(str = '') {
  let _str = decodeURIComponent(str)
  if (`${_str}`.startsWith('%')) {
    return doUrlDeCode(_str)
  }
  return _str
}

function getQueryParams(url = '') {
  const paramsObj = {};
  const reg = /[?&][^?&]+=[^&]+/g;
  const found = url.match(reg) || []

  found.forEach(item => {
    let temp = item.substring(1).split('=')
    let [key, value = ''] = temp
    paramsObj[key] = value
  })

  return paramsObj
}

function transformObject(obj) {
  const transformedObj = {};

  for (const key in obj) {
    const match = key.match(/^(\w+)\[(\w+)\]$/);
    if (match) {
      const mainKey = match[1];
      const subKey = match[2];
      if (!transformedObj[mainKey]) {
        transformedObj[mainKey] = {};
      }
      transformedObj[mainKey][subKey] = obj[key];
    } else {
      transformedObj[key] = obj[key];
    }
  }

  return transformedObj;
}

function encodeFormObject(obj = {}) {
  const transformedObj = {};
  const targetObject = Object.entries(obj)
  for (const [key, value] of targetObject) {
    transformedObj[decodeURIComponent(key)] = decodeURIComponent(`${value}`);
  }
  return transformedObj;
}

const panTransformDCode2Object = (obj = {}) => {
  let target = {};
  for (let key in obj) {
    let keyString = key;
    let keyDeep = keyString.split("%5B");
    if (keyDeep.length > 1) {
      let first = keyDeep[0];
      if (!target[first]) target[first] = {}
      let last = keyDeep[1].slice(0, keyDeep[1].length - 3);
      target[first][`${last}`] = obj[keyString];
    } else {
      target[keyString] = obj[keyString];
    }
  }
  return target;
}

function panTransformUrl2Params(url = '') {
  let resParams = {}
  for (let [key, value] of Object.entries(qs.parse(url))) {
    if (`${key}`.includes('?')) {
      const [_path, nextKey] = `${key}`.split('?')
      resParams['targetPath'] = _path
      resParams[`${nextKey}`] = value
    } else if (`${value}`.includes('?')) {
      const [_perfix, _value] = `${value}`.split('?')
      resParams['perfix'] = _perfix
      if (`${_value}`.includes('=')) {
        const [_vKey, _vValue] = `${_value}`.split('=')
        resParams[`${_vKey}`] = _vValue
      } else {
        resParams[`value`] = _value
      }
    } else {
      resParams[key] = doUrlDeCode(value + '')
    }
  }

  return resParams
}

function throttle(fn, gapTime) {
  if (gapTime == null || gapTime == undefined) {
    gapTime = 1500
  }

  let _lastTime = null

  // 返回新的函数
  return function () {
    let _nowTime = +new Date()
    if (_nowTime - _lastTime > gapTime || !_lastTime) {
      fn.apply(this, arguments)   //将this和参数传给原函数
      _lastTime = _nowTime
    }
  }
}

// 将一个大数组分割成几个小数组
const sliceArray = (array, size) => {
  if (!array || !array.length) return []
  let result = []
  for (let i = 0; i < Math.ceil(array.length / size); i++) {
    let start = i * size
    let end = start + size
    result.push(array.slice(start, end))
  }
  return result
}


/**
 * 四舍五入
 */
let round = function (val, digit = 1) {
  let offset = Math.pow(10, digit)
  return Math.round(val * offset) / offset
}

// 需要前端手动拼上单位的集合
const needAddUnitKey = (key) => {
  return {
    // minBuyMoney: '元',
    establish: '元',
    publishScale: '元'
  }[key]
}

const tel400 = function (tel) {
  tel = tel.toString();
  if (tel.slice(0, 3) === '400') {
    if (tel.indexOf('-') === -1) {
      return tel.slice(0, 4) + '-' + tel.slice(4, 3) + '-' + tel.slice(7, 3);
    }
  }
  return tel;
}

const getUrlQuery = (url = '') => {
  // 用JS拿到URL，如果函数接收了URL，那就用函数的参数。如果没传参，就使用当前页面的URL
  let queryString = url ? url.split('?')[1] : window.location.search.slice(1);
  // 用来存储我们所有的参数
  let obj = {};
  // 如果没有传参，返回一个空对象
  if (!queryString) {
    return obj;
  }
  // stuff after # is not part of query string, so get rid of it
  queryString = queryString.split('#')[0];
  // 将参数分成数组
  let arr = queryString.split('&');
  for (let i = 0; i < arr.length; i++) {
    // 分离成key:value的形式
    let a = arr[i].split('=');
    // 将undefined标记为true
    let paramName = a[0];
    let paramValue = typeof (a[1]) === 'undefined' ? true : a[1];
    // 如果调用对象时要求大小写区分，可删除这两行代码
    paramName = paramName.toLowerCase();
    if (typeof paramValue === 'string') paramValue = paramValue.toLowerCase();
    // 如果paramName以方括号结束, e.g. colors[] or colors[2]
    if (paramName.match(/\[(\d+)?\]$/)) {
      // 如果paramName不存在，则创建key
      let key = paramName.replace(/\[(\d+)?\]/, '');
      if (!obj[key]) obj[key] = [];
      // 如果是索引数组 e.g. colors[2]
      if (paramName.match(/\[\d+\]$/)) {
        // 获取索引值并在对应的位置添加值
        let index = /\[(\d+)\]/.exec(paramName)[1];
        obj[key][index] = paramValue;
      } else {
        // 如果是其它的类型，也放到数组中
        obj[key].push(paramValue);
      }
    } else {
      // 处理字符串类型
      if (!obj[paramName]) {
        // 如果如果paramName不存在，则创建对象的属性
        obj[paramName] = paramValue;
      } else if (obj[paramName] && typeof obj[paramName] === 'string') {
        // 如果属性存在，并且是个字符串，那么就转换为数组
        obj[paramName] = [obj[paramName]];
        obj[paramName].push(paramValue);
      } else {
        // 如果是其它的类型，还是往数组里丢
        obj[paramName].push(paramValue);
      }
    }
  }
  return obj;
};

const isEmptyObject = (obj = {}) => {
  return Object.keys(obj).length === 0;
}

const analyzeAction = (act = '') => {
  let res = {}
  if (!act) {
    return res
  }

  let [temp, ..._res] = act.split('action://share/')
  if (temp) {
    return res
  }

  const [type, ...value] = `${_res.join('')}`.split('?')
  let _value = value.join('')
  res = {
    type,
    analyzeRes: qs.parse(_value)
  }
  return res
}

const timeFormat = (value, linker) => {
  if (!value) return ''
  let time = parseInt(value)
  time = time + ''
  if (time.length > 11) {
    time = parseInt(value)
  } else {
    time = parseInt(value) * 1000
  }
  let date = new Date(time)
  let month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1
  let currentDate = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
  let hh = date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
  let mm = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
  let ss = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
  return date.getFullYear() + '-' + month + '-' + currentDate + (linker || " ") + hh + ":" + mm + ":" + ss
}

const isJSON = (jsonContent) => {
  if (typeof jsonContent == 'string') {
    try {
      if (jsonContent.indexOf('{') > -1) {
        return {
          "msg": "",
          "data": jsonContent,
          "success": true
        };
      } else {
        return {
          "msg": "数据配置错误",
          "data": null,
          "success": false,
        };
      }
    } catch (e) {
      console.log('=== isJSON >>>', e);
      return {
        "msg": "数据配置错误",
        "data": null,
        "success": false,
      };
    }
  }
  return {
    "msg": "数据配置错误",
    "data": null,
    "success": false,
  };
};

const isGoodJSON = (str) => {
  try {
    JSON.parse(str)  // 如果抛出异常，则会从这条语句终止，被catch捕捉
    return true
  } catch (e) {
    return false
  }
};

const doJSON_PARSE = (obj = '') => {
  if (typeof obj === 'string') {
    if (obj.startsWith('{') && obj.endsWith('}') && isGoodJSON(obj)) {
      obj = JSON.parse(obj)
      for (let [key, value] of Object.entries(obj)) {
        obj[key] = doJSON_PARSE(value)
      }
    }
  }
  return obj
}

/**
 * 深拷贝 by huiwen
 * @param origin
 * @param hasMap
 * @returns {any|RegExp|Date}
 */
const panDClone = function (origin, hasMap = new WeakMap()) {
  if (origin == undefined || typeof origin !== 'object') {
    return origin
  }

  if (origin instanceof Date) {
    return new Date(origin)
  }

  if (origin instanceof RegExp) {
    return new RegExp(origin)
  }

  let hashKey = hasMap.get(origin)
  if (hashKey) {
    return hashKey
  }

  const target = new origin.constructor()
  for (const key in origin) {
    if (origin.hasOwnProperty(key)) {
      target[key] = panDClone(origin[key], hasMap)
    }
  }

  return target
}

function getTextLength(str, full) {
  let len = 0;
  for (let i = 0; i < str.length; i++) {
    let c = str.charCodeAt(i);
    //单字节加1
    if ((c >= 0x0001 && c <= 0x007e) || (0xff60 <= c && c <= 0xff9f)) {
      len++;
    } else {
      len += (full ? 2 : 1);
    }
  }
  return len;
}

function formatUrlObject(obj = {}) {
  // console.log('======= formatUrlObject obj >>>>', obj)
  let res = {}

  if (isEmptyObject(obj)) {
    return obj
  }

  for (const item of Object.entries(obj)) {
    const key = item[0]
    let value = item[1]
    if (Object.prototype.toString.call(value) === '[object String]' && formatKeys.includes(`${value}`)) {
      if (FalseKey.includes(value)) {
        value = false
      }

      if (TrueKeys.includes(value)) {
        value = true
      }
    }

    if (Object.prototype.toString.call(value) !== '[object Boolean]' && !isNaN(value * 1)) {
      value = value * 1
    }

    if (NullKeys.includes(value)) {
      value = ''
    }

    res[`${key}`] = value
  }

  return res
}

function repairLaunchPath(path = '') {
  if (!`${path}`.startsWith('/')) {
    path = `/${path}`
  }

  return path
}

const getUuid = () => {
  function S4() {
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
  }

  return (S4() + S4() + S4() + S4() + S4() + S4() + S4() + S4());
}

const getQueryString = (url, name) => {
  var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
  var r = url.substr(1).match(reg);
  if (r != null) {
    return unescape(r[2]);
  }
  return null;
}

const parseOptions2Object = (options = {}) => {
  const result = {}

  for (const item of Object.entries(qs.parse(options))) {
    const [key, value] = item
    if (typeof value !== 'object') {
      result[key] = doUrlDeCode(`${value}`)
    } else {
      const temp = {}
      for (const [tKey, tValue] of Object.entries(Object.assign(value))) {
        temp[tKey] = doUrlDeCode(tValue + '')
      }
      result[key] = { ...temp }
    }

    if (result[key] === 'true') {
      result[key] = true
    }
  }

  return result
}


module.exports = {
  plusXing,
  getDomain,
  toPromise,
  rpx2px,
  px2rpx,
  isJSON,
  fileNameSuffix,
  parseQuery,
  openFile,
  throttle,
  sliceArray,
  round,
  needAddUnitKey,
  tel400,
  conmmonPreviewIOS,
  getUrlQuery,
  analyzeAction,
  isEmptyObject,
  timeFormat,
  getTextLength,
  formatUrlObject,
  getQueryParams,
  doUrlDeCode,
  getUuid,
  doJSON_PARSE,
  panDClone,
  repairLaunchPath,
  panTransformUrl2Params,
  panTransformDCode2Object,
  parseOptions2Object,
  transformObject,
  encodeFormObject,
  getQueryString
}

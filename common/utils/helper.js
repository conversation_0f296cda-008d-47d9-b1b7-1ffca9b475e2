import {getToken, getOpenId} from './userStorage'
import {wbs, postV2Api, interaction} from '../index'
import {TAB_LISTENER_EVENT, FETCH_NET_DATA_ERROR} from "../event/name";
import vLog from "../business/vLog";

const gfPerFix = wbs.gfApi
const gfMimiFix = wbs.gfMini
//  'https://gf-admin.newtamp.cn',

const nbDecludeUrl = ['/marketing-api/api/v1/mini/token/local/replace']

const contentType = {
  json: 'application/json;charset=utf-8',
  form: 'application/x-www-form-urlencoded;charset=utf-8'
}

const ERROR_CODE = [400, 404]

const log = wx.getRealtimeLogManager ? wx.getRealtimeLogManager() : null

function parseApiName(api) {
  const arr = api.split('/');
  let index = arr.findIndex(str => str.startsWith('v'));
  if (index === -1){
    return api;
  }
  return '/' + arr.slice(index + 1).join('/');
}

export function doFetch(
  url,
  data,
  method = "POST",
  header = '',
  dataType = 'json',
  domain = postV2Api.mns,
  isMarketing = false,
  isStaff = false,
  isGfMini = false) {
  let _url = `${gfPerFix}${domain}${url}`
  if (isGfMini){
    _url = `${gfMimiFix}${domain}${url}`
  }

  let headers = header || {
    'Accept': 'application/json;charset=UTF-8',
    'Content-Type': dataType === 'json' ? contentType.json : contentType.form,
    'X-Requested-With': 'XMLHttpRequest',
    "token": '',
  }

  if ((getApp()?.globalData?.token || getToken())){
    headers.token = getApp()?.globalData.token || getToken() || ''
  }

  if (isStaff && getOpenId()){
    headers.cookie = `openid=${getOpenId()}`
  }

  if (isMarketing){
    headers = {
      ...headers,
      'serviceChannel': 'PRI'
    }
  }

  return new Promise((resolve, reject) => {
    wx.request({
      url: _url,
      method,
      header: headers,
      data: {...data},
      success(response) {
        const {data, statusCode} = response || {}
        if (statusCode === 200){
          resolve(data)
        } else if (statusCode === 401){
          let page = getCurrentPages().pop();

          if (page.route == 'pages/user/login/index'){
            return interaction.showToast(data.msg || data.error || '')
          }
          const {code, path} = data || {}
          if (code === 401 && path === '/uc-system/mobile/api/v1/staff/card/get'){
            resolve(data)
          } else {
            reject(response)
          }
        } else {
          if (ERROR_CODE.includes(statusCode * 1)){
            getApp().event.emit(TAB_LISTENER_EVENT, {key: FETCH_NET_DATA_ERROR})
          }
          reject(response)
        }
      },
      fail(err) {
        vLog.error(`${parseApiName(url)} err >>`, err).report()
        reject(err)
      },
      complete(res) {
        console.log(`%c ${url} `, 'background:#f04b28ff;color:#fff', res)
        log && log.info({
          url: _url,
          statusCode: res.statusCode,
          msg: res?.data?.message || res?.data?.msg || res?.data?.error || '',
        })
      }
    })
  })
}

// HTTP
export function http(url, data, method = "post", header, isGfMini = false) {
  let _url = gfPerFix + url
  if (isGfMini){
    _url = gfMimiFix + url
  }
  let headers = header || {}

  // 区分nb、鸿坤接口
  if (url.startsWith('/nb')){
    headers.token = getToken()
    // 移除前缀 '/nb'
    _url = gfPerFix + url.slice(3)
    for (let item of nbDecludeUrl) {
      if (_url.includes(item)){
        delete headers.token
      }
    }
  } else {
    if ((getApp()?.globalData?.token || getToken())){
      headers.token = getApp()?.globalData.token || getToken() || ''
    }
  }

  return new Promise((res, rej) => {
    wx.request({
      url: _url,
      method,
      header: headers,
      data: {
        ...data
      },
      success(response) {
        if (response.statusCode === 200){
          res(response.data)
        } else {
          rej(response)
        }
      },
      fail(err) {
        rej(err)
      },
      complete(res) {
        console.log(`%c ${url} `, 'background:#f04b28ff;color:#fff', res)
        log && log.info({
          url: _url,
          statusCode: res.statusCode,
          msg: res?.data?.message || res?.data?.msg || res?.data?.error || '',
        })
      }
    })
  })
}

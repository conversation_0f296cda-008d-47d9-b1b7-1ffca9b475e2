import storage from "./storage"
import {global} from "../index.js"

const dayJs = require('dayjs')

export function getUser() {
  const wInfo = wx.getStorageSync(global.STORAGE_GLOBAL_WECHAT_INFO)
  return wInfo || {}
}

export function setUser(info) {
  return wx.setStorageSync(global.STORAGE_GLOBAL_WECHAT_INFO, info)
}

export function getCurrRoleType() {
  const customerType = wx.getStorageSync(global.STORAGE_GLOBAL_CUSTOMER_ROLE_STRING)
  const cType = getApp()?.globalData?.customerType || ''
  return cType || customerType || 'CUSTOMER'
}

export function setCurrRoleType(type = 'CUSTOMER') {
  if (getApp()?.globalData){
    getApp().globalData.customerType = type
  }
  return wx.setStorageSync(global.STORAGE_GLOBAL_CUSTOMER_ROLE_STRING, type)
}

export function getSystemInfo() {
  const sysInfo = wx.getStorageSync(global.STORAGE_GLOBAL_SYSTEM_INFO)
  return sysInfo || {}
}

export function setSystemInfo(info) {
  return wx.setStorageSync(global.STORAGE_GLOBAL_SYSTEM_INFO, info)
}

export function getCustomerTypeInt() {
  const staffType = wx.getStorageSync(global.STORAGE_GLOBAL_CUSTOMER_ROLE_INT)
  return staffType || 0
}

export function setCustomerTypeInt(type) {
  return wx.setStorage({key: global.STORAGE_GLOBAL_CUSTOMER_ROLE_INT, data: type})
}

export function setChannelParamsInfo(info) {
  return wx.setStorageSync(global.STORAGE_GLOBAL_REG_AREA_INFO, info)
}

export function getChannelParamsInfo() {
  return wx.getStorageSync(global.STORAGE_GLOBAL_REG_AREA_INFO)
}

export function setChannelNameTips(name) {
  return wx.setStorageSync(global.STORAGE_GLOBAL_REG_CHANNEL_NAME, name)
}

export function getChannelNameTips() {
  return wx.getStorageSync(global.STORAGE_GLOBAL_REG_CHANNEL_NAME)
}

export function saveVersionInfo(info) {
  return wx.setStorageSync('WX_VERSION_INFO', info)
}

export function getVersionInfo() {
  return wx.getStorageSync('WX_VERSION_INFO')
}

export function setUserLogin(boolean) {
  if (typeof boolean === 'string'){
    if (boolean === 'true' || boolean === 'ture'){
      boolean = true
    }

    if (boolean === 'false' || boolean === 'flase'){
      boolean = false
    }
  }
  return wx.setStorageSync(global.STORAGE_GLOBAL_HAS_LOGIN, boolean)
}

export function getUserLogin() {
  const hasLogin = wx.getStorageSync(global.STORAGE_GLOBAL_HAS_LOGIN)
  if (typeof hasLogin === 'string' && hasLogin === ''){
    return false
  }

  return hasLogin
}

export function setToken(token) {
  getApp().globalData.token = token
  return wx.setStorageSync('token', token)
}

export function getToken() {
  return wx.getStorageSync('token') || ''
}

export function setOpenId(openid) {
  return wx.setStorageSync('openid', openid)
}

export function getOpenId() {
  return wx.getStorageSync('openid') || ''
}

export function getUnionID() {
  return wx.getStorageSync('unionid') || ''
}

export function setUnionID(unionid) {
  return wx.setStorageSync('unionid', unionid)
}

export function setUserId(id) {
  return wx.setStorageSync('userId', id)
}

export function getUserId() {
  return wx.getStorageSync('userId') || ''
}

export function getUserPhone() {
  const {phone} = getUser() || {}
  return wx.getStorageSync('phone') || phone || ""
}

export function setWechatInfoId(wechatInfoId) {
  return wx.setStorageSync('wechatInfoId', wechatInfoId) || ''
}

export function getWechatInfoId() {
  return wx.getStorageSync('wechatInfoId') || ''
}

export function setWebviewMoment(status) {
  return wx.setStorageSync(global.STORAGE_GLOBAL_SHARE_MOMENT, status)
}

export function getWebviewMoment() {
  return wx.getStorageSync(global.STORAGE_GLOBAL_SHARE_MOMENT)
}

export function setAppHoldStatus(status) {
  return wx.setStorage({key: global.STORAGE_GLOBAL_APP_HOLD, data: status})
}

export function getAppHoldStatus() {
  return wx.getStorageSync(global.STORAGE_GLOBAL_APP_HOLD)
}

export function getSpringboardStatus() {
  return wx.getStorageSync(global.STORAGE_GLOBAL_SPRINGBOARD_STATUS)
}

export function setOUInfo(params) {
  return wx.setStorageSync(global.STORAGE_GLOBAL_BREAK_IN_BASE_PARAMS, params)
}

export function getOUInfo() {
  return wx.getStorageSync(global.STORAGE_GLOBAL_BREAK_IN_BASE_PARAMS)
}

export function setSpringboardStatus(status) {
  return wx.setStorageSync(global.STORAGE_GLOBAL_SPRINGBOARD_STATUS, status)
}

export function getLoadingMoment() {
  return wx.getStorageSync(global.STORAGE_GLOBAL_LOADING_MOMENT)
}

export function setLoadingMoment(status) {
  return wx.setStorageSync(global.STORAGE_GLOBAL_LOADING_MOMENT, status)
}

export function setStartUpRoutePageType(data) {
  return wx.setStorageSync(global.STORAGE_GLOBAL_START_UP_ROUTE_PAGE_TYPE, data)
}

export function getStartUpRoutePageType() {
  return wx.getStorageSync(global.STORAGE_GLOBAL_START_UP_ROUTE_PAGE_TYPE)
}

export function setUserRole(role) {
  return wx.setStorageSync(global.STORAGE_GLOBAL_USER_ROLE, role)
}

export function getUserRole() {
  return wx.getStorageSync(global.STORAGE_GLOBAL_USER_ROLE)
}

export function setFirstPath(path) {
  return wx.setStorageSync(global.STORAGE_GLOBAL_FIRST_TAB_PATH, path)
}

export function getFirstPath() {
  let fPath = wx.getStorageSync(global.STORAGE_GLOBAL_FIRST_TAB_PATH) || ''
  const roleType = getCurrRoleType()
  if (!fPath && roleType === 'AGENCY'){
    fPath = '/pages/product/index'
  }

  if (!fPath){
    fPath = '/pages/home/<USER>'
  }

  console.log('FirstPath >>>>>', fPath)
  return fPath
}

export function setTabBarConfig(config) {
  return wx.setStorageSync(global.STORAGE_GLOBAL_TAB_BAR_CONFIG, config)
}

export function getTabBarConfig() {
  return wx.getStorageSync(global.STORAGE_GLOBAL_TAB_BAR_CONFIG)
}

export function setTabBarList(list) {
  return wx.setStorageSync(global.STORAGE_GLOBAL_TAB_BAR_LIST, list)
}

export function getTabBarList() {
  return wx.getStorageSync(global.STORAGE_GLOBAL_TAB_BAR_LIST)
}

export function setShortLink(link) {
  return wx.setStorageSync(global.STORAGE_GLOBAL_SHORT_LINK, link)
}

export function getShortLink() {
  return wx.getStorageSync(global.STORAGE_GLOBAL_SHORT_LINK)
}

export function setTabPageCardInfo(info) {
  return wx.setStorageSync(global.STORAGE_GLOBAL_TAB_PAGE_CARD_INFO, info)
}

export function getTabPageCardInfo() {
  return wx.getStorageSync(global.STORAGE_GLOBAL_TAB_PAGE_CARD_INFO)
}

export function setPreviousPath(path) {
  return wx.setStorageSync(global.STORAGE_GLOBAL_PREVIOUS_PATH, path)
}

export function getPreviousPath() {
  return wx.getStorageSync(global.STORAGE_GLOBAL_PREVIOUS_PATH)
}

export function setViolationsPhone(phone) {
  return wx.setStorageSync(global.STORAGE_GLOBAL_SHOW_VIOLATIONS_PHONE, phone)
}

export function getViolationsPhone() {
  return wx.getStorageSync(global.STORAGE_GLOBAL_SHOW_VIOLATIONS_PHONE)
}

export function setRouterInRegister(params) {
  return wx.setStorageSync(global.STORAGE_GLOBAL_ROUTER_FOR_REGISTER, params)
}

export function getRouterInRegister() {
  return wx.getStorageSync(global.STORAGE_GLOBAL_ROUTER_FOR_REGISTER)
}

export function setTempChannelAvatar(url) {
  return wx.setStorageSync(global.STORAGE_GLOBAL_TEMP_CHANNEL_AVATAR, url)
}

export function getTempChannelAvatar() {
  return wx.getStorageSync(global.STORAGE_GLOBAL_TEMP_CHANNEL_AVATAR)
}

export function setTempAgencyAvatar(url) {
  return wx.setStorageSync(global.STORAGE_GLOBAL_TEMP_AGENCY_AVATAR, url)
}

export function getTempAgencyAvatar() {
  return wx.getStorageSync(global.STORAGE_GLOBAL_TEMP_AGENCY_AVATAR)
}

export function setMarKDayStatus(status) {
  return wx.setStorageSync(global.STORAGE_GLOBAL_MARK_DAY_STATUS, status)
}

export function getMarkDayStatus() {
  return wx.getStorageSync(global.STORAGE_GLOBAL_MARK_DAY_STATUS)
}

export function setStartUpHasCover(boolean) {
  if (typeof boolean === 'string'){
    if (boolean === 'true' || boolean === 'ture'){
      boolean = true
    }

    if (boolean === 'false' || boolean === 'flase'){
      boolean = false
    }
  }
  return wx.setStorageSync(global.STORAGE_GLOBAL_START_UP_HAS_CHANNEL_COVER, boolean)
}

export function getStartUpHasCover() {
  const hasCover = wx.getStorageSync(global.STORAGE_GLOBAL_START_UP_HAS_CHANNEL_COVER)
  if (typeof hasCover === 'string' && hasCover === ''){
    return false
  }

  return hasCover
}

export function setAttentionDays(day = dayJs(new Date()).format('YYYY-MM-DD')) {
  const arrDays = wx.getStorageSync(global.STORAGE_GLOBAL_ATTENTION_STATUS) || []
  if (arrDays && arrDays.length && arrDays.indexOf(`${day}`) !== -1){
    return arrDays
  } else {
    arrDays.push(`${day}`)
    return wx.setStorageSync(global.STORAGE_GLOBAL_ATTENTION_STATUS, arrDays)
  }
}

export function getAttentionDays(day = dayJs(new Date()).format('YYYY-MM-DD')) {
  const recordDays = wx.getStorageSync(global.STORAGE_GLOBAL_ATTENTION_STATUS) || []
  if (recordDays && recordDays.length){
    if (recordDays.indexOf(`${day}`) !== -1){
      return true
    }

    return false
  } else {
    return false
  }
}

export function setStartUpCover(cover) {
  return wx.setStorageSync(global.STORAGE_GLOBAL_START_UP_CHANNEL_COVER, cover)
}

export function getStartUpCover() {
  return wx.getStorageSync(global.STORAGE_GLOBAL_START_UP_CHANNEL_COVER)
}

export function setLoadRawStatus(boolean) {
  if (typeof boolean === 'string'){
    if (boolean === 'true' || boolean === 'ture'){
      boolean = true
    }

    if (boolean === 'false' || boolean === 'flase'){
      boolean = false
    }
  }
  return wx.setStorageSync('LOAD_RAW_STATUS', boolean)
}

export function getLoadRawStatus() {
  const rawStatus = wx.getStorageSync('LOAD_RAW_STATUS')
  if (typeof rawStatus === 'string' && rawStatus === ''){
    return false
  }

  return rawStatus
}


/**
 * setStorageFactoryValue
 * @param key
 * @param value
 */
export function setSFV(key, value) {
  return wx.setStorageSync(key, value)
}

/**
 * getStorageFactoryValue
 * @param key
 * @returns {any}
 */
export function getSFV(key) {
  return wx.getStorageSync(key)
}

export function getUserAppKey() {
  const user = getUser()
  if (!user || !user?.name1) return null
  return user.name1
}

export function deleteUser() {
  storage.removeStorage(global.STORAGE_GLOBAL_WECHAT_INFO)
  storage.removeStorage(global.STORAGE_GLOBAL_HAS_LOGIN)
  storage.removeStorage(global.STORAGE_GLOBAL_USER_ROLE)
  storage.removeStorage('user')
  return true
}

export function clear() {
  wx.clearStorageSync()
}

export function setSaveUserInfo(data = {},flag = true) {
  const { token = '', wechatInfoId = '', orgId = '', userId = '' } = data || {}
  if (token) {
    setToken(token)
    setUserId(userId)
  } else {
    if (flag) {
      setToken("")
    }
  }

  if (wechatInfoId){
    setWechatInfoId(wechatInfoId)
  }

  if (orgId){
    storage.setStorage('orgId', orgId)
  }

  let _wInfo = {
    ...getUser(),
    ...data,
  }
  setUser(_wInfo)
}

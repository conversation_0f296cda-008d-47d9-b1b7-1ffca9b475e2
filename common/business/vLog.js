/**
 * @Description: VLog
 * @version 1.0.3
 * <AUTHOR>
 * @date 2023/3/9
 */

/**
 * 使用说明
 * vLog.log('>>>> START_UP log信息 不上报 <<<<')
 * vLog.info('>>>> START_UP info信息 <<<<')
 * vLog.warn('>>>> START_UP 警告信息 <<<<')
 * vLog.error('>>>> START_UP 错误信息 <<<<')
 *
 * vLog('直接用')
 *
 * vLog('直接用', '多组', [1, 3, 4], false, '多类型')
 *
 * 事件上报
 * name: doReport 固定值
 * eventName：reportEventName.※ 枚举类型
 * info: 事件上报内容 Object类型
 * vLog({name: 'doReport', eventName: reportEventName.BI_wx_login, info: {key: 'Value'}})
 *
 * 日志上报
 * 调用 .report()方法
 * vLog.info('START_UP ORIGIN qs.parse(options) >>>>', qs.parse(options)).report()
 *
 * 过滤关键字，最多不超过1Kb，可以在小程序管理后台根据设置的内容搜索得到对应的日志。
 * vLog.setFilterMsg('openid')
 *
 * 是setFilterMsg的添加接口。用于设置多个过滤关键字。
 * vLog.addFilterMsg('wechatInfoId')
 *
 */

const log = wx.getRealtimeLogManager ? wx.getRealtimeLogManager() : null

const LOCAL_STATUS_COLOR = {
  'ERROR': 'red',
  'WARN': 'yellow',
  'INFO': '#c2c2c2',
  "LOG": 'white',
  'REQLOG': '#90e0ef',
  'REPLOG': '#fb5607',
  'TAG': 'purple',
  'EVENT_REPORT': 'Beige'
}

const VLOG_FUNC = ['log', 'info', 'warn', 'error', 'reqLog', 'repLog', 'table']

function formatLogArgs(args) {
  return args.map((arg) => {
    if (typeof arg === 'object'){
      return JSON.stringify(arg, null, 2);
    }
    return arg;
  });
}

function formatLogContent(args) {
  const logArgs = formatLogArgs(args);
  let logContent = []
  let logMark = ''
  if (Array.isArray(logArgs) && logArgs.length > 1){
    let logList = logArgs.slice(1)
    logMark = logArgs[0]
    logList.forEach((item) => {
      if (!`${item}`.startsWith('{') && !`${item}`.startsWith('[')){
        logContent.push(item)
      } else {
        logContent.push(JSON.parse(item + '' || '{}'))
      }
    })
  }
  if (!logMark){
    const pages = getCurrentPages();
    logMark = `${pages[pages.length - 1].route} : `;
  }
  if (!logArgs.length || !logContent.length){
    logContent = [].concat(logArgs, logContent)
  }

  return {
    logMark,
    logContent
  }
}

const consoleEntity = {
  'doReport': {
    subscription: async (event = {}) => {
      let {eventName = '', info = {}} = event
      eventName = `${eventName}`.toLocaleLowerCase()
      console.log(`%c ${eventName}`, `color:${LOCAL_STATUS_COLOR.EVENT_REPORT}`, info)
      return wx.reportEvent(eventName, {res: JSON.stringify(info)})
    }
  }
}

function vLog(...args) {
  let _args = args?.length === 1 ? args[0] : args

  if (Array.isArray(_args)){
    const time = new Date().toISOString();
    const logArgs = formatLogArgs(_args);
    console.log(`[${time}]`, ...logArgs);
  }

  if (Object.prototype.toString.call(_args) === "[object Object]" && _args.hasOwnProperty('name')){
    return consoleEntity[`${_args?.name}`].subscription(_args)
  }
}

for (let i = 0, type; type = VLOG_FUNC[i++];) {
  (function(type) {
    vLog[type] = (...args) => {
      const {logMark, logContent} = formatLogContent(args)
      console.log(`%c ${logMark}`, `color:${LOCAL_STATUS_COLOR[type.toLocaleUpperCase()]}`, ...logContent)

      return {
        report: () => {
          if (!log)
            return
          switch (type) {
            case 'log':
            case 'info':
            case 'reqLog':
            case 'repLog':
              log.info.apply(log, logContent)
              break
            case 'warn':
              log.warn.apply(log, logContent)
              break
            case 'error':
              log.error.apply(log, logContent)
              break
            case 'table':
              console.log(logContent)
              break

            default:
              break
          }
        }
      }
    }
  })(type)
}

vLog.setFilterMsg = (msg) => {
  if (!log || !log.setFilterMsg) return
  if (typeof msg !== 'string') return
  log.setFilterMsg(msg)
  console.log(`%c ${msg}`, `color:${LOCAL_STATUS_COLOR.TAG}`)
}

vLog.addFilterMsg = (msg) => {
  if (!log || !log.addFilterMsg) return
  if (typeof msg !== 'string') return
  log.addFilterMsg(msg)
  console.log(`%c ${msg}`, `color:${LOCAL_STATUS_COLOR.TAG}`)
}

export default vLog

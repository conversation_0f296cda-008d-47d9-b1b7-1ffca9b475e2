import {
  global,
  md5,
  storage,
  vLog,
} from "../index";

import {
  getAttentionDays,
  getFirstPath,
  getUser,
  setAttentionDays,
  setUser
} from "../utils/userStorage";

import {
  getAdvStaffCardInfo,
  getStaffCustomerType
} from "../nb/home";

const userContent = {
  'initWxInfo': {
    subscription: async (param = {}) => {
      wx.showLoading({
        title: '加载中...',
      })
      const {success, data} = await getAdvStaffCardInfo({timeStamp: +new Date()})
      console.log('initWxInfo success,data >>', success, data)
      wx.hideLoading()

      const _wInfoCache = storage.getStorage(global.STORAGE_GLOBAL_WECHAT_INFO_CACHE) || ''
      let userInfo = getUser()

      let _currWICache = md5.hexMD5(JSON.stringify(data))
      if (_wInfoCache && _wInfoCache == _currWICache){
        return Promise.resolve(userInfo)
      }

      if (success){
        const {orgId = ''} = data || {}
        storage.setStorage('orgId', orgId)

        userInfo = {
          ...userInfo,
          ...data
        }
        setUser(userInfo)
        storage.setStorage(global.STORAGE_GLOBAL_WECHAT_INFO_CACHE, _currWICache)
      }

      return Promise.resolve(userInfo)
    }
  },

  'getUserTypeInfo': {
    subscription: async (param = {}) => {
      console.log('getUserTypeInfo param >>', param)

      wx.showLoading({title: '加载中...'})
      const {success, data, msg = '', code} = await getStaffCustomerType({})
      wx.hideLoading()
      console.log('getUserTypeInfo data >>', data)
      if (!success){
        wx.showToast({
          title: msg,
          icon: 'none'
        })
        return Promise.resolve(msg)
      }

      return Promise.resolve(data)
    }
  },

  "showRecordModal": {
    subscription: (param = {}) => {
      const {params = {}} = param
      let {tabName = ''} = params || {}

      const fPath = getFirstPath()
      const dRecord = getAttentionDays()
      tabName = tabName.toLocaleLowerCase()
      if (!fPath.includes(tabName) || dRecord){
        return
      }

      setAttentionDays()
      return wx.showModal({
        title: '',
        content: '为给您提供更优质的服务，请您关注“广发基金木棉花”服务号',
        confirmText: '前往关注',
        showCancel: true,
        confirmColor: '#f04b28',
        cancelColor: '#696969',
        success(res) {
          const {confirm} = res || {}
          if (confirm){
            return wx.navigateTo({
              url: "/packages-common/pages/common/guide/index"
            })
          }
        }
      })
    }
  },
}

export default function userAction(params) {
  vLog.log(`action params ==>`, params)
  const {name = ''} = params || {}
  if (name && name in userContent){
    return userContent[`${name}`].subscription(params)
  }
  vLog.error(`类型错误 userAction:`, name)
}

import reViewConfig from '../const/review.js'
import { getPageTemplateInfo, getTemplateById, getUnionId, getStaffAuthRecord } from "../nb/home";
import { global, qs, enums } from "../index";
import { TabBarPath } from '../const/enum'
import { BREAK_IN_INIT_SUCCESS } from "../event/name";
const {
  ROLE_TYPE_IMAGE,
} = enums
import {
  setTabBarConfig,
  setFirstPath,
  getCurrRoleType,
  setTabBarList,
  setTabPageCardInfo,
  getTabBarList,
} from '../utils/userStorage'

const log = wx.getRealtimeLogManager ? wx.getRealtimeLogManager() : null

const isEmptyObject = (obj = {}) => {
  return Object.keys(obj).length === 0;
}

const isGoodJSON = (str) => {
  try {
    JSON.parse(str)  // 如果抛出异常，则会从这条语句终止，被catch捕捉
    return true
  } catch (e) {
    return false
  }
};

const doJSON_PARSE = (obj = '') => {
  if (typeof obj === 'string') {
    if (obj.startsWith('{') && obj.endsWith('}') && isGoodJSON(obj)) {
      obj = JSON.parse(obj)
      for (let [key, value] of Object.entries(obj)) {
        obj[key] = doJSON_PARSE(value)
      }
    }
  }
  return obj
}

const convertObj = {
  'tabBar': {
    subscription: async (event = {}) => {
      let { initType = '', currRoleType = '' } = event
      let customerType = currRoleType || getCurrRoleType()
      if (customerType === "CUSTOMER") {
        const { code = '' } = await wx.login()
        const { param = {} } = await getUnionId({ code, wechatCode: global.SOURCE_CODE })
        if (param?.openid && param?.unionid) {
          const { data } = await getStaffAuthRecord(param)
          customerType = ROLE_TYPE_IMAGE[data?.staffType || 3]
        }
      }
      console.info('TAB_BAR event,customerType >>>>', event, customerType)
      log && log?.info({
        page: 'TAB_BAR',
        event: JSON.stringify(event),
        customerType
      })
      if (!initType) {
        const tabList = (getApp().globalData.tabList)?.length ? getApp().globalData.tabList : getTabBarList() || []
        if (!tabList || !tabList.length) {
          initType = 'START_UP'
        }
      }

      // 启动初始化
      if (initType && `${initType}`.toUpperCase() === 'START_UP') {
        const { list, params } = await convert({ name: 'getTabInfo', customerType: customerType })
        console.info('TAB_BAR tabBarList list, params >>>', list, params)
        log && log?.info({
          page: 'TAB_BAR',
          listLength: list.length,
          paramsLength: params.length
        })
        const { tabPath = '', type: topKey = '' } = list[0] || {}
        let firstPath = tabPath || TabBarPath[topKey] || '/pages/home/<USER>'
        console.log('TAB_BAR tabPath >>>', tabPath)
        console.log('TAB_BAR firstPath>>>', firstPath)
        log && log?.info({
          page: 'TAB_BAR',
          tabPath,
          firstPath,
        })

        setFirstPath(firstPath)
        if (getApp()?.globalData) {
          getApp().globalData.tabList = list
        }

        getApp().event.emit(BREAK_IN_INIT_SUCCESS)
        if (params && params.length) {
          const { list: cardList = [] } = await convert({ name: 'getTabPageCardInfo', params: params, tList: list })
          console.log(`TAB_BAR cardList >>`, cardList)
          console.info('TAB_BAR tabBarList cardList >>>>', cardList)
          if (getApp()?.globalData) {
            getApp().globalData.cardList = cardList
          }
        }
      }
    }
  },

  'getTabInfo': {
    subscription: async (event = {}) => {
      const { customerType } = event
      const { data, success, msg } = await getPageTemplateInfo({ customerType })
      console.info('TAB_BAR getPageTemplateInfo data,success,msg >>>>', data, success, msg)

      let _tabBarConfig = {}
      let _tList = []
      let _nList = []
      let _fetchParam = []

      log && log?.info({
        page: 'TAB_BAR',
        function: 'getTabInfo',
        data: JSON.stringify(data),
        success,
        msg
      })

      if (!success) {
        log && log?.error({
          page: 'TAB_BAR',
          function: 'getTabInfo',
          error: msg
        })
        setFirstPath('/pages/home/<USER>')
        const { list = [] } = qs.parse(reViewConfig.tabBar) || {}
        setTabBarList(list)
        let tConfig = {}
        list.forEach((lItem) => {
          const { pageTabBarConf, ordinal } = lItem || {}
          tConfig[ordinal * 1] = pageTabBarConf?.tabName
        })
        setTabBarConfig(tConfig) // TAB_NAME tab的枚举标识
        return { list, params: _fetchParam }
      }

      if (data && Array.isArray(data)) {
        // 过滤
        _tList = data.filter(item => item && item.status === 'ENABLE')

        // 排序
        _tList = _tList.sort(function (a, b) {
          return a.ordinal - b.ordinal
        })

        // 解析
        _tList.forEach((item, index) => {
          const {
            pageTabBarConf = '{}',
            icon = '{}',
            ordinal,
            id = ''
          } = item || {}

          const tConfig = JSON.parse(`${pageTabBarConf}`)
          const tIcon = JSON.parse(`${icon}`)

          const { tabName = '' } = tConfig || {}
          _fetchParam.push({ admin: false, pageId: id })
          _tabBarConfig[ordinal * 1] = tabName

          const props = {
            ...item,
            pageShow: true,
            type: ordinal * 1 || index,
            tabPath: TabBarPath[tabName],
            tabName,
            icon: tIcon,
            pageTabBarConf: tConfig
          }

          _nList.push(props)
        })
        if (_tabBarConfig && !isEmptyObject(_tabBarConfig)) {
          setTabBarConfig(_tabBarConfig)
        }
        setTabBarList(_nList)
        return { list: _nList, params: _fetchParam }
      }
    }
  },

  'getTabPageCardInfo': {
    subscription: async (event = {}) => {
      const { params = [], tList = [] } = event
      console.log('TAB_BAR getTabPageCardInfo event >>>>>', event)
      console.info('TAB_BAR getTabPageCardInfo params,tList >>>>>', params.length, tList.length)

      log && log?.info({
        page: 'TAB_BAR',
        tListLength: tList.length,
        paramsLength: params.length
      })
      const _nList = [].concat(tList)
      await Promise.all(params.map(item => getTemplateById({ ...item })))
        .then(res => {
          if (res && res.length) {
            res.forEach((tItem, tIndex) => {
              const { data, success } = tItem || {}
              const _filerCardInfos = []
              if (success) {
                const { cardInfos = [] } = data || {}
                cardInfos.forEach((cItem, index) => {
                  const props = {
                    ...cItem,
                    cardIndex: index,
                    cardLast: index === cardInfos.length - 1,
                    conf: '{}',
                    finalConf: doJSON_PARSE(cItem?.finalConf)
                  }
                  _filerCardInfos.push(props)
                })
                _nList[tIndex]['cardInfos'] = _filerCardInfos
              }
            })
            return _nList
          }
        })
        .then((result) => {
          if (getApp()?.globalData) {
            getApp().globalData.cardList = result
          }
          setTabPageCardInfo(result)
        })
        .catch(err => {
          log && log?.error({
            page: 'TAB_BAR',
            function: 'getTabPageCardInfo',
            err
          })
        })
      console.info('TAB_BAR getTabPageCardInfo _nList >>>>>', _nList)
      log && log?.info({
        page: 'TAB_BAR',
        nListLength: _nList.length
      })
      return { list: _nList }
    }
  }

}

export default function convert(params = {}) {
  const { name = '' } = params
  return convertObj[name].subscription(params)
}

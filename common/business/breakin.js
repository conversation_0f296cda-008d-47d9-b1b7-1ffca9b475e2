import {
  checkLogin,
  getAttentionStatus,
  getContentCategoryFilter,
  getFindWechatInfo,
  getStaffAuthRecord,
  getUCSystemApiConfig,
  getUnionId,
  getCheckBing,
  getPageTemplateInfo,
  getTemplateById,
  getAdvStaffCardInfo
} from "../nb/home";

import {
  convert,
  global,
  interaction,
  qs,
  storage,
  wbs,
  vLog,
  store,
  shapeShift
} from "../index";

import FormData from '../../lib/form-data/formData.js'

import {
  getCurrRoleType,
  getCustomerTypeInt,
  getOpenId,
  getSFV,
  setSFV,
  getSystemInfo,
  getToken,
  getUnionID,
  getUser,
  getUserLogin,
  getUserRole,
  saveVersionInfo,
  setCurrRoleType,
  setCustomerTypeInt,
  setOpenId,
  setPreviousPath,
  setSystemInfo,
  setTempAgencyAvatar,
  setTempChannelAvatar,
  setToken,
  setUnionID,
  setUserLogin,
  setUserRole,
  setMarKDayStatus,
  setStartUpHasCover,
  setStartUpCover,
  setOUInfo,
  getOUInfo,
  setAttentionDays,
  setLoadRawStatus,
  getAttentionDays,
  getWechatInfoId,
  getRouterInRegister,
  setRouterInRegister,
  setStartUpRoutePageType,
  getTabBarList,
  setShortLink, getVersionInfo, setUserId, getUserId
} from "../utils/userStorage";

import {
  BREAK_IN_INIT_SUCCESS,
  REFRESH_PAGE_DATA,
  SEND_REGISTER_OPTIONS,
  SEND_WEBVIEW_OPTIONS,
  SET_SHOW_RECORD,
  TAB_LISTENER_EVENT,
} from "../event/name";

import {
  CARD_SURROUND,
  CHANGE_REALM,
  LOGIN_VISITOR,
  LoginState,
  MARK_DAY_COVER,
  FILE_TYPES,
  ROLE_TYPE_IMAGE,
  GLOBAL_START_PATH,
  TAB_BAR_PATH,
  ROLE_TYPE,
  REGISTER_TYPE,
  EnterSource,
  SHARE_IMG_DEFAULT,
  CUSTOMER_RESOLVE,
  DISABLE_SHARE_PAGE,
  LOGIN_SITE,
  LOGIN_SITE_STR,
  MARKET_CLUE_DEF
} from "../const/enum";

import {
  doUrlDeCode,
  doJSON_PARSE,
  isEmptyObject,
  panDClone,
  repairLaunchPath
} from "../utils/util";

const FILTER_ROUTE_KEYS = ['targetPath', 'perfix', 'value', 'newUrl', 'blackBox', 'captchaToken', 'code', 'banShare']
const FILTER_QR_KEY = ['routerPage', 'rolePage']

const reviewFilter = [
  'live',
  'multimedia'
]

const REVIEW_STATUS = ['develop', 'trial']

const dayJs = require('dayjs')

const ERROR_STATUS = ['null', 'undefined', '', '[object Object]']
const VISITOR_STATUS = [
  LoginState.ROLE_IS_VISITOR,
  LoginState.ABSENCE
]
const log = wx.getRealtimeLogManager ? wx.getRealtimeLogManager() : null
const staffTypeStr = {
  0: '渠道门户',
  1: '机构门户',
  2: '普通客户门户'
}

const breakContent = {
  // 初始化-启动
  'initUnionId': {
    subscription: async () => {
      const { code = '' } = await wx.login()
      const { param = {} } = await getUnionId({ code, wechatCode: global.SOURCE_CODE })
      const baseParams = param || getOUInfo() || {}
      log && log?.info({
        page: 'BREAK_IN',
        function: 'initUnionId',
        params: baseParams
      })
      setLoadRawStatus(true)

      if (baseParams && !isEmptyObject(baseParams) && baseParams.openid) {
        await getStaffAuthRecord(baseParams)
          .then(async res => {
            const { success, data = {}, code } = res || {}
            log && log?.info({
              page: 'BREAK_IN',
              function: 'getStaffAuthRecord',
              res: res
            })
            getApp().sensors.registerApp({
              site_type: staffTypeStr[data?.staffType],
              app_type: 'miniprogram'
            });
            if (VISITOR_STATUS.includes(code * 1)) {
              setUserLogin(false)
              setUserRole(30035)
            } else {
              setUserLogin(true)
              setUserRole(0)
            }

            setLoadRawStatus(false)
            if (success && data) {
              const { openid, unionid, staffType, userId } = data || {}
              let customerType = ROLE_TYPE_IMAGE[staffType] || 'CUSTOMER'
              await breakIn({ name: 'doCheckLogin', params: { openid, unionid, customerType: staffType } })
              convert({ name: 'tabBar', initType: 'START_UP', currRoleType: customerType })
              setCustomerTypeInt(staffType)
              setCurrRoleType(customerType)
              setOpenId(openid)
              setUnionID(unionid)
              setUserId(userId)
            } else {
              setCustomerTypeInt(3)
              convert({ name: 'tabBar', initType: 'START_UP', currRoleType: 'CUSTOMER' })
              setCurrRoleType('CUSTOMER')
            }
          })
          .catch(error => {
            log && log?.error({
              page: 'BREAK_IN',
              function: 'getStaffAuthRecord',
              error: error
            })
          })

        return
      }

      await wx.login({
        success(res) {
          const { code = '' } = res || {}
          if (code) {
            getUnionId({ code, wechatCode: global.SOURCE_CODE })
              .then(result => {
                const { success, param = {} } = result || {}
                log && log?.info({
                  page: 'BREAK_IN',
                  function: 'getUnionId',
                  result: result
                })
                setOUInfo(param)
                if (success) {
                  getStaffAuthRecord(param)
                    .then(async authRes => {
                      const { success: cSucc, data: cData = {}, code: cCode } = authRes || {}
                      log && log?.info({
                        page: 'BREAK_IN',
                        function: 'getStaffAuthRecord',
                        result: cData
                      })
                      getApp().sensors.registerApp({
                        site_type: staffTypeStr[data?.staffType],
                        app_type: 'miniprogram'
                      });
                      if (VISITOR_STATUS.includes(cCode * 1)) {
                        setUserLogin(false)
                        setUserRole(30035)
                      } else {
                        setUserLogin(true)
                        setUserRole(0)
                      }
                      setLoadRawStatus(false)
                      if (cSucc && cData) {
                        const { openid, unionid, staffType, userId } = cData || {}
                        let customerType = ROLE_TYPE_IMAGE[staffType] || 'CUSTOMER'
                        await breakIn({ name: 'doCheckLogin', params: { openid, unionid, customerType: staffType } })
                        convert({ name: 'tabBar', initType: 'START_UP', currRoleType: customerType })
                        setCustomerTypeInt(staffType)
                        setCurrRoleType(customerType)
                        setOpenId(openid)
                        setUnionID(unionid)
                        setUserId(userId)
                      } else {
                        setCustomerTypeInt(3)
                        convert({ name: 'tabBar', initType: 'START_UP', currRoleType: 'CUSTOMER' })
                        setCurrRoleType('CUSTOMER')
                        setOpenId(param?.openid)
                        setUnionID(param?.unionid)
                      }
                    })
                    .catch(error => {
                      log && log?.error({
                        page: 'BREAK_IN',
                        function: 'getStaffAuthRecord',
                        err: error
                      })
                    })
                }
              })
              .catch(error => {
                log && log?.error({
                  page: 'BREAK_IN',
                  function: 'getUnionId',
                  err: error
                })
              })
          }
        },
        fail(err) {
          log && log?.error({
            page: 'BREAK_IN',
            function: 'wx.login',
            err: err
          })
        }
      })
    }
  },

  // 初始化-全局配置
  'initSys': {
    subscription: async () => {
      let customerType = getCurrRoleType()
      const { success, data, code, msg } = await getUCSystemApiConfig({ customerType })
      console.log('BREAK_IN initSys customerType >>>', customerType)
      console.log('BREAK_IN initSys getUCSystemApiConfig >>>', success, data, code, msg)
      log && log?.info({
        page: 'BREAK_IN',
        function: 'initSys',
        data: data
      })
      if (success && code == 0) {
        let sysInfo = { ...data }
        for (const [key, value] of Object.entries(sysInfo)) {

          if (Array.isArray(value)) {
            sysInfo[key] = [].concat(value)
          } else if (value instanceof Object) {
            sysInfo[key] = { ...value }
          } else {
            sysInfo[key] = doJSON_PARSE(value || '{}' + '')
          }
        }
        console.log('BREAK_IN initSys sysInfo >>>', sysInfo)
        setSystemInfo(sysInfo)

        // breakIn({name: 'setReportUrlLink', params: {}})
      }
    }
  },

  // 初始化-切换站点
  'resetTabInfo': {
    subscription: async () => {
      const type = getCurrRoleType()
      console.log(`BREAK_IN resetTabInfo type:`, type)
      log && log?.info({
        page: 'BREAK_IN',
        function: 'resetTabInfo',
        type: type
      })
      await convert({ name: 'tabBar', initType: 'START_UP', currRoleType: type })
    }
  },

  // 设置神策数据
  'registerApp': {
    subscription: async () => {
      const baseParams = getOUInfo() || {}
      if (baseParams.openid) {
        getApp().sensors.identify(baseParams.openid);
      }
      const { success, data } = await getAdvStaffCardInfo({ timeStamp: +new Date() })
      if (success) {
        getApp().sensors.registerApp({
          aim_username: data.name, // 姓名
          aim_userid: data.userId,// 木棉花用户id
          aim_user_type: staffTypeStr[getCurrRoleType()],// 客户类型  渠道客户、机构客户、普通客户、其他
          active_time: data.activatedTime,// 激活时间
          mobileno: data.phone,// 手机号码
          // is_subscribe: user.name,// 木棉花公众号关注状态
          user_status: data.accountStatus,// 账号状态
          unionid: getUnionID(),// 微信unionid
          agent: data.orgName,// 关联渠道
          agent_master: data.principal,// 渠道负责人
          agent_l1: data.companyName,// 一级渠道
          agent_l2: data.secondOrgName,// 二级渠道
          sginup_type: data.sourceType,// 注册来源
          sginup_inviter: data.InviterStaffModel,// 注册邀请人
          agency_master: data.inviterStaffName,// 机构销售负责人
          agency_department: data.department,// 机构所属部门
        });
      }
    }
  },

  // 初始化-版本信息
  'initVersionInfo': {
    subscription: () => {
      const {
        appId = '',
        envVersion = '',
        version = ''
      } = wx.getAccountInfoSync().miniProgram || {}

      let vTime = dayJs(new Date()).format('YYYY-MM-DD HH:mm')
      saveVersionInfo({ appId, envVersion, version: version ? version : '1.0.0', vTime })
    }
  },

  // 初始化-默哀模式
  'initCurrentMarkDay': {
    subscription: () => {
      console.log('BREAK_IN initCurrentMarkDay')
      const sysInfo = getSystemInfo()
      const customerType = getCurrRoleType()

      const _startDate = sysInfo?.staff?.clientParams?.grayscale?.startDate || '2030-12-13 00:00:00'
      const _endDate = sysInfo?.staff?.clientParams?.grayscale?.endDate || '2030-12-13 23:59:59'
      let _defCover = sysInfo?.staff?.clientParams?.startUpConfig?.defaultImgUrl || ''
      switch (customerType) {
        case 'CUSTOMER': // 普客
          _defCover = sysInfo?.common?.startUpImg ? sysInfo.common.startUpImg + '?x-oss-process=image/resize,m_fill,h_1624,w_750' : ''
          break

        case 'AGENCY': //机构
          _defCover = sysInfo?.agency?.startUpImg ? sysInfo.agency.startUpImg + '?x-oss-process=image/resize,m_fill,h_1624,w_750' : ''
          break

        default:
          break
      }

      const currDay = dayJs(new Date()).format('YYYY-MM-DD HH:mm:ss')

      let _diffBefore = dayJs(currDay).diff(dayJs(_startDate), 'seconds')
      let _diffAfter = dayJs(currDay).diff(dayJs(_endDate), 'seconds')

      console.log('BREAK_IN _diffBefore >>>>', _diffBefore)
      console.log('BREAK_IN _diffAfter >>>>', _diffAfter)

      if (_diffBefore >= 0 && _diffAfter <= 0) {
        setMarKDayStatus('LIVE')
        getApp().globalData.isMarkDayStatus = 'LIVE'
        breakIn({
          name: 'doSetMiniCoverUrl',
          params: {
            hasChannel: true,
            channelImgUrl: MARK_DAY_COVER
          }
        })
      } else {
        setMarKDayStatus('NULL')
        getApp().globalData.isMarkDayStatus = 'NULL'
        setStartUpHasCover(!!_defCover)
        setStartUpCover(_defCover)
      }
    }
  },

  // 跳转登录
  'doCheckJumpIn': {
    subscription: (event = {}) => {
      log && log?.info({
        page: 'BREAK_IN',
        function: 'doCheckJumpIn',
        event: event
      })
      const { params = {} } = event || {}
      const _currCode = getUserRole()
      const hasLogin = getUserLogin()
      if (!hasLogin || LOGIN_VISITOR.includes(_currCode * 1)) {
        let pagePath = getCurrentPages().pop();
        log && log?.info({
          page: 'BREAK_IN',
          function: 'doCheckJumpIn',
          pagePath: pagePath?.route
        })
        setPreviousPath(pagePath?.route)
        wx.showToast({ title: '当前为游客，该内容限注册用户可见。请先登录。', icon: "none" })
        setTimeout(() => {
          return wx.navigateTo({
            url: '/pages/user/login/index',
            success(res) {
              console.log('BREAK_IN doCheckJumpIn params >>>', params)
              res.eventChannel.emit(SEND_REGISTER_OPTIONS, params)
            }
          })
        }, 500);
        return
      } else {
        switch (_currCode * 1) {
          case LoginState.INREVIEW: {// 账号审核中，请稍后登录 => 跳转审核中页面
            interaction.showToast('账号审核中')
            setUserLogin(false)
            setUserRole(_currCode * 1)
            let url = encodeURIComponent(`${wbs.gfH5}/share/advInviteRegister?result=${LoginState.INREVIEW}`)
            return wx.reLaunch({
              url: `/pages/common/webview/webPage?url=${url}&hideHomeNav=1&banShare=1`,
            })
          }

          case LoginState.FORBIDDEN: { //禁用 => 跳转禁用页面
            setUserLogin(false)
            setUserRole(_currCode * 1)
            interaction.showToast('当前账号已禁用，请联系管理员')
            return wx.reLaunch({
              url: '/packages-user/pages/applyFail/applyFail',
            })
          }
        }
      }
    }
  },

  // 跳转详情页
  'doRouteWebPage': {
    subscription: (event = {}) => {
      const { params = {} } = event
      return wx.navigateTo({
        url: "/pages/common/webview/webPage",
        success(res) {
          res.eventChannel.emit(SEND_WEBVIEW_OPTIONS, params)
        }
      })
    }
  },

  // 查询用户状态
  'doCheckLogin': {
    subscription: async (event = {}) => {
      let executed = 'FAILED'
      let { params = {} } = event

      console.info(`BREAK_IN doCheckLogin params >>`, params)
      log && log?.info({
        page: 'BREAK_IN',
        function: 'doCheckLogin',
        params: params
      })
      if (!params || isEmptyObject(params) || !params?.openid) {
        params = {
          openid: getOpenId(),
          unionid: getUnionID(),
          customerType: getCustomerTypeInt()
        }
        console.info(`BREAK_IN doCheckLogin after params >>`, params)
      }
      const { code: wxcode } = await wx.login()
      const { msg, success, data, code } = await checkLogin({ code: wxcode, customerType: params.customerType, wechatCode: global.SOURCE_CODE }).catch(error => {
        log && log?.error({
          page: 'BREAK_IN',
          function: 'doCheckLogin',
          error: error
        })
      })
      if (code === 0 && data?.userId) {
        getApp().sensors.login("AIM-" + data.userId);
      }
      getApp().sensors.track('login', {
        login_site: LOGIN_SITE_STR[params.customerType],
        account_type: code === 0 ? '手机号' : '游客',
        is_success: success,
        fail_reason: success ? '' : msg
      })

      console.info(`BREAK_IN doCheckLogin msg, success,data, code >>`, msg, success, data, code)
      log && log?.info({
        page: 'BREAK_IN',
        function: 'doCheckLogin',
        rest: data
      })
      const { token = '', userId } = data || {}
      // 如果是相同站点 保存token
      if (token) {
        setToken(token)
        setUserId(userId)
        executed = 'DONE'
      } else {
        setToken('')
        setUserId('')
      }

      const resCode = code * 1
      setUserRole(resCode)
      switch (resCode) {
        case LoginState.REGISTER: // 注册
        case LoginState.ABSENCE: {
          // 用户不存在，或未完善微信授权,
          setUserLogin(false)
          setUserRole(resCode)
          break
        }
        case LoginState.ACTIVATED: // 已激活
        case LoginState.NOTACTIVATED: // 账号未激活
        case LoginState.HASBINDUSER: //该微信已绑定过用户
        case LoginState.HASBINDWECHAT: // 已绑定过微信，是否绑定当前微信
          break

        // 账号审核中，请稍后登录 => 跳转审核中页面
        case LoginState.INREVIEW: {
          setUserLogin(false)
          setUserRole(resCode)
          let url = encodeURIComponent(`${wbs.gfH5}/share/advInviteRegister?result=${LoginState.INREVIEW}`)
          wx.reLaunch({
            url: `/pages/common/webview/webPage?url=${url}&hideHomeNav=1&banShare=1`
          })
        }

        //禁用 => 跳转禁用页面
        case LoginState.FORBIDDEN: {
          setUserLogin(false)
          setUserRole(resCode)
          wx.reLaunch({
            url: `/packages-user/pages/applyFail/applyFail`
          })
        }

        default:
          break
      }

      return { executed, code: resCode }
    }
  },

  'doCheckBanding': {
    subscription: async (event = {}) => {
      console.info('BREAK_IN doCheckBanding event >>', event)
      const { params } = event

      interaction.showLoading('加载中...')
      const { success, data, msg, code } = await getCheckBing(params)
      interaction.hideLoading()
      console.log('BREAK_IN doCheckBanding getCheckBing success, data, msg, code >>', success, data, msg, code)
      if (!success) {
        console.info('BREAK_IN doCheckBanding getCheckBing >>', msg)
        log && log.error({
          page: 'BREAK_IN',
          function: 'doCheckBanding',
          error: msg
        })
        return { isBanded: false }
      }
      interaction.showLoading('加载中...')
      const { executed, code: cCode } = await breakIn({ name: 'doCheckLogin', params })
      interaction.hideLoading()
      console.info('BREAK_IN doCheckBanding executed,cCode >>', executed, cCode)

      return { isBanded: true, code: cCode }
    }
  },

  'doRouteTargetPage': {
    subscription: async (event = {}) => {
      console.info('BREAK_IN doRouteTargetPage event >>', event)
      const sysInfo = getSystemInfo()
      const { type = '', registerInfo = {} } = event
      const {
        toTargetPathParams = {},
        toTargetPathUrl = '',
        shouldLogin = '',
        shouldInvite = '',
        fromPage = '',
        rolePage = '',
        inviterName = '',
        holdType = ''
      } = registerInfo

      console.info('BREAK_IN doRouteTargetPage sysInfo >>', sysInfo)
      const openAgencySuccessPath = sysInfo?.agency?.clientParams?.registerConfig?.showSuccessPage
      const openChannelSuccessPath = sysInfo?.staff?.clientParams?.registerConfig?.showSuccessPage

      let tpParams = {}
      let tpPath = doUrlDeCode(toTargetPathUrl)
      if (toTargetPathParams && !isEmptyObject(toTargetPathParams)) {
        for (const item of Object.entries(toTargetPathParams)) {
          const [key, value] = item
          tpParams[key] = doUrlDeCode(value + '')
        }
      }

      const rTimer = setTimeout(() => {
        clearTimeout(rTimer)

        const reRouteParams = {
          ...tpParams,
          token: getToken(),
          unionid: getUnionID(),
          openid: getOpenId(),
          wechatInfoId: getWechatInfoId()
        }
        let { perfix = '', value = '', id = '', targetPath = '', targetRole = '' } = tpParams
        let _filterParams = {}
        for (const item of Object.entries(panDClone(reRouteParams))) {
          const [key, value] = item
          if (!FILTER_ROUTE_KEYS.includes(key) && !ERROR_STATUS.includes(value)) {
            _filterParams[key] = value
          }
        }

        const inlineTimer = setTimeout(() => {
          clearTimeout(inlineTimer)

          _filterParams['pageFlag'] = "REGISTER"
          _filterParams['customerType'] = type
          if (!_filterParams?.token) {
            _filterParams['token'] = getToken()
          }
          if (`${targetPath}`.includes('topic')) {
            _filterParams['routerStatus'] = 'SHARE'
          }
          if (_filterParams?.TAG && _filterParams.TAG === 'AppAdvLive') {
            _filterParams['value'] = id
          }

          log && log?.info({
            page: 'BREAK_IN',
            function: 'doRouteTargetPage',
            holdType: holdType
          })
          if (holdType === 'RESET_SITE') {
            const { targetRole = 'CHANNEL' } = getRouterInRegister() || {}
            log && log?.info({
              page: 'BREAK_IN',
              function: 'doRouteTargetPage',
              targetRole: targetRole
            })
            return shapeShift(targetRole)
          }

          log && log?.info({
            page: 'BREAK_IN',
            function: 'doRouteTargetPage',
            shouldLogin: shouldLogin,
            shouldInvite: shouldInvite
          })
          // 登录注册
          if ((shouldLogin && shouldLogin == 2) || (shouldInvite && shouldInvite == 2)) {
            log && log?.info({
              page: 'BREAK_IN',
              function: 'doRouteTargetPage',
              shortLink: getApp().globalData.shortLink,
            })
            if (getApp().globalData.shortLink) {
              return breakIn({ name: 'doReloadPageInfo', type: ROLE_TYPE[type] })
            }

            if (rolePage && !`${rolePage}`.toLocaleLowerCase().includes('topic')) {
              if (rolePage === type) { //跳转首页
                return breakIn({ name: 'doReloadPageInfo', type: ROLE_TYPE[rolePage] })
              } else { // 选择身份
                const roleParams = {
                  pageType: 'CHANGE',
                  currRole: ROLE_TYPE[type],
                  registerType: REGISTER_TYPE.CHANGE,
                  regFrom: 'START_UP',
                  singlePage: true
                }

                return wx.reLaunch({
                  url: `/packages-user/pages/role/index?${qs.stringify(roleParams)}`,
                })
              }
            }

            if (type && (type === 'CHANNEL' && openChannelSuccessPath) || (type === 'AGENCY' && openAgencySuccessPath)) {
              return breakIn({ name: 'doReloadPageWelcome' })
            }

            const _regRes = getRouterInRegister()
            let _targetRes = {}
            let _fromTab = ''
            let _targetPath = ''
            let _customerType = type
            let _reSetSite = false

            if (!isEmptyObject(_regRes)) {
              let {
                customerType = '',
                targetPath: _tPath = '',
                perfix: _perfix = '',
                fromTab = '',
                routerPage = '',
                reSetSite = ''
              } = _regRes || {}
              perfix = _perfix
              targetPath = _tPath
              _fromTab = fromTab
              _targetPath = routerPage
              _customerType = customerType
              _reSetSite = reSetSite

              _targetRes = panDClone(_regRes)
              delete _targetRes.targetPath
              delete _targetRes.perfix
            }
            if (!targetPath) {
              targetPath = '/pages/common/webview/webPage'
            }
            if (!_filterParams.hasOwnProperty('value') || !_filterParams?.value) {
              _filterParams['value'] = value
            }
            if (`${perfix}`.includes('?')) {
              perfix = `${perfix}`.replace('?', '&')
            }

            // 注册成功后 判断站点信息
            if (_reSetSite) {
              // 是目标站点
              log && log?.info({
                page: 'BREAK_IN',
                function: 'doRouteTargetPage',
                type: type,
                targetRole: _regRes?.targetRole
              })
              if (type == _regRes?.targetRole) {
                console.log('BREAK_IN doRouteTargetPage reRoutePage _regRes >>>>>', _regRes)
                if (_regRes?.type === LoginState.START_UP && _regRes?.way === 'reSetSite') {
                  setUserRole(0)
                  setUserLogin(true)
                  return shapeShift(type, false, false, _regRes?.path)
                }

                return wx.reLaunch({
                  url: `/${GLOBAL_START_PATH}`,
                  complete: () => {
                    setUserRole(0)
                    setUserLogin(true)
                    setRouterInRegister({})
                    breakIn({ name: 'resetTabInfo' })
                    getApp().globalData.emitter.emit(REFRESH_PAGE_DATA, { isRefresh: true, registerRouter: true })
                  }
                })
              } else {
                //不是目标站点
                console.log('BREAK_IN doRouteTargetPage reRoutePage _targetRes, _filterParams >>>>>', _targetRes, _filterParams)
                const roleInfo = {
                  ..._targetRes,
                  path: _targetRes?.path || `/${GLOBAL_START_PATH}`,
                  way: 'reSetSite',
                  holdType: 'RESET_SITE',
                  targetRole: _regRes?.targetRole,
                  customerType: type
                }
                return wx.reLaunch({
                  url: `/pages/user/holdUp/index?${qs.stringify(roleInfo)}`,
                  complete: () => {
                    setStartUpRoutePageType({})
                  }
                })
              }
            }

            // 专题页面
            if (_fromTab && `${_fromTab}`.toLocaleUpperCase() === 'TOPIC') {
              let rpt = {}
              for (let [rKey, rValue] of Object.entries(_regRes)) {
                if (!FILTER_QR_KEY.includes(rKey)) {
                  rpt[rKey] = rValue
                }
              }
              if (!_targetPath) {
                _targetPath = '/pages/common/topic/topic'
              }

              /**
               * 身份不同
               * 到切换身份页
               */
              if (_customerType !== type) {
                console.log('BREAK_IN doRouteTargetPage reRoutePage type,_filterParams,_targetRes >>>>>', type, _filterParams, _targetRes)
                const roleInfo = {
                  ..._targetRes,
                  targetRole: _customerType,
                  customerType: type
                }

                return wx.reLaunch({
                  url: `/pages/user/holdUp/index?${qs.stringify(roleInfo)}`,
                  complete: () => {
                    setStartUpRoutePageType({})
                  }
                })
              }

              _targetPath = repairLaunchPath(_targetPath)
              return wx.reLaunch({
                url: `${_targetPath}?${qs.stringify(rpt)}`,
                complete: () => {
                  setUserRole(0)
                  setUserLogin(true)
                  setRouterInRegister({})
                  breakIn({ name: 'resetTabInfo' })
                  getApp().globalData.emitter.emit(REFRESH_PAGE_DATA, { isRefresh: true, registerRouter: true })
                }
              })
            }

            targetPath = repairLaunchPath(targetPath)
            return wx.reLaunch({
              url: `${targetPath}?url=${perfix}&${qs.stringify(_targetRes)}&${qs.stringify(_filterParams)}`,
              complete: () => {
                setUserRole(0)
                setUserLogin(true)
                setRouterInRegister({})
                breakIn({ name: 'resetTabInfo' })
                getApp().globalData.emitter.emit(REFRESH_PAGE_DATA, { isRefresh: true, registerRouter: true })
              }
            })
          }

          // 跳转三重好礼页
          if (type && (type === 'CHANNEL' && openChannelSuccessPath) || (type === 'AGENCY' && openAgencySuccessPath)) {
            return breakIn({ name: 'doReloadPageWelcome' })
          }

          log && log?.info({
            page: 'BREAK_IN',
            function: 'doRouteTargetPage',
            filterParams: _filterParams,
          })
          // 跳转详情
          if (`${targetPath}`.includes('webview') || `${tpPath}`.includes('webview')) {
            return wx.reLaunch({
              url: `/pages/common/webview/webPage?url=${perfix}&value=${value}&${qs.stringify(_filterParams)}`,
              complete: () => {
                setUserRole(0)
                setUserLogin(true)
                setRouterInRegister({})
                breakIn({ name: 'resetTabInfo' })
                getApp().globalData.emitter.emit(REFRESH_PAGE_DATA, { isRefresh: true, registerRouter: true })
              }
            })
          }

          // 专题页
          if (`${targetPath}`.includes('topic')) {
            let { customerType = '' } = _filterParams || {}
            console.log('BREAK_IN doRouteTargetPage reRoutePage _currType,customerType >>>>>', type, customerType, type === customerType)

            log && log?.info({
              page: 'BREAK_IN',
              function: 'doRouteTargetPage',
              customerType: customerType,
              type: type
            })
            targetPath = repairLaunchPath(targetPath)
            _filterParams['path'] = targetPath || '/pages/common/topic/topic'
            if (type === customerType || type === targetRole) {
              if (targetRole) {
                _filterParams['customerType'] = targetRole;
                customerType = targetRole;
              }

              targetPath = repairLaunchPath(targetPath)
              return wx.reLaunch({
                url: `${targetPath}?${qs.stringify(_filterParams)}`,
                complete: () => {
                  setUserRole(0)
                  setUserLogin(true)
                  setCurrRoleType(`${customerType}`)
                  setCustomerTypeInt(ROLE_TYPE[type])
                  setRouterInRegister({})
                  breakIn({ name: 'resetTabInfo' })
                  getApp().globalData.emitter.emit(REFRESH_PAGE_DATA, { isRefresh: true, registerRouter: true })
                }
              })
            } else {
              const roleInfo = {
                ..._filterParams,
                targetRole: customerType,
                customerType: type
              }

              return wx.reLaunch({
                url: `/pages/user/holdUp/index?${qs.stringify(roleInfo)}`,
                complete: () => {
                  setStartUpRoutePageType({})
                }
              })
            }
          }

          // 跳转到定投罗盘
          // 跳转到活动
          if (`${targetPath}`.includes('compass') || `${targetPath}`.includes('activity') || `${targetPath}`.includes('introduce')) {
            targetPath = repairLaunchPath(targetPath)
            return wx.reLaunch({
              url: `${targetPath}?${qs.stringify(_filterParams)}`,
              complete: () => {
                setUserRole(0)
                setUserLogin(true)
                setRouterInRegister({})
                breakIn({ name: 'resetTabInfo' })
                getApp().globalData.emitter.emit(REFRESH_PAGE_DATA, { isRefresh: true, registerRouter: true })
              }
            })
          }

          // 邀请注册
          if (`${tpPath}`.includes('home') || inviterName) {
            return breakIn({ name: 'doReloadPageInfo', type: ROLE_TYPE[type] })
          }

          // 抽奖
          if (fromPage && fromPage === 'LOTTERY') {
            const h5Params = {
              pageType: 'advLotteryActivityPage'
            }
            return wx.reLaunch({
              url: `/pages/common/h5/index?${qs.stringify(h5Params)}`
            })
          }

          // 跳转站点对应tab页
          if (_filterParams?.type === `${LoginState.START_UP}` && _filterParams?.way === 'reSetSite') {
            return shapeShift(_filterParams?.targetRole, false, false, _filterParams?.path)
          }
        }, 10)
      }, 10)
    }
  },

  'doReloadPageInfo': {
    subscription: (event = {}) => {
      const { type = '' } = event
      console.log('BREAK_IN doReloadPageInfo type >>>', type)
      log && log?.info({
        page: 'BREAK_IN',
        function: 'doReloadPageInfo',
        type: type,
      })
      setCustomerTypeInt(type)
      return shapeShift(ROLE_TYPE_IMAGE[type])
    }
  },

  'doReloadPageWelcome': {
    subscription: () => {
      const sParams = {
        routerPage: `/pages/common/introduce/index`,
        shareFrom: EnterSource.WELCOME,
        routerStatus: 'SHARE',
        fromTab: 'HOME',
      }
      let path = `${GLOBAL_START_PATH}?${qs.stringify(sParams)}`

      path = repairLaunchPath(path)
      return wx.reLaunch({
        url: `${path}`
      })
    }
  },

  // 获取渠道信息
  'doGetChannelInfos': {
    subscription: (event = {}) => {
      console.log('BREAK_IN doGetChannelInfos event >>', event)
      log && log?.info({
        page: 'BREAK_IN',
        function: 'doGetChannelInfos',
        event: event,
      })
      const { doSave = '' } = event

      const sysInfo = getSystemInfo()
      const customerType = getCurrRoleType()
      const wInfo = getUser()
      let channelImgUrl = getSFV(global.STORAGE_GLOBAL_CURR_CHANNEL_IMG_URL) || ''

      let hasChannel = false
      let defaultCoverUrl = ''
      if (sysInfo && !isEmptyObject(sysInfo)) {
        let channelName = sysInfo?.staff?.clientParams?.startUpConfig?.channelName || []
        let channelStartPic = sysInfo?.staff?.clientParams?.startUpConfig?.channelStartPic || []

        if (wInfo && !isEmptyObject(wInfo)) {
          const { companyName = '' } = wInfo
          if (channelName && channelName.length && channelStartPic && channelStartPic.length) {
            channelName.forEach((cName, cIndex) => {
              if (cName == companyName) {
                hasChannel = true
                channelImgUrl = `${channelStartPic[cIndex]}`.includes('x-oss-process') ? channelStartPic[cIndex] : channelStartPic[cIndex] + '?x-oss-process=image/resize,m_fill,h_1624,w_750'
              }
            })
            defaultCoverUrl = channelImgUrl
          }
        }
      }

      switch (customerType) {
        case 'CUSTOMER': // 普客
          channelImgUrl = sysInfo?.common?.startUpImg ? sysInfo.common.startUpImg + '?x-oss-process=image/resize,m_fill,h_1624,w_750' : defaultCoverUrl
          if (channelImgUrl) {
            hasChannel = true
          }
          break

        case 'AGENCY': //机构
          channelImgUrl = sysInfo?.agency?.startUpImg ? sysInfo.agency.startUpImg + '?x-oss-process=image/resize,m_fill,h_1624,w_750' : defaultCoverUrl
          if (channelImgUrl) {
            hasChannel = true
          }
          break

        default:
          break
      }

      console.log('BREAK_IN doSave,customerType,hasChannel,channelImgUrl >>', doSave, customerType, hasChannel, channelImgUrl)
      log && log?.info({
        page: 'BREAK_IN',
        function: 'doGetChannelInfos',
        doSave: doSave,
        customerType: customerType,
        hasChannel: hasChannel,
        channelImgUrl: channelImgUrl,
      })
      if (doSave) {
        setSFV(global.STORAGE_GLOBAL_CURR_CHANNEL_IMG_URL, channelImgUrl)
        return
      }

      return {
        hasChannel,
        channelImgUrl,
      }
    }
  },

  // 过滤
  'doFilterUrlKey': {
    subscription: (event = {}) => {
      const { url = '' } = event
      let [service, params] = `${url}`.split('?')
      params = qs.parse(params)
      let _params = {}
      for (let [pKey, pValue] of Object.entries(params)) {
        if (!ERROR_STATUS.includes(pValue)) {
          if (Array.isArray(pValue)) {
            _params[pKey] = pValue[0]
          } else {
            _params[pKey] = pValue
          }
        }
      }

      let resUrl = `${service}?${qs.stringify(_params)}`
      console.info(`BREAK_IN resUrl:${resUrl}`)
      log && log?.info({
        page: 'BREAK_IN',
        function: 'doFilterUrlKey',
        resUrl: resUrl,
      })
      return resUrl
    }
  },

  // 配置文件处理
  'doInstallCardInfo': {
    subscription: async (event = {}) => {
      const { elementName = '', data = {}, state = {} } = event
      const {
        cardName = '',
        aggRecCardIds = '',
        finalConf = {}
      } = data

      const {
        cardId,
        TabCur = 0,
        showViolations = false
      } = state
      let cardInfo = {}
      if (typeof finalConf === 'string') {
        cardInfo = JSON.parse(finalConf)
      }
      if (typeof finalConf === 'object') {
        cardInfo = Object.assign(cardInfo, finalConf)
      }

      let list = []
      const {
        dataSource = {},
        interaction = {},
        subTitle = {},
        title = {},
        template = '',
        titleAreaVisible = 1,
        area = 1,
        imageUrl = ''
      } = cardInfo || {}

      let _cardName = title?.text || ''
      if (!isEmptyObject(dataSource)) {
        const { data = [] } = dataSource || {}
        list = [].concat(data)
      }

      let contentInfo = []
      if (list && list.length <= 1 && elementName === 'CONTENT') {
        list = await breakIn({ name: 'doUpDateSubTagToTab', list })
      }

      let filterFnName
      switch (elementName) {
        case 'CONTENT':
          filterFnName = 'doFilterHideCategoryV1'
          break
        case 'SINGLE_CONTENT':
          filterFnName = 'doFilterHideCategoryV2'
          break
        default:
          filterFnName = 'doFilterHideCategoryV1'
          break
      }

      contentInfo = await breakIn({ name: `${filterFnName}`, param: list, state: { showViolations } })
      const initNextIdxs = []
      if (contentInfo && contentInfo.length && elementName === 'CONTENT') {
        contentInfo.forEach((cItem) => {
          const { name = '', list } = cItem || {}
          const props = {
            name,
            idx: list && list.length ? 0 : -1 // -1为没有二级分类
          }
          initNextIdxs.push(props)
        })
      }

      let currList = []
      if (initNextIdxs[TabCur]?.idx !== -1 && elementName === 'CONTENT') {
        const fList = contentInfo[TabCur]?.list || []
        currList = fList[0]?.dataList || []
        currList = currList.map((dItem, dIndex) => {
          return {
            ...dItem,
            isLast: dIndex == currList.length - 1,
            signalBlock: !(contentInfo && contentInfo.length > 1) && !(initNextIdxs[TabCur]?.idx !== -1 && contentInfo[TabCur]?.list?.length > 1),
            cName: _cardName,
            cId: cardId,
            mName: initNextIdxs[TabCur]?.name || ''
          }
        })
      }

      if (contentInfo && contentInfo.length && elementName === 'SINGLE_CONTENT') {
        currList = contentInfo[TabCur].dataList || []

        currList = currList.map((item) => {
          return {
            ...item,
            cName: _cardName,
            cId: cardId,
            mName: contentInfo[TabCur]?.name || ''
          }
        })
      }

      const titleInfo = {
        interaction,
        subTitle,
        title,
        template,
        cardType: cardName,
        titleAreaVisible,
        cardSurround: CARD_SURROUND[area * 1],
        hasIcon: imageUrl,
        contentHasScrollTab: contentInfo && contentInfo.length > 1,
        contentHasSubNav: initNextIdxs[TabCur]?.idx !== -1 && contentInfo[TabCur]?.list?.length > 1
      }

      return {
        aggRecCardIds,
        cardName,
        list,
        cardInfo,
        contentInfo,
        titleInfo,
        initNextIdxs,
        currList,
        signalCard: titleAreaVisible == 2
      }
    }
  },

  'doFilterHideCategoryV1': {
    subscription: async (event = {}) => {
      const { param = [], state = {} } = event
      const { showViolations = true } = state

      let dataSource = []
      let isSameType = false
      let currRealm = ''
      if (param && param instanceof Array) {
        const { category: cOrigin = [] } = param[0] || {}
        const { realm = '' } = cOrigin[0] || {}
        let fRealm = currRealm = `${realm}`

        isSameType = param.every((pItem) => {
          const { category = [] } = pItem || {}
          const { realm: _realm = '' } = category[0] || {}
          return (category.length === 1 && fRealm === _realm)
        })
      }

      if (isSameType) {
        let sKey = []
        let sNoNeedFilter = false
        let sNoNeedFilterTemp = []

        param.forEach((cItem) => {
          const { category } = cItem || {}
          if (category && category.length === 1) {
            const { realm = '', id = '' } = category[0] || {}
            sKey.push({
              key: CHANGE_REALM[realm],
              value: id
            })
          }
        })

        if (currRealm === 'live') {
          let _sFilterList = []
          sNoNeedFilterTemp = [].concat(param)
          sNoNeedFilterTemp.forEach((_fItem) => {
            const { category } = _fItem || {}
            const { id = '', realm: mRealm = '', dataList = [] } = category[0] || {}
            let props = {
              ...category[0],
              dataList: dataList.map((mItem, mIndex) => {
                return {
                  ...mItem,
                  realm: mRealm,
                  index: mIndex,
                  isLast: mIndex === dataList.length - 1,
                  categoryId: id
                }
              })
            }
            _sFilterList.push(props)
          })
          sNoNeedFilterTemp = [].concat(_sFilterList)
          param.forEach((_pItem, _pIndex) => {
            const { category, name } = _pItem || {}
            let _list = {
              ...category[0],
              ...sNoNeedFilterTemp[_pIndex]
            }
            const props = {
              name,
              list: [].concat(_list)
            }
            dataSource.push(props)
          })
          return dataSource
        }

        let { data, success, msg = '' } = await getContentCategoryFilter({ entity: sKey })
        if (!success) {
          return wx.showToast({
            title: msg || '',
          })
        }

        let sShowListIds = []
        if (data && data.length) {
          data.forEach((sItem) => {
            const { status = '', id = '' } = sItem || {}
            // 过滤显示的一级分类
            if (status == '1' || status === 'PUBLISHED') {
              sShowListIds.push(id)
            }
          })
        }

        let sFilerList = []
        if (sShowListIds.length) {
          param.forEach((fItem) => {
            const { category } = fItem || {}
            const { id = '', realm: mRealm = '', dataList = [] } = category[0] || {}
            if (sShowListIds.includes(id + '')) {
              let props = {
                ...category[0],
                dataList: dataList.map((mItem, mIndex) => {
                  return {
                    ...mItem,
                    realm: mRealm,
                    index: mIndex,
                    isLast: mIndex === dataList.length - 1,
                    categoryId: id
                  }
                })
              }
              if (showViolations && reviewFilter.includes(mRealm)) {
                props.dataList = []
              }
              sFilerList.push(props)
            }
          })
        }

        if (sNoNeedFilter) {
          let _sFilterList = []
          sFilerList = [].concat(sNoNeedFilterTemp)
          sFilerList.forEach((_fItem) => {
            const { category } = _fItem || {}
            const { id = '', realm: mRealm = '', dataList = [] } = category[0] || {}
            let props = {
              ...category[0],
              dataList: dataList.map((mItem, mIndex) => {
                return {
                  ...mItem,
                  realm: mRealm,
                  index: mIndex,
                  isLast: mIndex === dataList.length - 1,
                  categoryId: id
                }
              })
            }
            _sFilterList.push(props)
          })
          sFilerList = _sFilterList
        }

        param.forEach((_pItem, _pIndex) => {
          const { category, name } = _pItem || {}
          let _list = {
            ...category[0],
            ...sFilerList[_pIndex]
          }
          const props = {
            name,
            list: [].concat(_list)
          }
          dataSource.push(props)
        })

        return dataSource
      }

      for (const item of param) {
        let key = []
        let noNeedFilter = false
        let noNeedFilterTemp = []
        let { category = [], name = '' } = item || {}
        if (category && category.length) {
          category.forEach((cItem) => {
            const { realm = '', id = '' } = cItem || {}
            key.push({
              key: CHANGE_REALM[realm],
              value: id
            })
          })
        }
        if (!category) {
          category = []
        }

        if (category && category.length) {
          if (category[0].realm === 'live') {
            noNeedFilter = true
            noNeedFilterTemp = [].concat(category)
          }
        }

        let { data, success, msg = '' } = await getContentCategoryFilter({ entity: key })
        if (!success) {
          return wx.showToast({
            title: msg || '',
          })
        }

        let showListIds = []
        let _category = []
        if (data && data.length) {
          data.forEach((sItem, sIndex) => {
            const { status = '', id = '', name = '' } = sItem || {}
            let cParams = { ...category[sIndex] }
            // 过滤显示的一级分类
            if (status == '1' || status === 'PUBLISHED') {
              showListIds.push(id)
              cParams = {
                ...cParams,
                secondName: name
              }
            }
            _category.push(cParams)
          })
        }
        category = _category

        let filerList = []
        if (showListIds.length) {
          category.forEach((fItem) => {
            const { id = '', realm: mRealm = '', dataList = [] } = fItem || {}
            if (showListIds.includes(id + '')) {
              let props = {
                ...fItem,
                dataList: dataList.map((mItem, mIndex) => {
                  return {
                    ...mItem,
                    realm: mRealm,
                    index: mIndex,
                    isLast: mIndex === dataList.length - 1,
                    categoryId: id
                  }
                })
              }
              if (showViolations && reviewFilter.includes(mRealm)) {
                props.dataList = []
              }
              filerList.push(props)
            }
          })
        }

        if (noNeedFilter) {
          let _filterList = []
          filerList = [].concat(noNeedFilterTemp)
          filerList.forEach((_fItem) => {
            const { id = '', realm: mRealm = '', dataList = [] } = _fItem || {}
            let props = {
              ..._fItem,
              dataList: dataList.map((mItem, mIndex) => {
                return {
                  ...mItem,
                  realm: mRealm,
                  index: mIndex,
                  isLast: mIndex === dataList.length - 1,
                  categoryId: id
                }
              })
            }
            _filterList.push(props)
          })
          filerList = _filterList
        }
        const props = {
          name,
          list: filerList
        }
        dataSource.push(props)
      }

      return dataSource
    }
  },

  'doFilterHideCategoryV2': {
    subscription: (event = {}) => {
      const { param = [], state = {} } = event
      const { showViolations } = state

      let contentRes = []
      if (param && param instanceof Array) {
        for (const item of param) {
          let { name = '', content: dataList = [] } = item || {}
          if (showViolations) {
            dataList = dataList.filter((fItem) => {
              const { realm = '' } = fItem || {}
              return !(reviewFilter.includes(`${realm}`))
            })
          }

          let _dataList = []
          if (dataList && dataList.length) {
            dataList.forEach((dItem, dIndex) => {
              const props = {
                ...dItem,
                signalBlock: true,
                index: dIndex,
                isLast: dIndex === dataList.length - 1
              }

              _dataList.push(props)
            })
          }

          const filterProps = {
            name,
            dataList: _dataList || dataList
          }
          contentRes.push(filterProps)
        }
      }

      return contentRes
    },
  },

  'doUpDateSubTagToTab': {
    subscription: (event = {}) => {
      const { list = [] } = event
      let resList = []
      const { category = [] } = list[0] || {}

      let mCategory = Array.from(category)
      if (mCategory && mCategory.length) {
        for (const item of mCategory) {
          const { secondName = '' } = item || {}
          const upTag = {
            name: secondName,
            category: [].concat(item)
          }
          resList.push(upTag)
        }
      }
      return resList
    }
  },

  // 刷新token
  'doRefreshToken': {
    subscription: async () => {
      const { success, data, msg, code } = await getFindWechatInfo({})
      console.info('BREAK_IN doRefreshToken success, msg, code >>>', success, msg, code)
      log && log?.info({
        page: 'BREAK_IN',
        function: 'doRefreshToken',
        success: success,
        msg: msg,
        code: code,
        data: data,
      })

      if (success) {
        const { id: wechatInfoId = '' } = data || {}
        if (`${wechatInfoId}`) {
          storage.setStorage('wechatInfoId', wechatInfoId)
        }
      }
    }
  },

  // 清除缓存文件
  'doClearSaveFiles': {
    subscription: () => {
      const FM = wx.getFileSystemManager()
      FM.stat({
        path: `${wx.env.USER_DATA_PATH}/`,
        recursive: true,
        success: (res) => {
          console.info('BREAK_IN doClearSaveFiles res >>>', res)
          log && log?.info({
            page: 'BREAK_IN',
            function: 'doClearSaveFiles',
            path: `${wx.env.USER_DATA_PATH}/`,
            res: res,
          })
          const stats = res?.stats

          for (const item of stats) {
            let filePath = item.path || ''
            console.log('BREAK_IN doClearSaveFiles FM.unlink filePath >>>', filePath)
            FILE_TYPES.forEach((fType) => {
              if (filePath.indexOf(`${fType}`) !== -1) {
                if (!`${filePath}`.startsWith('/')) {
                  filePath = `/${filePath}`
                }
                FM.unlink({
                  filePath: `${wx.env.USER_DATA_PATH + filePath}`,
                  complete(res) {
                    console.log('BREAK_IN doClearSaveFiles FM.unlink complete res >>>', res)
                  }
                })
              }
            })
          }
        },
        complete: () => {
          getApp().globalData.filePath = ''
        }
      })
    }
  },

  'doSetSiteBack': {
    subscription: () => {
      return wx.reLaunch({
        url: `/${GLOBAL_START_PATH}`,
        complete: () => {
          getApp().globalData.refersMark = true
          getApp().event.emit(BREAK_IN_INIT_SUCCESS)
        }
      })
    }
  },

  'doCheckAttention': {
    subscription: async () => {
      const customerType = getCurrRoleType()
      const dRecord = getAttentionDays()
      const cPage = getCurrentPages().pop()?.route || ''
      console.log('BREAK_IN doCheckAttention dRecord,customerType,cPage >>', dRecord, customerType, cPage)
      log && log?.info({
        page: 'BREAK_IN',
        function: 'doCheckAttention',
        dRecord: dRecord,
        customerType: customerType,
        cPage: cPage,
      })
      if (!TAB_BAR_PATH.includes(cPage) || customerType !== 'CHANNEL') {
        return
      }

      if (!dRecord) {
        const { success, code, data = false, msg = '' } = await getAttentionStatus({ unionid: getUnionID() })
        console.log('BREAK_IN doCheckAttention success,code,data,msg >>>', success, code, data, msg)

        if (!success) {
          interaction.showToast(msg || "")
          return setAttentionDays()
        }

        if (success && data) {
          console.log('BREAK_IN doCheckAttention success && data >>>', success && data)
          return setAttentionDays()
        }

        if (success && !data) {
          getApp().event.emit(TAB_LISTENER_EVENT, { key: SET_SHOW_RECORD })
          return setAttentionDays()
        }
      }
      return setAttentionDays()
    }
  },

  'getTabCardInfo': {
    subscription: async (param = {}) => {
      const { params = {} } = param
      const { pageName = '' } = params
      const tabPageCardInfo = (getApp().globalData.tabList).length ? getApp().globalData.tabList : getTabBarList() || []
      let cardInfos = []

      let targetPageCard = []
      if (tabPageCardInfo && tabPageCardInfo.length) {
        targetPageCard = tabPageCardInfo.find((tItem) => {
          const {
            pageTabBarConf: {
              tabName = ''
            }
          } = tItem || {}

          return pageName === tabName
        })
      }
      let id = '', pageId = ''
      if (!targetPageCard && !targetPageCard.length) {
        wx.showLoading({ title: '加载中...' })
        const { data, success } = await getPageTemplateInfo({ customerType: getCurrRoleType() })
        console.log('breakIn getTabCardInfo success,data >>>', success, data)
        wx.hideLoading()
        if (success) {
          if (data && Array.isArray(data)) {
            const tPage = data.find((tItem) => {
              const {
                pageTabBarConf: {
                  tabName = ''
                }
              } = tItem || {}

              return pageName === tabName
            })

            const { id: pId = '', pageId: _pId = '' } = tPage || {}
            id = pId
            pageId = _pId
          } else {
            return cardInfos
          }
        }
      } else {
        const { id: pId = '', pageId: _pId = '' } = targetPageCard || {}
        id = pId
        pageId = _pId
      }

      let _pageId = id || pageId
      if (!`${_pageId}`) {
        return cardInfos
      }

      wx.showLoading({ title: '加载中...' })
      const { data, msg, success } = await getTemplateById({ admin: false, pageId: _pageId })
      console.log('breakIn getTabCardInfo success,data >>>', success, data)
      wx.hideLoading()
      if (!success) {
        wx.showToast({
          title: msg || '',
          icon: 'none'
        })
        return
      }

      let { cardInfos: cInfos = [] } = data || {}
      if (!cInfos || !cInfos.length) {
        cInfos = []
      }

      cInfos.forEach((cItem, cIndex) => {
        const props = {
          ...cItem,
          cardIndex: cIndex,
          cardLast: cIndex === cInfos.length - 1,
          conf: '{}',
          finalConf: doJSON_PARSE(cItem?.finalConf),
          fromTab: pageName
        }
        cardInfos.push(props)
      })

      console.log('getTabCardInfo cardInfos >>>', cardInfos)
      return cardInfos
    }
  },

  'getFloatShareInfo': {
    subscription: (param = {}) => {
      const { shareParams = {} } = param
      const { floatShare = {} } = shareParams

      const sysInfo = getSystemInfo()
      const customerType = getCurrRoleType()
      let title = sysInfo?.enterprise?.abbreviation || '广发木棉花'
      let imageUrl = sysInfo[CUSTOMER_RESOLVE[customerType]]?.shareUserImg || SHARE_IMG_DEFAULT

      const {
        listPath = '',
        params = '',
        fromTab = ''
      } = floatShare || {}

      const sParams = {
        routerPage: `${listPath}`,
        shareFrom: EnterSource.LIST,
        params,
        fromTab,
        floatShare: true,
        customerType
      }
      let path = `${GLOBAL_START_PATH}?${qs.stringify(sParams)}`
      const shareData = {
        title,
        path,
        imageUrl
      }

      console.log('breakIn getFloatShareInfo shareData >>>> ', shareData)
      return shareData
    }
  },

  'getPageShareInfo': {
    subscription: (param = {}) => {
      const { params = {} } = param
      const { pathInfo = {} } = params

      const sysInfo = getSystemInfo()
      const customerType = getCurrRoleType()
      let title = sysInfo?.enterprise?.abbreviation || '广发木棉花'
      let imageUrl = sysInfo[CUSTOMER_RESOLVE[customerType]]?.shareUserImg || SHARE_IMG_DEFAULT


      const tabList = (getApp()?.globalData?.tabList)?.length ? getApp()?.globalData?.tabList : getTabBarList() || []
      const {
        fromTab = '',
        pageType,
        banShare = ''
      } = pathInfo || {}

      if (DISABLE_SHARE_PAGE.includes(pageType) || banShare) {
        let _path = `${GLOBAL_START_PATH}?pageType=${pageType}&banShare=${banShare}`
        return {
          title,
          path: _path,
          imageUrl
        }
      }
      const tpInfo = tabList.find(item => item.tabName == fromTab)
      let shouldLogin = false

      if (customerType) {
        if (LOGIN_SITE.includes(`${customerType}`)) {
          shouldLogin = true
        }
      }

      console.log('breakIn getPageShareInfo tpInfo >>>', tpInfo)
      const { tabPath = '' } = tpInfo || {}
      const sParams = {
        routerPage: `${tabPath}`,
        shareFrom: EnterSource.HOME,
        customerType,
        shouldLogin
      }

      let path = `${GLOBAL_START_PATH}?${qs.stringify(sParams)}`
      const shareData = {
        title,
        path,
        imageUrl
      }
      console.log('breakIn getPageShareInfo shareData >>>> ', shareData)
      return shareData
    }
  },

  'getCluesInfo': {
    subscription: (param = {}) => {
      const { params = {} } = param
      const tabList = (getApp()?.globalData?.tabList)?.length ? getApp()?.globalData?.tabList : getTabBarList() || []

      const {
        fromTab = '',
        cId = '',
        cName = '',
        mName = ''
      } = params || {}

      let _pageName = ''
      let _pageId = ''
      let _clues = [].concat(MARKET_CLUE_DEF) || []
      let _clueRes = []

      tabList.forEach((item) => {
        const { id = '', pageName = '', pageTabBarConf = {} } = item || {}
        const { tabName = '' } = pageTabBarConf || {}

        if (fromTab == tabName) {
          _pageName = pageName
          _pageId = id
        }
      })

      _clues.forEach((cItem) => {
        const { type = '' } = cItem || {}
        if (type == 'page') {
          cItem.name = _pageName
          cItem.id = _pageId
        }

        if (type == 'card') {
          cItem.name = cName
          cItem.id = cId
        }

        if (type == 'module') {
          cItem.name = mName
        }

        _clueRes.push(cItem)
      })

      return _clueRes || _clues
    }
  },

  'getCheckAppUpdate': {
    subscription: (param = {}) => {
      console.log('breakIn getCheckAppUpdate param >>>', param)
      let _oldVersion = '1.0.0'
      const _oldVersionInfo = getVersionInfo()
      if (_oldVersionInfo && !isEmptyObject(_oldVersionInfo)) {
        const { version: _oVersion = '' } = _oldVersionInfo
        _oldVersion = _oVersion
      }

      const {
        appId = '',
        envVersion = '',
        version = ''
      } = wx.getAccountInfoSync().miniProgram || {}

      if (REVIEW_STATUS.includes(envVersion) && version !== _oldVersion) { // 开发模式
        return breakIn({ name: 'setUpDateApp' })
      }

      if (envVersion === 'release' && version !== _oldVersion) {
        let vTime = dayJs(new Date()).format('YYYY-MM-DD HH:mm')
        saveVersionInfo({ appId, envVersion, version: version || _oldVersion + '.1', vTime })
        return breakIn({ name: 'setUpDateApp' })
      }
    }
  },

  'setUpDateApp': {
    subscription: async (param = {}) => {
      console.log('breakIn setUpDateApp param >>>', param)
      if (wx.canIUse("getUpdateManager")) {
        const updateManager = wx.getUpdateManager();
        updateManager.onCheckForUpdate(function (res) {
          if (res.hasUpdate) {
            // 请求完新版本信息的回调
            updateManager.onUpdateReady(function () {
              wx.showModal({
                title: "更新提示",
                content: "新版本已经准备好，是否重启应用，更新版本？",
                showCancel: false,
                success: function (res) {
                  if (res.confirm) {
                    // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
                    updateManager.applyUpdate();
                  }
                }
              });
            });
            updateManager.onUpdateFailed(function () {
              wx.showModal({
                // 新的版本下载失败
                title: "已经有新版本",
                content: "新版本已经上线啦~，请您删除当前小程序，重新搜索打开~"
              });
            });
          }
        });
      } else {
        wx.showModal({
          title: "溫馨提示",
          content:
            "当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。"
        });
      }
    }
  },

  'saveShortLink': {
    subscription: (param = {}) => {
      const { params = {} } = param
      const { shortLink = '' } = params
      console.log('BREAK_IN saveShortLink shortLink >>>', shortLink)
      setShortLink(shortLink)
      getApp().globalData.shortLink = shortLink
    }
  },

  // 'setReportUrlLink': {
  //   subscription: async (param = {}) => {
  //     let sysInfo = getSystemInfo()
  //     let linkPrams = sysInfo?.staff?.clientParams?.urlLink || {}

  //     await wx.cloud.callFunction({
  //       name: 'getLink',
  //       data: {
  //         param: {
  //           ...linkPrams
  //         }
  //       },
  //     }).then(res => {
  //       const {result = {}} = res || {}
  //       const {errMsg = '', result: urlLink = ''} = result || {}
  //       console.log('breakIn setReportUrlLink urlLink >>>', urlLink)

  //       log && log?.info({
  //         page: 'BREAK_IN',
  //         function: 'setReportUrlLink',
  //         urlLink: urlLink,
  //       })
  //     }).catch(err => {
  //       console.log('breakIn getLink err >>>', err)
  //     })

  //     return Promise.resolve('success')
  //   }
  // },

  'doSetMiniCoverUrl': {
    subscription: (param = {}) => {
      const { params = {} } = param

      console.log('breakIn doSetMiniCoverUrl params >>>', params)
      const {
        hasChannel = '',
        channelImgUrl = '',
      } = params || {}

      setStartUpHasCover(!!hasChannel)
      setStartUpCover(channelImgUrl)
      store?.setState({
        hasChannelCover: !!hasChannel,
        channelCoverUrl: channelImgUrl
      })
    }
  },

  'uploadImage': {
    subscription: async (event = {}) => {
      const { file = '', role } = event
      let imgType = `${file}`.split(`.`).pop()
      let formData = new FormData();
      formData.append("bizCode", "marketing");
      formData.append('features.publicRead', true);
      formData.append("features.reUpload", false);
      formData.appendFile("file", file, `${+new Date()}.${imgType}`);
      let fData = formData.getData();

      interaction.showLoading('上传中...')
      await wx.request({
        url: `https://aim-share-dev.gffunds.com.cn/file-system/api/v1/fs/upload?timeStemp=${+new Date()}`,
        filePath: file,
        name: "file",
        method: 'POST',
        header: {
          'content-type': fData.contentType,
          'serviceChannel': 'PRI',
          'type': 'marketingApi',
          token: getToken(),
        },
        data: fData.buffer,
        success: (res) => {
          vLog.log('res >>', res)
          const { data } = res || {}
          const {
            data: {
              cdnUrl = ''
            },
          } = data || {}

          if (role === 'CHANNEL') {
            setTempChannelAvatar(cdnUrl)
          } else {
            setTempAgencyAvatar(cdnUrl)
          }
        },
        fail: (err) => {
          console.error('err >>', err)
          return err
        },
        complete: (res) => {
          interaction.hideLoading()
          log && log?.info({
            page: 'BREAK_IN',
            function: 'uploadImage',
            res: res,
          })
        }
      })
    }
  }
}

export default function breakIn(params) {
  console.log('BREAK_IN params >>>', params)
  const { name = '' } = params || {}
  return breakContent[`${name}`].subscription(params)
}

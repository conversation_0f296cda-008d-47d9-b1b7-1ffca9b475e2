import {
  getOpenId,
  getUnionID,
  setCurrRoleType,
  setRouterInRegister,
} from "../utils/userStorage";

import { convert, global, interaction } from "../index";
import { checkLogin } from "../nb/home";

const log = wx.getRealtimeLogManager ? wx.getRealtimeLogManager() : null

const ROLE_TYPE = {
  CHANNEL: 0,
  AGENCY: 2,
  CUSTOMER: 3
}

export default async function shapeShift(type = 'CUSTOMER', hasDone = false, isRefers = false, switchTab = '') {
  console.info('SHAPE_SHIFT type,hasDone,isRefers,switchTab >>>>', type, hasDone, isRefers, switchTab)
  log && log?.info({
    page: 'SHAPE_SHIFT',
    type,
    hasDone,
    isRefers,
    switchTab
  })
  console.log('==================================== SHAPE_SHIFT START ====================================')
  getApp().globalData.customerType = type
  setCurrRoleType(type)

  interaction.showLoading('加载中...')
  let params = {
    openid: getOpenId(),
    unionid: getUnionID(),
    customerType: ROLE_TYPE[type]
  }
  console.log('SHAPE_SHIFT checkLogin >>>>', params)
  const { code: wxcode } = await wx.login()
  await checkLogin({ code: wxcode, customerType: params.customerType, wechatCode: global.SOURCE_CODE })
    .then((res) => {
      console.log('SHAPE_SHIFT checkLogin res >>>', res)
    }).catch((err) => {
      console.error('SHAPE_SHIFT checkLogin err >>>', err)
      log && log?.error({
        page: 'SHAPE_SHIFT',
        function: 'checkLogin',
        err: JSON.stringify(err)
      })
    })

  if (hasDone) {
    console.log('==================================== SHAPE_SHIFT END hasDone ====================================')
    interaction.showLoading('加载中...')
    await convert({ name: 'tabBar', initType: 'START_UP', currRoleType: type })
    interaction.hideLoading()
    return { executed: 'DONE' }
  }

  if (switchTab) {
    console.log('==================================== SHAPE_SHIFT END switchTab ====================================')
    interaction.showLoading('加载中...')
    await convert({ name: 'tabBar', initType: 'START_UP', currRoleType: type })
    return wx.switchTab({
      url: `${switchTab}`,
      complete: () => {
        setRouterInRegister({})
        interaction.hideLoading()
      }
    })
  }

  console.log('==================================== SHAPE_SHIFT END ====================================')
  interaction.showLoading('加载中...')
  await convert({ name: 'tabBar', initType: 'START_UP', currRoleType: type })
  interaction.hideLoading()
  if (isRefers) {
    getApp().globalData.refersMark = true
  }
  return wx.reLaunch({
    url: '/pages/loginAndRegist/startUp/index',
    complete: () => {
      setRouterInRegister({})
      interaction.hideLoading()
    }
  })
}

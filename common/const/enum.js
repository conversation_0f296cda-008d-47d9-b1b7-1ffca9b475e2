const TabBarPath = {
  HOME: '/pages/home/<USER>',
  PRODUCT: '/pages/product/index',
  ACCOMPANY: '/pages/accompany/index',
  VIEWPOINT: '/pages/viewPoint/index',
  MINE: '/pages/mine/mine'
}

const APP_ID = {
  DEV: 'wx3501ac822988bb64', // 测试环境
  PRO: 'wxf9bfd0f8fd40a6d8', // 生产环境
}

const NORMAL_REGISTER_ORG_ID = {
  DEV: '3125928033251073', // 银行-工商银行-北京分行-北京空港支行
  // NEW_DEV: '3363609225056063', // 普通渠道-nb测试
  NEW_DEV: '3363823453333632', // 普通渠道-gf测试
  PRO: '3273585334722892', // 普通渠道
}

/**
 * @type {{
 * AGENCY: 机构用户,
 * CHANNEL: 渠道用户,
 * CUSTOMER: 普通用户}}
 */
const ROLE = {
  AGENCY: 'AGENCY',
  CHANNEL: 'CHANNEL',
  CUSTOMER: 'CUSTOMER',
}

/**
 * 新用户注册审核模式
 */
const REGISTER_MODEL = {
  5: 'CHANNEL_MANAGER', //渠道经理验证
  3: 'WHITE_LIST', //自动审核（白名单）
  1: 'REVIEW_BY_USER', // 手动审核
  2: 'STOP_REVIEW', //停用
}
/**
 * 注册审核模式对应的提示语
 */
const LOSE_TYPE_TOAST_MSG = {
  0: '请完善信息',
  1: '请输入姓名',
  2: '请输入职位',
  3: '请选择渠道',
  4: '请补全信息'
}
/**
 * 注册审核模式KEY
 */
const REGISTER_MODEL_LIST = [1, 2, 3, 5]

/**
 * 注册提交参数过滤KEY
 */
const PARAMS_EXCHANGE_FILTER_KEYS = [
  'openid',
  'unionid',
  'wechatInfoId',
  'phone',
  'code',
  'blackBox',
  'captchaToken',
  'name',
  'avatar'
]

const ROLE_REGISTER = {
  CHANNEL: 'channel',
  AGENCY: 'agency',
  CUSTOMER: 'customer',
}

const CUSTOMER_RESOLVE = {
  'CHANNEL': 'staff',
  'CUSTOMER': 'common',
  'AGENCY': 'agency'
}

const LOGIN_SITE = [
  'CHANNEL',
  'AGENCY',
  'CUSTOMER'
]

const LOGIN_SITE_STR = [
  '渠道客户门户',
  '普通客户门户',
  '机构客户门户',
  '普通客户门户',
  '其他'
]

const ROLE_TYPE = {
  CHANNEL: 0,
  AGENCY: 2,
  CUSTOMER: 3,
  STAFF: 99
}

const ROLE_TYPE_IMAGE = {
  0: 'CHANNEL',
  2: 'AGENCY',
  3: 'CUSTOMER'
}

const INVITER_INFO = '广发基金小管家'

// 当前为
const ROLE_CURRENT = [
  {
    type: ROLE.AGENCY,
    mark: ROLE_TYPE.AGENCY,
    icon: 'https://aim-pic-dev.gffunds.com.cn/image/course/2023-02-22/bf01384d-8518-402a-b26f-30b12359df2f.png',
    tips: '当前身份为机构客户',
  },
  {
    type: ROLE.CHANNEL,
    mark: ROLE_TYPE.CHANNEL,
    icon: 'https://aim-pic-dev.gffunds.com.cn/image/course/2023-02-22/4f185081-a934-45e3-9d33-41f1919ae762.png',
    tips: '当前身份为理财经理',
  },
  {
    type: ROLE.CUSTOMER,
    mark: ROLE_TYPE.CUSTOMER,
    icon: 'https://aim-pic-dev.gffunds.com.cn/image/course/2023-02-22/66e0dfbe-311c-480b-af49-15849f2e59f3.png',
    tips: '当前身份为普通客户',
  }
]

// 选择为
const ROLE_LIST_CHOOSE = [
  {
    type: ROLE.CHANNEL,
    truth: ROLE_TYPE.CHANNEL,
    mark: ROLE_TYPE.CHANNEL,
    icon: 'https://fs-file.newbanker.cn/image/course/2023-03-09/d0605d4e-d50b-4b04-ae7d-4c6c48f786cc.png',
  },
  {
    type: ROLE.AGENCY,
    truth: ROLE_TYPE.AGENCY,
    mark: ROLE_TYPE.AGENCY,
    icon: 'https://fs-file.newbanker.cn/image/course/2023-03-09/791dfa11-2f16-4aab-82d0-243f10acdc69.png',
  },
  {
    type: ROLE.CUSTOMER,
    truth: ROLE_TYPE.CUSTOMER,
    mark: ROLE_TYPE.CUSTOMER,
    icon: 'https://fs-file.newbanker.cn/image/course/2023-03-09/f1d9a57e-94f0-40b6-94b2-e76430c04854.png',
  }
]

// 切换为  注册用户不能切换到普客站点
const ROLE_LIST_CHANGE = [
  {
    type: ROLE.CHANNEL,
    truth: ROLE_TYPE.CHANNEL,
    mark: ROLE_TYPE.CHANNEL,
    icon: 'https://fs-file.newbanker.cn/image/course/2023-03-09/31edc2f4-4851-4bbd-a760-65cb3ae1c088.png',
  },
  {
    type: ROLE.AGENCY,
    truth: ROLE_TYPE.AGENCY,
    mark: ROLE_TYPE.AGENCY,
    icon: 'https://fs-file.newbanker.cn/image/course/2023-03-09/f69a358e-1e15-4199-a8b8-3d106ed93693.png',
  },
  // {
  //   type: ROLE.CUSTOMER,
  //   truth: ROLE_TYPE.CUSTOMER,
  //   mark: ROLE_TYPE.CUSTOMER,
  //   icon: 'https://fs-file.newbanker.cn/image/course/2023-03-09/e4e4ec05-0ab0-43ea-b430-efa423bece5f.png',
  // }
]

const ROLE_VISITOR = [
  {
    type: ROLE.CHANNEL,
    mark: ROLE_TYPE.CHANNEL,
    icon: 'https://aim-pic-dev.gffunds.com.cn/image/course/2023-02-22/4f185081-a934-45e3-9d33-41f1919ae762.png',
    tips: '当前身份为理财经理',
    checkRole: '机构用户',
    targetRole: ROLE_TYPE.AGENCY,
    targetRoleType: ROLE.AGENCY
  },
  {
    type: ROLE.AGENCY,
    mark: ROLE_TYPE.AGENCY,
    icon: 'https://aim-pic-dev.gffunds.com.cn/image/course/2023-02-22/bf01384d-8518-402a-b26f-30b12359df2f.png',
    tips: '当前身份为机构客户',
    checkRole: '理财经理',
    targetRole: ROLE_TYPE.CHANNEL,
    targetRoleType: ROLE.CHANNEL
  },
  {
    type: ROLE.CHANNEL,
    mark: ROLE_TYPE.CHANNEL,
    icon: 'https://aim-pic-dev.gffunds.com.cn/image/course/2023-02-22/4f185081-a934-45e3-9d33-41f1919ae762.png',
    tips: '当前身份为理财经理',
    checkRole: '普通用户',
    targetRole: ROLE_TYPE.CUSTOMER,
    targetRoleType: ROLE.CUSTOMER
  },
  {
    type: ROLE.AGENCY,
    mark: ROLE_TYPE.AGENCY,
    icon: 'https://aim-pic-dev.gffunds.com.cn/image/course/2023-02-22/bf01384d-8518-402a-b26f-30b12359df2f.png',
    tips: '当前身份为机构客户',
    checkRole: '普通用户',
    targetRole: ROLE_TYPE.CUSTOMER,
    targetRoleType: ROLE.CUSTOMER
  },
  {
    type: ROLE.CUSTOMER,
    mark: ROLE_TYPE.CUSTOMER,
    icon: 'https://aim-pic-dev.gffunds.com.cn/image/course/2023-02-22/66e0dfbe-311c-480b-af49-15849f2e59f3.png',
    tips: '当前身份为普通用户',
    checkRole: '理财经理',
    targetRole: ROLE_TYPE.CHANNEL,
    targetRoleType: ROLE.CHANNEL
  },
  {
    type: ROLE.CUSTOMER,
    mark: ROLE_TYPE.CUSTOMER,
    icon: 'https://aim-pic-dev.gffunds.com.cn/image/course/2023-02-22/66e0dfbe-311c-480b-af49-15849f2e59f3.png',
    tips: '当前身份为普通用户',
    checkRole: '机构用户',
    targetRole: ROLE_TYPE.AGENCY,
    targetRoleType: ROLE.AGENCY
  }
]

const CUSTOMER_CHANGE = {
  type: ROLE.CUSTOMER,
  mark: ROLE_TYPE.CUSTOMER,
  icon: 'https://aim-pic-dev.gffunds.com.cn/image/course/2023-02-22/66e0dfbe-311c-480b-af49-15849f2e59f3.png',
  tips: '当前身份为普通客户',
}

const REGISTER_TYPE = {
  CHANGE: "CHANGE",
  REGISTER: 'REGISTER'
}

/**
 * TEMP_CONTENT 单内容详情
 * TEMP_SINGLE_LIST 单列表
 * TEMP_UNION_LIST 聚合列表
 * TEMP_OTHERS 其他小程序
 */
const FROM_CHANNEL = {
  CONTENT: 'TEMP_CONTENT',
  SINGLE: 'TEMP_SINGLE_LIST',
  UNION: 'TEMP_UNION_LIST',
  OTHERS: 'TEMP_OTHERS',
  OLD_TEMP: 'TEMP_MSG',
}

const DISABLE_SHARE_PAGE = [
  'AppAdvProfileInfo',
  'AppAdvMarketingClue',
]

const DISPLAY_403_PAGE = 'https://aim-pic-dev.gffunds.com.cn/image/course/2023-01-28/2de384dc-9ee6-40f4-89e8-e5e0c669b561.png'
const SHARE_IMG_DEFAULT = 'https://aim-pic-dev.gffunds.com.cn/image/course/2022-06-29/36b56b67-212b-4bf5-b026-ce3822f13173.jpeg?x-oss-process=image/format,jpg/resize,m_lfit,w_500,limit_0/crop,h_400,y_200/interlace,1'
const AVATAR_URL_DEFAULT = 'https://aim-pic-dev.gffunds.com.cn/image/course/2022-11-23/4710f76b-a6ad-4a7e-963d-ef4a1b3e3004.jpeg'
const MARK_DAY_COVER = 'https://aim-pic-dev.gffunds.com.cn/image/course/2022-12-01/e744aed1-294d-462e-9767-b6ed1063f4a2.jpeg'

const SHARE_BLOCK_DEFAULT = {
  title: '广发木棉花',
  imageUrl: SHARE_IMG_DEFAULT,
  path: '/pages/loginAndRegist/startUp/index'
}

const DEV_PHONE = [
  '15354872767',
]

const REVIEW_PHONE = [
  '18513390120X',
  '18513390123X'
]

const CHANNEL_ID = 'SGAAAAUYXIRFNAAA' // 生产
// const CHANNEL_ID = 'VAAAAAAAVAAKBAAA' // 测试

/**
 * 标题显示区域  area   1 卡片内   2 卡片外
 * 卡片标题区   titleAreaVisible   1  展示   2  隐藏
 * @type {{"1": boolean, "2": boolean}}
 */
const CARD_SURROUND = {
  1: true,
  2: false
}

const FILE_TYPES = [".doc", ".xls", ".ppt", ".pdf", ".docx", ".xlsx", ".pptx"]

/**
 * CONTENT filter type
 */
const CHANGE_REALM = {
  news: 'ARTICLE',
  marketPlan: 'MARKET_PLAN',
  multimedia: 'MULTI_COURSE',
  live: 'LIVE',
  product: 'FUND',
  manger: 'FUND_MANAGER'
}

/**
 * 渠道列表
 */
const CHANNEL_REALM = {
  marketPlan: 'MARKET_PLAN',
  market_channel: 'MARKET_PLAN',
  market_content: 'MARKET_PLAN',
  picture_channel: "POSTER",
  poster_channel: 'POSTER',
  information_channel: 'ARTICLE',
  media_channel: 'MULTI_COURSE',
  live_channel: 'LIVE',
}

const PAGE_INFO_REALM = {
  news: 'AppAdvNewsDetail',
  marketPlan: 'advProductDataDetail',
  multimedia: 'AppAdvInvestorTrainCom',
  live: 'AppAdvLive',
  poster: 'AppAdvPosterInfo',
  unscramble: 'advFundScram',
  stock: 'advFundScram',
  // report: 'advFundScram',
}

const SHARE_RESPONDENT_REALM = {
  AppAdvNewsDetail: 'ARTICLE',
  AppAdvInvestorTrainCom: 'MULTI_COURSE',
  advProductDataDetail: 'MARKETING_PLAN',
  AppAdvLive: 'LIVE'
}

/**
 * 跳转页面属性
 * @type {{INTER_PAGE: number}}
 */
const PAGE_LINK_TYPE = {
  INTER_PAGE: 1, // 列表
  PRODUCT_PAGE: 2, // 产品详情、内容详情
  WEB_PAGE: 3, // 地址链接（action/http）
  POLYMERS_LIST: 4, //聚合列表
}

/**
 * 冷启动
 * 小程序场景值命中以下值时，可展示关注公众号组件：
 1011 扫描二维码
 1017 前往小程序体验版的入口页
 1025 扫描一维码
 1047 扫描小程序码
 1124 扫“一物一码”打开小程序
 */
const WECHAT_COLD_START = [
  '1011',
  '1017',
  '1025',
  '1047',
  '1124'
]

/**
 * 直播状态
 * @type {{"1": string, "2": string, "3": string, "4": string}}
 */
const LIVE_STATUS = {
  1: '#00B659',
  2: '#F24D28',
  3: '#ccc',
  4: '#FF9800'
}

//进入小程序来源
const EnterSource = {
  HOME: '1', //首页
  LIST: '2', //列表
  WEBVIEW: '3', //详情
  COMPASS: '4', //定投罗盘
  ACTIVITY: '5', //活动
  POSTER: '6', //海报
  INVITE: '7', //邀请注册
  WELCOME: '8', //活动中转页面
  H5: '9',
  TOPIC: '10' //专题页
}

//名片夹理财师类型
const RealmType = {
  ARTICLE: "ARTICLE", //资讯
  LIVE: "LIVE", //直播
  POSTER: "POSTER", //海报
  MULTI_COURSE: "MULTI_COURSE", //音视频
  MARKET_PLAN: "MARKET_PLAN", //营销方案
  UNSCRAMBLE: "UNSCRAMBLE", //基金解读
  STOCK: "STOCK", //股票
  REPORT: "REPORT", //运作报告
}

//登录状态
const LoginState = {
  SUCCESS: 0, //通过
  WHITE_LIST_PASS: 30001,   //白名单未通过
  INREVIEW: 30003,   //账号审核中，请稍后登录
  OVERDUE: 30004,   //验证码已过期
  ABSENCE: 30035,   //用户不存在，或未完善微信授权,转至登录/注册页
  ACTIVATED: 200,   //已激活
  FORBIDDEN: 30037,   //禁用
  NOTACTIVATED: 30036, //账号未激活
  REGISTER: 30002,   //注册
  HASBINDUSER: 30005, //该微信已绑定过用户
  HASBINDWECHAT: 30006, //已绑定过微信，是否绑定当前微信
  CODEERROR: 30004, //验证码错误
  VERIFY_ERROR: 300030, //验证不通过
  CODE_ERROR_01: 30031, //失败码01
  CODE_ERROR_02: 30032, //失败码01
  CODE_ERROR_03: 30033, //失败码01
  MULTIPLE_CUSTOMER: 30047, //关联多个用户
  ROLE_IS_VISITOR: 30048, //游客状态
  INVESTOR_INVALID: 30044, //邀请人信息有误
  AGENCY_INVALID: 30043,//机构信息设置有误
  AGENCY_UN_OPEN_REGISTER: 30041, //未开放公开注册
  FLOAT_SHARE: 600001, // 浮窗列表分享
  INVITE: 600002, // 邀请注册
  QR_INVITE: 600003, // 邀请注册小程序码
  LIST_POSTER: 600004, // 列表小程序码
  CHECK_LOGIN: 600005, // 校验登录
  QR_DETAIL: 600006, // 各类详情小程序码
  START_UP: 600007, // 小程序码跳首页
  JUMP_TARGET: 600008, // 跳转目标页
  JUMP_ACTIVITY: 600009, // 跳转目标页
  JUMP_OTHERS: 600010, // 跳转其他小程序
  WELCOME: 600011, // 跳转目标页
  H5: 600012, // 跳转目标页
  TOPIC: 600013, // 跳转专题页
}

const ERROR_STATUS = [null, undefined, 'null', 'undefined', '', '[object Object]']

/**
 * 消息推送参数过滤
 * @type {string[]}
 */
const FILTER_KEYS_TEMP = ['pageFlag', 'url']

/**
 * 注册参数过滤
 * @type {string[]}
 */
const FILTER_KEYS_REGISTER = ['TAG', 'fromTab', 'code', 'phone', 'url', 'blackBox', 'captchaToken']

/**
 * 列表分享过滤
 */
const FILTER_KEYS_SHARE_LIST = [...FILTER_KEYS_REGISTER, 'perfix', 'targetPath', 'trunk', 'fromChannel', 'WEBVIEW']

/**
 * 基础参数过滤
 */
const FILTER_KEYS_BASE = ['token', 'openid', 'unionid', 'wechatInfoId']

/**
 * 员工登录状态
 */
const EMPLOYEE_LOGIN_STATUS = {
  EMPLOYEE_NOT_EXIST: 30026, // 此员工不存在
  EMPLOYEE_HAS_DEACTIVATED: 30038 // 员工已停用
}

/**
 * 游客模式
 * @type {number[]}
 */
const LOGIN_VISITOR = [
  30035,
  30036,
  30002,
  30005,
  30006,
]

/**
 * 搜索&半屏小程序打开基本机构
 */
const SEARCH_CHANNEL_EDF = {
  'marketPlan': {
    label: '营销资料',
    type: 'MARKET_PLAN',
    shortLink: ''
  },
  'fundProductGf': {
    label: '基金解读',
    type: 'UNSCRAMBLE',
    shortLink: ''
  },
  'stockGf': {
    label: '股票',
    type: 'STOCK',
    shortLink: ''
  },
  'reportGf': {
    label: '运作报告',
    type: 'REPORT',
    shortLink: ''
  },
  'live': {
    label: '在线直播',
    type: 'LIVE',
    shortLink: ''
  },
  'article': {
    label: '资讯文章',
    type: 'ARTICLE',
    shortLink: ''
  },
  'multiCourse': {
    label: '音视频课程',
    type: 'MULTI_COURSE',
    shortLink: ''
  }
}

/**
 * 上报线索基本机构
 */
const MARKET_CLUE_DEF = [
  {
    type: "page",
    name: "",
    id: "",
    level: 1
  },
  {
    type: "card",
    name: "",
    id: "",
    level: 2
  },
  {
    type: "module",
    name: "",
    id: "",
    level: 3
  },
]

const TAB_BAR_PATH = [
  'pages/home/<USER>',
  'pages/product/index',
  'pages/accompany/index',
  'pages/viewPoint/index',
  'pages/mine/mine',
]

const LIST_PAGE_PATH = [
  'pages/home/<USER>/list',
  'pages/home/<USER>/list',
]

const LIST_PAGE_SEARCH = [
  'packages-common/pages/common/search/search'
]

const LIST_SHARE_PATH = [
  'pages/common/list/index'
]

const COUNTER_PAGE = [
  'wechatFixedInvestmentCalculator',
  'wechatProfitProbabilityCalculator'
]

const reportEventName = {
  'BI_wx_login': 'break_in_wx_login',
  'BI_init_unionid': 'break_in_init_unionid',
  'BI_get_staff_auth_record': 'break_in_get_staff_auth_record',
  'BI_get_page_template_info': 'break_in_get_page_template_info',
  'BI_get_template_by_id': 'break_in_get_template_by_id',
  'BI_check_login': 'break_in_check_login',
  'app_get_page_template_info': 'app_get_page_template_info',
  'app_get_template_by_id': 'app_get_template_by_id',
  'convert_get_page_template_info': 'convert_get_page_template_info',
  'convert_get_template_by_id': 'convert_get_template_by_id',
  'start_up_android_reload': 'start_up_android_reload',
  'start_up_get_scene_param': 'start_up_get_scene_param',
  'start_up_init_config': 'start_up_init_config',
  'login_init_blackbox': 'login_init_blackbox',
  'init_unionid': 'init_unionid',
  'login_wx_login': 'login_wx_login',
  'login_get_uc_system_api_config': 'login_get_uc_system_api_config',
  'login_get_user_profile': 'login_get_user_profile',
  'login_rebinding': 'login_rebinding',
  'login_get_adv_staff_card_info': 'login_get_adv_staff_card_info',
  'login_params': 'login_params',
  'login_res': 'login_res',
  'login_verify_fail': 'login_verify_fail',
  'login_send_sms_code': 'login_send_sms_code',
  'register_get_all_display_node': 'register_get_all_display_node',
  'register_get_uc_system_api_config': 'register_get_uc_system_api_config',
  'register_init_blackbox': 'register_init_blackbox',
  'register_verify_fail': 'register_verify_fail',
  'register_save_place_by_point': 'register_save_place_by_point',
  'register_exchange_params': 'register_exchange_params',
  'register_do_submit_params': 'register_do_submit_params',
  'register_res': 'register_res',
  'register_reroute_target_page_params': 'register_reroute_target_page_params',
  'register_get_query_org_node': 'register_get_query_org_node',
  'register_get_user_profile': 'register_get_user_profile',
  'register_get_unionid': 'register_get_unionid',
  'register_wx_login': 'register_wx_login',
  'register_find_last_node': 'register_find_last_node',
  'agency_register_init_blackbox': 'agency_register_init_blackbox',
  'agency_register_verify_fail': 'agency_register_verify_fail',
  'agency_register_exchange_params': 'agency_register_exchange_params',
  'agency_register_do_submit_params': 'agency_register_do_submit_params',
  'agency_register_res': 'agency_register_res',
  'agency_register_get_user_profile': 'agency_register_get_user_profile',
  'agency_register_get_unionid': 'agency_register_get_unionid',
  'agency_register_wx_login': 'agency_register_wx_login',
  'agency_register_reroute_target_page_params': 'agency_register_reroute_target_page_params',
  'agency_register_get_agency_list': 'agency_register_get_agency_list',
  'normal_register_init_blackbox': 'normal_register_init_blackbox',
  'normal_register_verify_fail': 'normal_register_verify_fail',
  'normal_register_do_submit_params': 'normal_register_do_submit_params',
  'normal_register_res': 'normal_register_res',
  'normal_register_get_user_profile': 'normal_register_get_user_profile',
  'normal_register_get_unionid': 'normal_register_get_unionid',
  'normal_register_wx_login': 'normal_register_wx_login',
  'normal_register_reroute_target_page_params': 'normal_register_reroute_target_page_params',
  'role_register_role_params': 'role_register_role_params',
  'role_choose_role_check_login': 'role_choose_role_check_login',
  'hold_up_check_login': 'hold_up_check_login',
  'hold_up_check_login_res': 'hold_up_check_login_res',
  'union_list_get_unionid': 'union_list_get_unionid',
  'union_list_wx_login': 'union_list_wx_login',
  'topic_do_refresh_info': 'topic_do_refresh_info',
  'webview_init_res_info': 'webview_init_res_info',
  'webview_init_final_url': 'webview_init_final_url',
  'webview_to_mini_program': 'webview_to_mini_program',
  'webview_post_message_event_org': 'webview_post_message_event_org',
  'webview_post_message_data': 'webview_post_message_data',
  'webview_share_info': 'webview_share_info',
  'webview_load_done_time': 'webview_load_done_time',
  'webview_load_error_url': 'webview_load_error_url',
  'utils_open_file': 'utils_open_file'
}

const GLOBAL_START_PATH = 'pages/loginAndRegist/startUp/index'

const OSS_BASE = '?x-oss-process=image'

/**
 * APP_START_WAY_IDS
 1001  发现栏小程序主入口，「最近使用」列表（基础库2.2.4版本起包含「我的小程序」列表）
 1003  星标小程序列表
 1005  微信首页顶部搜索框的搜索结果页
 1006   发现栏小程序主入口搜索框的搜索结果页
 1017  前往小程序体验版的入口页
 1023  系统桌面小图标打开小程序
 1026  发现栏小程序主入口，「附近的小程序」列表
 1035  公众号自定义菜单
 1080  客服会话菜单小程序入口，打开小程序
 1187  新版浮窗，微信8.0起
 1089  聊天主界面下拉，打开小程序/微信聊天主界面下拉，「最近使用」栏（基础库2.2.4版本起包含「我的小程序」栏）
 1102  公众号 profile 页服务预览
 1103  发现-小程序主入口我的小程序，打开小程序
 1104  聊天主界面下拉，从我的小程序，打开小程序
 1106  聊天主界面下拉，从顶部搜索结果页，打开小程序
 1113  安卓手机负一屏，打开小程序
 1114  安卓手机侧边栏，打开小程序
 1117  后台运行小程序的管理页中，打开小程序
 */
const APP_START_WAY_IDS = [
  '1001',
  '1003',
  '1005',
  '1006',
  '1017',
  '1023',
  '1026',
  '1035',
  '1080',
  '1187',
  '1089',
  '1090',
  '1102',
  '1103',
  '1104',
  '1106',
  '1113',
  '1114',
  '1117'
]

const VERSION_BLOCK = {
  cardIndex: 99,
  cardLast: true,
  cardName: "Version",
  cardType: "VERSION",
  hasLogin: true,
  fromTab: 'MINE',
  finalConf: '{}',
  ordinal: 99
}

export {
  EnterSource,
  RealmType,
  LoginState,
  LOGIN_VISITOR,
  EMPLOYEE_LOGIN_STATUS,
  LIVE_STATUS,
  ERROR_STATUS,

  TabBarPath,
  FILTER_KEYS_REGISTER,
  FILTER_KEYS_BASE,
  FILTER_KEYS_SHARE_LIST,
  FILTER_KEYS_TEMP,

  GLOBAL_START_PATH,
  DEV_PHONE,
  APP_ID,
  APP_START_WAY_IDS,
  REVIEW_PHONE,
  CARD_SURROUND,
  PAGE_INFO_REALM,
  PAGE_LINK_TYPE,

  CHANNEL_REALM,
  CHANNEL_ID,
  CHANGE_REALM,

  SHARE_RESPONDENT_REALM,
  SHARE_IMG_DEFAULT,
  SHARE_BLOCK_DEFAULT,
  SEARCH_CHANNEL_EDF,
  FROM_CHANNEL,

  MARKET_CLUE_DEF,

  TAB_BAR_PATH,
  LIST_PAGE_PATH,
  LIST_SHARE_PATH,
  LIST_PAGE_SEARCH,

  ROLE,
  ROLE_TYPE,
  ROLE_TYPE_IMAGE,
  ROLE_CURRENT,
  ROLE_LIST_CHANGE,
  ROLE_LIST_CHOOSE,
  ROLE_VISITOR,
  ROLE_REGISTER,
  LOGIN_SITE,
  LOGIN_SITE_STR,
  CUSTOMER_RESOLVE,
  CUSTOMER_CHANGE,
  REGISTER_MODEL,
  LOSE_TYPE_TOAST_MSG,
  PARAMS_EXCHANGE_FILTER_KEYS,
  REGISTER_MODEL_LIST,
  INVITER_INFO,

  AVATAR_URL_DEFAULT,
  DISPLAY_403_PAGE,

  WECHAT_COLD_START,
  COUNTER_PAGE,
  VERSION_BLOCK,
  REGISTER_TYPE,
  NORMAL_REGISTER_ORG_ID,

  MARK_DAY_COVER,
  FILE_TYPES,
  OSS_BASE,
  reportEventName,
  DISABLE_SHARE_PAGE
}

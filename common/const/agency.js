const tabBarAgency = {
  "backgroundColor": "#FFFFFF",
  "color": "#CCCCCC",
  "selectedColor": '#f04b28ff',
  "list": [
    {
      "id": 3153342490745732,
      "name": '首页',
      "ordinal": 0,
      "pageId": 3153342490745732,
      "pageName": "首页",
      "pageShow": true,
      "cardIds": "3349408662203207,3349408662203203,3349408662203205",
      "icon": {
        "iconNormal": "https://aim-pic.gffunds.com.cn/image/course/2022-04-01/d1c06b8a-6994-45e6-bb6a-378cb64dedcd.png",
        "iconSelector": "https://aim-pic.gffunds.com.cn/image/course/2022-04-01/3d7874d6-c80f-4e98-9b68-b1ced8c37b1b.png"
      },
      "pageTabBarConf": {
        "name": "首页",
        "tabName": "HOME",
        "type": 0,
      },
      "type": 0,
      "tabName": "HOME",
      "path": "/pages/home/<USER>",
      "tabPath": "/pages/home/<USER>",
      "status": "ENABLE"
    },
    {
      "id": 3153342490745733,
      "name": '产品',
      "ordinal": 1,
      "pageId": 3153342490745733,
      "pageName": "产品",
      "pageShow": true,
      "cardIds": "3349408662203144",
      "icon": {
        "iconNormal": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-03-23/93163a45-3f6f-4a7d-8b13-ee2db2795ce7.png",
        "iconSelector": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-03-23/95a3eb2d-5e65-416d-8b5f-6eafabe1c90c.png"
      },
      "pageTabBarConf": {
        "name": "产品",
        "tabName": "PRODUCT",
        "type": 1,
      },
      "type": 1,
      "tabName": "PRODUCT",
      "tabPath": "/pages/product/index",
      "path": "/pages/product/index",
      "status": "ENABLE"
    },
    {
      "id": 3153330889300745,
      "name": "我的",
      "ordinal": 2,
      "pageId": 3153330889300745,
      "pageName": "我的",
      "pageShow": true,
      "cardIds": "3349408662203136,3349408662203140,3349408662203142",
      "icon": {
        "iconNormal": "https://aim-pic.gffunds.com.cn/image/course/2022-04-01/6e834f2d-42d5-43bc-854e-afbeeaec2e89.png",
        "iconSelector": "https://aim-pic.gffunds.com.cn/image/course/2022-04-01/00205a40-263c-46b5-804e-b809a6711054.png"
      },
      "pageTabBarConf": {
        "name": "我的",
        "tabName": "MINE",
        "type": 4,
      },
      "type": 4,
      "tabName": "MINE",
      "path": "/pages/mine/mine",
      "tabPath": "/pages/mine/mine",
      "status": "ENABLE"
    }
  ]
}

module.exports = {
  tabBarAgency
}

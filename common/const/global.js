//Key
export const STORAGE_GLOBAL_SCREEN_CODE = 'wechat,wbs.screen.code'
export const STORAGE_GLOBAL_SCREEN_QUERY = 'wechat.wbs.screen.query'
export const STORAGE_GLOBAL_HAS_LOGIN = 'wechat.wbs.global.has.login'
export const STORAGE_GLOBAL_TAB_BAR_LIST = 'wechat.wbs.global.tab.bar.list' // tabBarList 不包含cardInfo数据
export const STORAGE_GLOBAL_TAB_BAR_CONFIG = 'wechat.wbs.global.tab.bar.config' // tabBar的tab枚举
export const STORAGE_GLOBAL_TAB_PAGE_CARD_INFO = 'wechat.wbs.global.tab.page.card.info' // tab对应cardInfo的全部数据
export const STORAGE_GLOBAL_FIRST_TAB_PATH = 'wechat.wbs.global.first.tab.path'
export const STORAGE_GLOBAL_SYSTEM_INFO = 'wechat.wbs.global.system.info'
export const STORAGE_GLOBAL_WECHAT_INFO = 'wechat.wbs.global.wechat.info'
export const STORAGE_GLOBAL_WECHAT_INFO_CACHE = 'wechat.wbs.global.wechat.info.cache'
export const STORAGE_GLOBAL_APP_HOLD = 'wechat.wbs.global.app.hold'
export const STORAGE_GLOBAL_LOADING_MOMENT = 'wechat.wbs.global.loading.moment'

export const STORAGE_GLOBAL_START_UP_HAS_CHANNEL_COVER = 'wechat.wbs.global.start.up.has.channel.cover'
export const STORAGE_GLOBAL_START_UP_CHANNEL_COVER = 'wechat.wbs.global.start.up.channel.cover'

export const STORAGE_GLOBAL_ATTENTION_STATUS = 'wechat.wbs.global.attention.status'

export const STORAGE_GLOBAL_SHORT_LINK = 'wechat.wbs.global.short.link'

// ===================================机构相关===================================== //
export const STORAGE_GLOBAL_CUSTOMER_ROLE_INT = 'wechat.wbs.global.customer.role.int'
export const STORAGE_GLOBAL_CUSTOMER_ROLE_STRING = 'wechat.wbs.global.customer.role.string'
export const STORAGE_GLOBAL_BREAK_IN_BASE_PARAMS = 'wechat.wbs.global.break.in.base.params'
export const STORAGE_GLOBAL_TEMP_CHANNEL_AVATAR = 'wechat.wbs.global.channel.temp.avatar'
export const STORAGE_GLOBAL_TEMP_AGENCY_AVATAR = 'wechat.wbs.global.agency.temp.avatar'

export const STORAGE_GLOBAL_ROUTER_FOR_REGISTER = 'wechat.wbs.global.router.for.register'
export const STORAGE_GLOBAL_SHARE_MOMENT = 'wechat.wbs.global.share.moment'
export const STORAGE_GLOBAL_HASH_POOL_LIST = 'wechat.wbs.global.hash.pool.list'

export const STORAGE_GLOBAL_RESET_SHARE_PATH_DATA = 'storage.global.reset.share.path.data'
export const STORAGE_GLOBAL_USER_ROLE = 'wechat.wbs.global.user.role'

export const STORAGE_GLOBAL_EMPLOYEE_INFO = 'wechat.wbs.global.employee.info'
export const STORAGE_GLOBAL_CURR_CHANNEL_IMG_URL = 'wechat.wbs.global.curr.channel.img.url'
export const STORAGE_GLOBAL_PREVIOUS_PATH = 'wechat.wbs.global.previous.path'
export const STORAGE_GLOBAL_PREVIOUS_PARAMS = 'wechat.wbs.global.previous.params'

export const STORAGE_GLOBAL_OPEN_FILE_START = 'wechat.wbs.global.open.file.start'
export const STORAGE_GLOBAL_REROUTE_BY_CODE = 'wechat.wbs.global.reroute.by.code'

export const STORAGE_GLOBAL_INLINE_PAGE = 'wechat.wbs.global.inline.page'
export const STORAGE_GLOBAL_CASTER_PARAMS = 'wechat.wbs.global.caster.params'
export const STORAGE_GLOBAL_SPRINGBOARD_STATUS = 'wechat.wbs.global.springboard.status'
export const STORAGE_GLOBAL_SPRINGBOARD_PARAMS = 'wechat.wbs.global.springboard.params'
export const STORAGE_GLOBAL_REPORT_STATUS = 'wechat.wbs.global.report.status'
export const STORAGE_GLOBAL_REPORT_DURATION = 'wechat.wbs.global.report.duration'
export const STORAGE_GLOBAL_REPORT_PARAMS = 'wechat.wbs.global.report.params'
export const STORAGE_GLOBAL_START_UP_ROUTE_PAGE_TYPE = 'wechat.wbs.global.start.up.route.page.type'
export const STORAGE_GLOBAL_LATITUDE = 'wechat.wbs.global.latitude'
export const STORAGE_GLOBAL_LONGITUDE = 'wechat.wbs.global.longitude'
export const STORAGE_GLOBAL_REG_AREA_INFO = 'wechat.wbs.global.register.area.info'
export const STORAGE_GLOBAL_REG_CHANNEL_NAME = 'wechat.wbs.global.register.channel.name'
export const STORAGE_GLOBAL_AUTH_LOCATION = 'wechat.wbs.global.auth.location'

export const STORAGE_GLOBAL_SHOW_VIOLATIONS_PHONE = 'wechat.wbs.global.show.violations.phone'
export const STORAGE_GLOBAL_MARK_DAY_STATUS = 'wechat.wbs.global.mark.day.status'

export const SOURCE_CODE = 'wbs_investor'
export const entryQY = 'normal'

import config from './config.js'

import newConfig from '../../config/index.js'

const {
  env,
} = newConfig

const application = {
  appKey: 'test',
  secret: '123456'
}

const openApiConfig = {
  appKey: '2088',
  secret: '123456'
}

const postV2Api = {
  passport: '/passport/api',
  sop: '/sop/api',
  customer: '/csc/api',
  pdc: '/pdc/api',
  rsc: '/rsc/api',
  lm: '/lm-web/api',
  openapi: '/openapi/api',
  wcWeb: '/wc-web/api',
  webApi: '/webapi/api',
  webres: '/wbsres/api',
  ie: '/ie-web/api',
  ac: '/ac-web/api',
  im: '/im-web/api',
  csc: '/csc/api',
  npdc_web: '/npdc-web/api',
  npdc_admin: '/npdc-admin/api',
  obj: '/obj-web/api',
  fs: '/fs/api',
  mns: '/mns/mobile/api',
  marketing: '/marketing-api/api',
  newHttp: '/newHttp/lc/api',
  file_system: '/file-system/api',
  marketing_course: '/marketing-course/mobile/api',
  uc_system: '/uc-system/mobile/api',
  uc_admin: '/uc-system/admin/api',
  uc_login: '/uc-system/mobile',
  mc: '/api/mc'
}

const path = {
  gfService: `${env.service}`,
  service: `${env.service}/http/`,
  v2Service: `${env.service}`,
  leadsApi: `${env.service}/marketing-api/`, //展业中心接口地址

  gfH5: env.gfH5Service,
  gfApi: env.service,
  liveApi: env.liveService,
  gfMini: env.gfMimiProgram,
}

function getPath() {
  if (!config.isProvider){
    return path
  }
  let providerInitail = config.extConfig.request.initial
  return {
    gfService: `${env.service}`,
    service: `${providerInitail}/wbs/http/`,
    v2Service: `${providerInitail}/wbs/`,
    leadsApi: `${providerInitail}/mc/api/mcapi/`, //展业中心接口地址

    gfH5: env.gfH5Service,
    gfApi: env.service,
    liveApi: env.liveService,
    gfMini: env.gfMimiProgram,
  }
}

const wbs = getPath()

export {
  openApiConfig,
  application,
  postV2Api,
  wbs,
}

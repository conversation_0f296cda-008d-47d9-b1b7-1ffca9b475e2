const sysConfig = {
  "agency": {
    "loginImg": "",
    "registerImg": "https://aim-pic.gffunds.com.cn/image/course/2022-11-18/c3053b98-b8e7-461a-a20d-1a28f3c6fa39.jpg",
    "newUser": "3",
    "showInv": "1",
    "userChannel": "2",
    "userChangeChannel": "1",
    "userChangeWeChat": "2",
    "serverTel": "************",
    "description": "",
    "failTips": "",
    "registerSuccessType": "1",
    "registerSuccessContent": "",
    "disableUserTipImg": "https://aim-pic.gffunds.com.cn/image/course/2022-11-25/601f5f83-f3c2-478b-94bb-4105aa0e980d.jpg",
    "registerSuccessEditor": "https://aim-pic.gffunds.com.cn/image/marketing/2022-11-25/4a1676f3-c62f-4ece-a91d-9f2d1e2f7acc.jpg",
    "whiteConfig": {"element": ["请输入广发基金机构销售经理"], "failMsg": "请联系您的机构销售经理"},
    "invitationShareImg": "https://aim-pic.gffunds.com.cn/image/course/2022-11-25/933ebfc7-6c0f-406b-85c1-1222ec9a9587.png",
    "invitationImg": "https://aim-pic.gffunds.com.cn/image/course/2022-11-25/ec9128ca-76f0-4c6a-bd5c-e8ff4fde53b4.jpeg",
    "userChangeWeChatTip": "不允许换绑微信",
    "popupImg": "https://aim-pic.gffunds.com.cn/image/course/2022-11-25/933ebfc7-6c0f-406b-85c1-1222ec9a9587.png",
    "invitationColor": "rgba(242, 77, 40, 1)"
  },
  "enterprise": {
    "name": "广发基金管理有限公司",
    "abbreviation": "木棉花",
    "copyright": "广发基金.木棉花智能营销服务平台",
    "theme": "rgba(242, 77, 40, 1)",
    "cover": "https://fs-file.newbanker.cn/image/course/2021-06-17/3460ead9-bb05-4e97-86bb-13e37f8a404b.jpg",
    "icon": "https://aim-pic.gffunds.com.cn/image/course/2022-04-01/962d1239-6b08-4339-9da0-bdaeb9b4950b.jpeg",
    "enterpriseInfo": "",
    "disclaimer": "本材料不作为宣传推介材料，仅用于渠道经理内部参考使用。基金有风险，投资需谨慎。基金的过往业绩并不预示其未来表现，基金管理人管理的其他基金过往业绩不代表基金的未来表现。投资者在投资前请认真阅读基金合同和招募说明书等法律文件，充分了解本基金详情及风险特征，选择符合自身风险承受能力、投资目标的产品，并承担基金投资中出现的各类风险。",
    "marketingParams": "[{\n\t\"label\": \"资讯文章\",\n\t\"value\": \"ARTICLE\"\n}, {\n\t\"label\": \"财经早报\",\n\t\"value\": \"HEADLINE\"\n}, {\n\t\"label\": \"智能名片\",\n\t\"value\": \"CARD\"\n}, {\n\t\"label\": \"转发助手\",\n\t\"value\": \"OWNER_CONTENT\"\n}, {\n\t\"label\": \"精品海报\",\n\t\"value\": \"POSTER\"\n}, {\n\t\"label\": \"直播内容\",\n\t\"value\": \"LIVE\"\n}, {\n\t\"label\": \"音视频课程\",\n\t\"value\": \"MULTI_COURSE\"\n}, {\n\t\"label\": \"营销资料\",\n\t\"value\": \"MARKET_PLAN\"\n}, {\n\t\"label\": \"基金产品\",\n\t\"value\": \"FUND\"\n}, {\n\t\"label\": \"基金经理\",\n\t\"value\": \"FUND_MANAGER\"\n}, {\n        \"label\": \"朋友圈助手\",\n        \"value\": \"CONTENT\"\n}]",
    "summary": "广发基金专属渠道服务号获取更多营销材料！",
    "logo": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-03-23/800265cf-9d78-4ed2-894b-bbdb41b35833.png",
    "wholeSituationParams": {"predefineColor": ["#23E820", "#DD2F2F", "#1B17E7", "#D708B8", "#08D7AE", "#DA0032", "#000000"]}
  },
  "staff": {
    "loginImg": "https://aim-pic.gffunds.com.cn/image/course/2022-11-18/9bb37b97-87d1-42a2-9dad-a0497daceb83.jpg",
    "registerImg": "https://aim-pic.gffunds.com.cn/image/course/2022-11-18/c3053b98-b8e7-461a-a20d-1a28f3c6fa39.jpg",
    "newUser": "3",
    "userChannel": "1",
    "userChangeChannel": "1",
    "serverTel": "************",
    "invitationColor": "rgba(242, 77, 40, 1)",
    "whiteConfig": {"element": ["推荐人姓名"], "failMsg": "验证失败，请输入正确的渠道经理姓名"},
    "registerInvitationSuccessEditor": "https://aim-pic.gffunds.com.cn/html/marketing/2022-05-16/612c1b1e-77cc-4f19-b0e6-8030a8fe2b36.html",
    "registerInvitationSuccessEditor2": "https://aim-pic.gffunds.com.cn/html/marketing/2022-05-16/612c1b1e-77cc-4f19-b0e6-8030a8fe2b36.html",
    "invitationImg": "https://aim-pic.gffunds.com.cn/image/course/2022-04-14/6b4b1318-2d33-4b01-959d-79f9fc96f62e.jpg",
    "clientParams": {
      "channelRemarksStart": 2,
      "cardTypeImgs": {
        "style_6": "https://fs-file.newbanker.cn/image/course/2021-08-16/3dccc17d-ad73-4ab6-8c16-f3b336a89d95.png",
        "style_7": "https://fs-file.newbanker.cn/image/marketing/2021-09-01/3965aeda-c0d2-4539-a432-f9dc9446a8b2.png",
        "nality": "https://fs-file.newbanker.cn/image/course/2021-08-16/cf865a47-3461-4142-b0d2-6600c7793b99.png",
        "nologobusiness": "https://fs-file.newbanker.cn/image/course/2021-08-16/6e945c91-bff2-4d19-b4af-63ce752933a9.png",
        "symple": "https://fs-file.newbanker.cn/image/course/2021-08-16/75ca1e67-42a5-4bba-82fb-611affcf5f01.png",
        "business": "https://fs-file.newbanker.cn/image/marketing/2021-08-25/a8750523-c7bb-426e-809b-ccddf89932a0.png",
        "verysymple": "https://fs-file.newbanker.cn/image/marketing/2021-08-25/b56b02f5-fdf7-48e9-970a-552daf69dfce.png"
      },
      "contactMe": "95075fc3871db9d4872460a15c98bbe0",
      "shortLinkInfo": {
        "fundProductGf": {
          "name": "基金产品",
          "originUrl": "#小程序://广发基金木棉花/基金解读/EWRQpc7FknssNOl",
          "decodeUrl": "%23%E5%B0%8F%E7%A8%8B%E5%BA%8F%3A%2F%2F%E5%B9%BF%E5%8F%91%E5%9F%BA%E9%87%91%E6%9C%A8%E6%A3%89%E8%8A%B1%2F%E5%9F%BA%E9%87%91%E8%A7%A3%E8%AF%BB%2FEWRQpc7FknssNOl",
          "needExtraData": true
        },
        "stockGf": {
          "name": "股票",
          "originUrl": "#小程序://广发基金木棉花/股票持仓/vHXgWkfk3Fui4xk",
          "decodeUrl": "%23%E5%B0%8F%E7%A8%8B%E5%BA%8F%3A%2F%2F%E5%B9%BF%E5%8F%91%E5%9F%BA%E9%87%91%E6%9C%A8%E6%A3%89%E8%8A%B1%2F%E8%82%A1%E7%A5%A8%E6%8C%81%E4%BB%93%2FvHXgWkfk3Fui4xk",
          "needExtraData": true
        }
      },
      "startUpConfig": {
        "picHeader": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-05-21/620ac811-66c3-41a3-9acc-7c06d2a85b6e.png",
        "picFooter": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-05-21/72d68b27-d06e-4bf4-b849-6915c7e3d3ae.png",
        "picTips": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-06-27/d4aff9d2-ab63-4a87-863e-ce33e0fadff4.png",
        "bgColorLeftTop": "#FF690B",
        "bgColorRightBottom": "#FF690B",
        "tipText": "",
        "defaultImgUrl": "https://aim-pic.gffunds.com.cn/image/course/2022-06-27/8f3f2b26-584b-477a-8f98-4fc9b4b2ad82.jpeg",
        "channelName": ["民生银行"],
        "channelStartPic": ["https://aim-pic.gffunds.com.cn/image/course/2022-07-01/0b85bb9a-328b-4b45-8e19-a02406640b57.jpeg"]
      },
      "GFEmpCateoryIdList": ["3174697135277144"],
      "defFundCode": "270002",
      "liveCodeEnum": ["develop", "trial", "release"],
      "liveCodeVersion": "release",
      "activityConfig": {
        "startDate": "2022-08-15 00:00:00",
        "endDate": "2022-09-15 23:59:59",
        "rewardRules": [{
          "type": "INVITE_LEVEL",
          "standard": [{"ruleFrom": 3, "ruleTo": 10000, "rewardKey": [1, 2, 3, 4]}]
        }, {
          "type": "SHARE_LEVEL",
          "standard": [{"ruleFrom": 200, "ruleTo": 399, "rewardKey": [5, 6]}, {
            "ruleFrom": 400,
            "ruleTo": 799,
            "rewardKey": [7, 8]
          }, {"ruleFrom": 800, "ruleTo": 10000, "rewardKey": [9, 10]}]
        }],
        "contentInfo": {
          "header": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-08-12/5aeaf543-fe0c-4e81-b554-0140e5b23820.png",
          "rule": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-08-12/55515f87-5eae-4567-9fa9-8fde3ffd5490.jpg",
          "content": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-07-20/1d55f0e2-b1ab-47a5-9be9-87cc3ae75c8f.jpg",
          "rewards": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-08-12/85d914c6-58e2-4d85-b5cb-6ddc39297cf7.png",
          "rewards_register": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-08-24/01d41931-3d64-4a2b-9217-deb5c705e835.png",
          "rewards_content": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-08-24/ad2b8cc3-a589-4ca0-8225-ddfffe6234a8.png",
          "footer": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-08-12/0cfc812f-b31c-46b8-9019-a8a504339116.png",
          "button": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-07-19/7ea9646a-0c59-433f-8e22-2adeec62a631.png",
          "titleBar": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-07-28/6acd4904-aaec-46af-8936-04b504f99d68.png",
          "line": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-07-19/b29838e0-80f2-4930-a99d-d573f43a7d97.png"
        },
        "statusClue": {
          "DONE": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-07-20/d037ff89-53b8-42a7-a14b-2a4a33ec0fe2.png",
          "LOSE": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-07-20/5c53d467-ff8b-443c-8632-ce2b32246343.png",
          "WRITING": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-07-20/bca2a5a6-93a6-48cd-9497-fa85a6d949c2.png",
          "GO_ON": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-07-20/530f77ae-b9bc-4e84-9e32-0cd959dca2e1.png",
          "NOT_MORE": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-07-20/b74f9663-a42d-492c-9279-8435a617a2c1.png",
          "REG_FULL": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-08-26/5bced005-7530-4367-a761-e71244615b80.jpg"
        },
        "btnClue": {
          "INVITE": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-07-19/ca41b9d1-071a-42a6-843a-1dad96a75b4a.png",
          "OVER": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-07-19/547f1ffe-9880-44d1-9346-096b4a64edb9.png",
          "SHARE": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-07-20/bac76f46-5123-4f8f-8a5e-6cfdc9861829.png",
          "NOT_BEGIN": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-07-19/92390c09-f9fe-4d6f-a315-1629fa4024c4.png",
          "GET_FOR": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-07-19/24f5abe6-13cf-4a1b-b0bc-c171d3b68d6d.png"
        },
        "activityRules": {"rulesTips": ["本活动仅限于“银行/券商/机构”的理财经理参加（识别出获奖者身份非理财经理，主办方有权取消奖励），活动日期为2022年8月15日至2022年9月15日，理财经理需在活动日期内完成相关任务后有机会获得相应的奖品。本次活动最终解释权归主办方所有。", "活动页面会追踪您参与活动进度，对于完成注册礼的前1000名理财经理或分享礼各前300名理财经理，会邀请您填写寄送信息，我们会在活动结束后统一寄送相关奖品（请保证电话及地址正确）。", "上述礼品价格选自某网络平台零售价（不含优惠），仅供参考。", "风险提示：本内容不构成本公司任何业务的宣传推介资料、投资建议或保证。投资有风险，选择需谨慎。"]},
        "popImg": [{
          "level": 0,
          "name": "仅注册礼",
          "imgUrl": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-07-22/be7cd162-995e-4de0-a4dd-e9b8696a9840.jpg?x-oss-process=image/format,jpg/resize,m_lfit,w_375,limit_0/interlace,1"
        }, {
          "level": 1,
          "name": "仅分享礼2",
          "imgUrl": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-07-22/7db885a2-0829-4e7f-8c06-223cfa5d2e1a.jpg?x-oss-process=image/format,jpg/resize,m_lfit,w_375,limit_0/interlace,1"
        }, {
          "level": 2,
          "name": "仅分享礼3",
          "imgUrl": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-07-22/ca4e7f6d-aa9b-4494-8097-e4f4272bb848.jpg?x-oss-process=image/format,jpg/resize,m_lfit,w_375,limit_0/interlace,1"
        }, {
          "level": 3,
          "name": "仅分享礼1",
          "imgUrl": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-07-22/d418b7dd-8ca6-4408-8338-7f8f7fcd3a4b.jpg?x-oss-process=image/format,jpg/resize,m_lfit,w_375,limit_0/interlace,1"
        }, {
          "level": 4,
          "name": "注册礼+分享礼1",
          "imgUrl": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-07-22/45ca4564-1f61-45ed-84f5-3b16128f2690.jpg?x-oss-process=image/format,jpg/resize,m_lfit,w_375,limit_0/interlace,1"
        }, {
          "level": 5,
          "name": "注册礼+分享礼2",
          "imgUrl": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-07-22/2ef374a0-a690-40d0-a2f2-b22df354e828.jpg?x-oss-process=image/format,jpg/resize,m_lfit,w_375,limit_0/interlace,1"
        }, {
          "level": 6,
          "name": "注册礼+分享礼3",
          "imgUrl": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-07-22/a8db2bcc-01ef-427d-90cc-2de9103151b4.jpg?x-oss-process=image/format,jpg/resize,m_lfit,w_375,limit_0/interlace,1"
        }]
      },
      "compassConfig": {
        "headerUrl": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-11-04/98a075d9-869c-4cb1-9362-81dbcaee06e5.jpg?x-oss-process=image/format,jpg/resize,m_pad,h_400,w_500",
        "bgBlock": {
          "picHeader": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-05-21/620ac811-66c3-41a3-9acc-7c06d2a85b6e.png?x-oss-process=image/format,png/resize,m_lfit,w_375,limit_0/interlace,1",
          "picFooter": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-05-21/72d68b27-d06e-4bf4-b849-6915c7e3d3ae.png?x-oss-process=image/format,png/resize,m_lfit,w_375,limit_0/interlace,1",
          "bgColorLeftTop": "#FF690B",
          "bgColorRightBottom": "#FF690B",
          "picBgUrl": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-08-11/189b395a-1687-4497-8c89-058119838f49.png"
        },
        "content": [{
          "name": "定投择时期",
          "action": "action://share/wechatFixedInvestmentCalculator",
          "itemUrl": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-10-18/96863628-74f0-4567-9a44-3c56bb2d38aa.png"
        }, {
          "name": "定投择基器",
          "action": "action://share/wechatProfitProbabilityCalculator",
          "itemUrl": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-10-18/79ca5bc4-7db0-4b6f-b964-9b03a123e6ae.png"
        }, {
          "name": "定投解套器",
          "action": "action://share/advFixedCaster",
          "itemUrl": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-10-18/3a45905d-c1b7-4b05-97b0-9a8ca4e8cadd.png"
        }, {
          "name": "目标盈定投",
          "action": "action://share/advTargetProfit",
          "itemUrl": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-10-18/c735e859-babf-4e4b-b051-f2e3b3ac72b9.png"
        }, {
          "name": "定投计算器",
          "action": "action://share/advFixedCompass",
          "itemUrl": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-10-18/b3aa03b0-7bde-4f40-a8fe-415f0f3a762f.png"
        }],
        "bottomBar": "https://aim-pic-dev.gffunds.com.cn/image/course/2022-08-11/597f1d4b-e274-48cf-b45c-e1d28ec7927d.png"
      }
    },
    "isFanInvitation": "0",
    "registerInvitationSuccessType2": "1",
    "showInv": "1",
    "registerInvitationSuccessType": "1",
    "isFan": "1",
    "registerSuccessType": "1",
    "registerSuccessType2": "1",
    "agreementSuccessType": "1",
    "registerSuccessEditor": "https://aim-pic.gffunds.com.cn/html/marketing/2022-05-16/398ab5d1-e50a-4ac5-8324-0ed6de5b7181.html",
    "registerSuccessEditor2": "https://aim-pic.gffunds.com.cn/html/marketing/2022-05-16/612c1b1e-77cc-4f19-b0e6-8030a8fe2b36.html",
    "invitationShareImg": "https://aim-pic.gffunds.com.cn/image/course/2022-04-01/ab6d1776-fa03-44ff-8759-eec68e1aee07.png",
    "disableUserTipImg": "https://aim-pic.gffunds.com.cn/image/course/2022-04-01/339283c5-fda6-41e3-b020-92983e0410cc.jpg",
    "userChangeChannelTip": "如需更改渠道，请联系客服人员",
    "popupContent": "活动明细",
    "popupImg": "https://aim-pic.gffunds.com.cn/image/course/2022-04-01/cddc5f41-3e45-4310-b68f-f210955fa098.png",
    "agreementSuccessContent": "为了给您提供更好的服务，系统会使用您在公众号内的使用记录，请您确认已经知晓并同意&&广发基金注册协议&&。",
    "agreementSuccessEditor": "https://aim-pic.gffunds.com.cn/html/marketing/2022-05-23/3604eed6-7de8-4ceb-a9d7-09d8c264895a.html",
    "cardConfig": {"count1": "1", "count2": "1"},
    "userChangeWeChat": "2",
    "userChangeWeChatTip": "不允许换绑微信",
    "registerSuccessContentTitle": "欢迎加入NewBanker-注册成功标题",
    "BackColor": "rgba(16, 112, 236, 1)",
    "ShadowColor": "rgba(8, 77, 166, 0.8)",
    "registerInvitationSuccessContent2Title": "欢迎加入NewBanker-邀请成功标题-1",
    "loginImg2": "https://fs-file.newbanker.cn/image/course/2022-01-15/ae2e0611-83c6-46d8-928c-7a76889c80df.jpg",
    "registerSuccessContent2Title": "注册成功页-验证",
    "registerInvitationSuccessContentTitle": "邀请注册成功页标题-1"
  },
  "employee": {
    "invitationImg": "https://fs-file.newbanker.cn/image/course/2021-12-13/bcbf46f1-1aca-4ded-a701-475e60bba12d.jpg",
    "invitationShareImg": "https://fs-file.newbanker.cn/image/course/2021-12-10/6eadc0b7-f1e8-4866-81de-31363cbefa2e.jpg",
    "invitationColor": "rgba(17, 113, 237, 1)",
    "employeeParams": {
      "cardTypeImgs": {
        "style_6": "https://fs-file.newbanker.cn/image/course/2021-08-16/3dccc17d-ad73-4ab6-8c16-f3b336a89d95.png",
        "style_7": "https://fs-file.newbanker.cn/image/marketing/2021-09-01/3965aeda-c0d2-4539-a432-f9dc9446a8b2.png",
        "nality": "https://fs-file.newbanker.cn/image/course/2021-08-16/cf865a47-3461-4142-b0d2-6600c7793b99.png",
        "nologobusiness": "https://fs-file.newbanker.cn/image/course/2021-08-16/6e945c91-bff2-4d19-b4af-63ce752933a9.png",
        "symple": "https://fs-file.newbanker.cn/image/course/2021-08-16/75ca1e67-42a5-4bba-82fb-611affcf5f01.png",
        "business": "https://fs-file.newbanker.cn/image/marketing/2021-08-25/a8750523-c7bb-426e-809b-ccddf89932a0.png",
        "verysymple": "https://fs-file.newbanker.cn/image/marketing/2021-08-25/b56b02f5-fdf7-48e9-970a-552daf69dfce.png"
      },
      "configTips": {
        "myActionData": ["选择时间段内，本人的分享行为次数（含分享好友、群、朋友圈）", "选择时间段内，本人分享的内容，被浏览的次数", "选择时间段内，本人分享的内容，被浏览的人数"],
        "channelCon": ["选择时间段内，小程序中的客户经理，浏览各类内容的总时长占比和平均时长占比", "注：以上数据为您管理权限范围内的数据"],
        "channelGetCus": ["选择时间段内，小程序中的客户经理，获取的访客人数", "选择时间段内，小程序中的客户经理，获取访客的所有浏览次数", "注：以上数据为您管理权限范围内的数据"],
        "channelAction": ["选择时间段内，在小程序中有浏览行为的人数", "选择时间段内，在小程序所有用户的浏览行为次数", "选择时间段内，在小程序所有用户的分享行为次数", "注：以上数据为您管理权限范围内的数据"],
        "channelFans": ["选择时间段内，小程序的新注册用户", "还未在小程序激活的历史用户", "选择时间段内，小程序的新增粉丝与已激活粉丝的总和", "注：以上数据为您管理权限范围内的数据"],
        "channelConPersonal": ["当前用户浏览各类内容的总时长占比和平均时长占比"],
        "activeValue": ["通过当前用户的访问、浏览、分享行为综合计算的活跃度指标"],
        "contactsRoute": ["当前用户首次访问系统页面时的行为动线"]
      },
      "inviteTips": "享NewBanker独家权益",
      "usingTutorials": ["https://fs-file.newbanker.cn/image/marketing/2021-09-02/7c1a20aa-0c90-4ec4-a0e2-83d4d1c6b299.jpeg"],
      "appId": "",
      "shareTips": {"shortName": "成为渠道用户", "shareCardTip": "", "shareInviteTip": "选中后，页面底部将显示注册入口，快去邀请你的粉丝吧～"},
      "cardBg": "linear-gradient(151deg, #567AEA 0%, #2B5DD1 100%);",
      "bindingPrompt": "绑定后，即可同步你在小程序端的线索数据",
      "bindPageTitle": "请绑定渠道端的登录微信",
      "bindingPromptBtn": "请绑定渠道端的登录微信"
    },
    "channelUserBindingPagePrompt": "https://fs-file.newbanker.cn/image/course/2021-12-13/5a0edb45-07a4-474d-9718-f4c308a8b248.jpg"
  },
  "content": {
    "intro": "<h1><span style=\"font-size: 24px;\"><strong>智能营销平台</strong><strong>-</strong><strong>新人指引</strong></span></h1>\n<p><img title=\"src=http---img.jj20.com-up-allimg-tp01-1ZZQ20QJS6-0-lp.jpg&amp;refer=http---img.jj20.com&amp;app=2002&amp;size=f9999,10000&amp;q=a80&amp;n=0&amp;g=0n&amp;fmt=jpeg.jpeg\" src=\"https://fs-file.newbanker.cn/image/course/2022-02-17/2ceae38c-1d7e-4674-9073-200e4b0d2ceb.jpeg\" alt=\"\" /><strong><img src=\"tinymce/tinymce/file-system/api/v1/fs/stream?url=https://fs-file.newbanker.cn/image/course/2022-02-16/316ccc2a-d0ef-4f68-be59-d1eb9e230fbc.jpg\" alt=\"\" /></strong><strong><img src=\"tinymce/tinymce/file-system/api/v1/fs/stream?url=https://fs-file.newbanker.cn/image/course/2022-02-16/316ccc2a-d0ef-4f68-be59-d1eb9e230fbc.jpg\" alt=\"\" /></strong><strong><img src=\"tinymce/tinymce/file-system/api/v1/fs/stream?url=https://fs-file.newbanker.cn/image/course/2022-02-16/316ccc2a-d0ef-4f68-be59-d1eb9e230fbc.jpg\" alt=\"\" /></strong><strong><img src=\"tinymce/tinymce/file-system/api/v1/fs/stream?url=https://fs-file.newbanker.cn/image/course/2022-02-16/316ccc2a-d0ef-4f68-be59-d1eb9e230fbc.jpg\" alt=\"\" /></strong><strong><img src=\"tinymce/tinymce/file-system/api/v1/fs/stream?url=https://fs-file.newbanker.cn/image/course/2022-02-16/316ccc2a-d0ef-4f68-be59-d1eb9e230fbc.jpg\" alt=\"\" /></strong><strong><img src=\"tinymce/tinymce/file-system/api/v1/fs/stream?url=https://fs-file.newbanker.cn/image/course/2022-02-16/316ccc2a-d0ef-4f68-be59-d1eb9e230fbc.jpg\" alt=\"\" /></strong><strong><img src=\"tinymce/tinymce/file-system/api/v1/fs/stream?url=https://fs-file.newbanker.cn/image/course/2022-02-16/316ccc2a-d0ef-4f68-be59-d1eb9e230fbc.jpg\" alt=\"\" /></strong></p>\n<h3><span style=\"font-size: 18px; color: #2880b9;\"><strong>Q1：为什么客户经理必须运营好朋友圈？</strong></span></h3>\n<p style=\"text-align: left;\"><span style=\"font-size: 14px;\">&nbsp; &nbsp; &nbsp; 其实大部分客户经理的痛点不在于如何安抚客户。而是如何获得客户，以及如何取</span><span style=\"font-size: 14px;\">得客户的信任。</span></p>\n<p style=\"text-align: left;\"><span style=\"font-size: 14px;\">&nbsp; &nbsp; &nbsp; &nbsp;打个比方，现在打开微信，微信列表里有多少人知道我们是做客户经理的呢？</span></p>\n<p style=\"text-align: left;\"><span style=\"font-size: 14px;\">&nbsp; &nbsp; &nbsp; &nbsp;根据数据表明，一个人的通讯录里，清楚的知道这个人工作的人数，95%不会超过50人。</span></p>\n<p style=\"text-align: left;\"><span style=\"font-size: 14px;\">&nbsp; &nbsp; &nbsp; &nbsp;所以很多客户经理并不是缺少客户源，而是无法激活自己微信中的客户。</span></p>\n<p style=\"text-align: left;\"><span style=\"font-size: 14px;\">&nbsp; &nbsp; &nbsp; 如果选择主动群发，则会对客户造成骚扰，甚至会被客户直接拉黑。</span></p>\n<p style=\"text-align: left;\"><span style=\"font-size: 14px;\">&nbsp; &nbsp; &nbsp; 所以客户经理如果想要激活沉睡的客户，只能把握被动曝光的窗口&mdash;朋友圈。</span></p>\n<p style=\"text-align: left;\"><span style=\"font-size: 14px;\">&nbsp; &nbsp; &nbsp; 在座的有多少人用客户的视角审视过自己的朋友圈呢？</span></p>\n<p style=\"text-align: left;\"><span style=\"font-size: 14px;\">&nbsp; &nbsp; &nbsp;一个充斥着广告、营销话术、以及产品信息的朋友圈无疑是客户屏蔽我们朋友圈的最好理由。</span></p>\n<p style=\"text-align: left;\"><span style=\"font-size: 14px;\">&nbsp; &nbsp; 《销售大师》的作者伊万&middot;米斯纳说过：&ldquo;真正成功的销售从来都不是主动成交的。&rdquo;</span></p>\n<p style=\"text-align: left;\"><span style=\"font-size: 14px;\">&nbsp; &nbsp; &nbsp;同样，一个可以精准获客的朋友圈，并不会充斥着对客户的广告，反而是根据客户的类型以及生命周期，按照时间段给客户展示不同的内容。</span></p>\n<p style=\"text-align: left;\"><span style=\"font-size: 14px;\">&nbsp; &nbsp; 如果你打开自己的朋友圈，发现还只有广告的话，那么你可能正在被你的客户抛弃！</span></p>\n<p style=\"text-align: left;\"><span style=\"font-size: 18px; color: #2880b9;\">&nbsp;</span></p>\n<p><strong><span style=\"font-size: 18px; color: #2880b9;\">Q2： 微信个人号应该如何运营呢？</span></strong></p>\n<p>&nbsp; &nbsp; &nbsp; <span style=\"font-size: 14px;\">如今很多企业会用私域流量的方式进行用户运营，私域流量可以触达到精准用户，提高转化率，同时运营人员与用户对接的日常工作号，也是一张较为专业化和权威化的工作名片；使用微信个人号，有利于打造形象化IP，提高产品认知与品牌曝光，对运营人员来说，这有效地避免了个人生活与职场工作的交叉渗透。</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; &nbsp; &nbsp;微信个人号应该如何运营可以从<span style=\"color: #3598db;\"><em>五个角度进行</em></span>&mdash;&mdash;<em><span style=\"color: #3598db;\">IP形象打造、朋友圈动态、1VN微信群、1V1、视频号打造</span></em>。</span></p>\n<p><span style=\"color: #3598db;\"><strong><span style=\"font-size: 14px;\">①、IP形象打造</span></strong></span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; &nbsp; 正所谓&ldquo;人靠衣装&rdquo;，一个人的穿衣打扮影响其颜值，与之相似的，微信的个人化形象打造需要一些外在的&ldquo;修饰&rdquo;，而这些&ldquo;修饰&rdquo;包括微信头像、朋友圈封面、朋友圈签名等要素。</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; &nbsp;微信头像是专业IP形象的核心要素，一般而言，微信个人号的头像主要有以下几种：</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; 个人职场写真常见于以运营者/管理者本人为运营中心的场景，比如基金大咖、经济专家等</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; &nbsp;公司标识指的是与公司强关联的形象标识，比如公司logo、办公大楼、品牌名等。</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; &nbsp; 广告语大字常见于强销售属性的使用场景，比如理财顾问、投资顾问等。</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; &nbsp; 专属表情包，顾名思义，就是公司内部产出的官方表情包，表情生动，有亲和力。</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; &nbsp; 至于个人名片，则与上述的&ldquo;广告语大字&rdquo;极为相似，所不同的是，个人名片常常把电子版的个人名片直接作为微信头像，主打推介自己、拓展人脉、寻找合作；而广告语大字则往往以宣传性、诱导性的大字作为头像，其主要目的是宣传产品、服务、品牌。</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; &nbsp;最后一条，以颜值较高的公司员工照片或者职场照，常常具备较强的视觉冲击，令人耳目一新，适用于强个人场景的微信运营。</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; &nbsp;此外，朋友圈封面也是增强曝光的重要渠道。打造朋友圈封面，可以从这几个方面入手：</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; 需要注意的是，以上要素并不是割裂开来的，可以创意组合，比如站在公司办公区logo前的个人形象照，是不是更加吸引人呢？</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; &nbsp;至于朋友圈签名，其实也是彰显个人专业化形象的重要部分，能够体现个人气质，吸引用户眼球。这里可以考虑一句话的个人简介，比如&ldquo;专业理财、伴您左右&rdquo;，也可以考虑加入公司、产品、品牌的主要宣传语。</span></p>\n<p><strong><span style=\"font-size: 14px; color: #3598db;\">②、朋友圈动态</span></strong></p>\n<p><span style=\"font-size: 14px;\">&nbsp; &nbsp; 朋友圈动态是抢占用户碎片化时间的主阵地，也是不容忽视的微信个人号运营渠道；利用朋友圈动态，可发布内容资讯，更新营销信息，打造品牌形象，塑造个人IP，提高产品曝光，进行用户教育。</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; 朋友圈内容：</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; &nbsp;品牌背书可以讲讲公司的发展史、产品的演变史、创始人的发迹史等；</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; &nbsp;理财产品专业管理人介绍可以用一两句话概括其的特质，比如学历、从业年限等；</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; &nbsp;产品收益，比如某产品是过往业绩如何亮眼，收益排名等；</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; 用户关怀，可以从早安、晚安、节日问候等方面触达用户，体现温情一面。</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; 发布动态注意事项：</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; 发布朋友圈动态之前，做好微信好友的标签筛选与更新。</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; 发布动态时，需进行分组，一般设置为仅某部分用户可见，但是关于品牌背书、产品指南等普适性的内容可设为全员可见。</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; 微信动态可以设置强提醒（&ldquo;提醒谁看&rdquo;），目前可以设置20个微信好友的强提醒，可以针对平时较为活跃、未购课的用户精准触达。</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; 至于地理标签是否加注，可以根据内容而定，一般来说，品牌背书的朋友圈动态需要加上公司总部所在地的地理标签，显得较为专业和权威。</span></p>\n<p><strong><span style=\"font-size: 14px; color: #3598db;\">③、微信群</span></strong></p>\n<p><span style=\"font-size: 14px;\">&nbsp;这里主要说一下金融行业需要注意的微信群运营事项：</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; 社群运营之前准备好行事日历、链接、物料，注意运营节奏和重要的时间节点；</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; 制定群规，明确不能灌水、发布广告等信息；</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; 发布群公告和群消息，及时@与回复群成员；</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; 管理员发布完群消息之后，要有马甲水群，需提前多备几个小号，或者多拉几个同事进群，烘托群氛围；</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp;主要的运营内容：产品介绍，运营策略、预期收益、安全性和风险提示等等&hellip;&hellip;</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp;主要的时间节点：开市、休市、开放日等。</span></p>\n<p><strong><span style=\"font-size: 14px; color: #3598db;\">④、1V1</span></strong></p>\n<p><span style=\"font-size: 14px;\">&nbsp; &nbsp;基本上与微信群的操作相同，有所不同的是可以根据用户的需求定制个性化方案，操作更加精准，触达成本较高，但更加人性化；可以利用微信自有的&ldquo;群发助手&rdquo;这一辅助功能去发，但它也有很大弊端，比如说不能带链接，文字和图片无法一起发送、要分两次单独发，一次只能发200人以内。</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; 1V1的注意事项主要有：</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; 注意发布频率，最好一天不超过2条，否则很容易引起用户反感，被删好友或者被拉黑；</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; 在重要的时间节点发布内容，比如抢红包前半小时、市场解读直播开始前20分钟等等</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; 注意内容文案的语气，最好是平易近人的、人性化的，不要显得像是机器人在发消息；</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; 文案不宜过长，用户很有可能没有耐心看那么多文字内容；</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; 发布1V1消息之后，有些微信好友会予以回复，请好好珍惜他们，及时回复他们。</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; 此外，还有利用微信个人号的直播间运营；需要注意的是：提前发布直播间文明听课规范，发布课程要点，进行直播间提问与评论区互动答疑，马甲号烘托氛围。</span></p>\n<p><span style=\"color: #3598db;\"><strong><span style=\"font-size: 14px;\">⑤、视频号</span></strong></span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; &nbsp;发布视频号需要注意的事项：</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; &nbsp;最好发布紧贴时事热点进行解读分析。</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; &nbsp;视频市场尽量控制在一分钟左右。</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; &nbsp;出镜人一定要穿正装，视频背景一定要挑选和金融相关的背景等等。</span></p>\n<h3><span style=\"color: #3598db; font-size: 18px;\"><strong>Q3：</strong><strong>客户经理</strong><strong>如何做客户安抚？</strong></span></h3>\n<p><span style=\"font-size: 14px;\">&nbsp; &nbsp; &ldquo;普通经理等待客户沟通，成功经理主动沟通客户。&rdquo;</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; &nbsp; &nbsp;很多客户由于没有金融知识傍身，不知道下跌的原因，就会习惯&ldquo;负面情绪转移&rdquo;，特别是在客户经理没有及时与其沟通的时候，更容易形成：&ldquo;基金下跌=亏了这么多钱客户经理都没有告诉我=我被蒙在鼓里了，客户经理骗我&rdquo;</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; &nbsp; &nbsp;这种时候更需要我们主动安抚客户，大胆跟进客户问题，就会把客户培养成自己的忠实&ldquo;粉丝&rdquo;。从而避免每逢大跌就导致客户流失的主要因素。</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; &nbsp; &nbsp;那如何改变这种情况呢？抢先一步，联系客户。</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; &nbsp; &nbsp;我们客户经理应该主动关注客户账户的盈亏情况，遇到剧烈波动时应该主动先客户联系自己之前，主动联系客户。并根据客户的问题适时地准备相应的话术。</span></p>\n<h3><span style=\"font-size: 18px; color: #3598db;\"><strong>Q4：</strong><strong>客户经理</strong><strong>为什么要做投教科普?</strong></span></h3>\n<p><span style=\"font-size: 14px;\">&nbsp; &nbsp; &ldquo;一个不会理财的客户，是不会找你理财的。&rdquo;</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; &nbsp; 资产配置与财富管理，作为在国内新兴的财富增值的方式。对于很多客户来说是又危险又充满诱惑。</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; &nbsp; 这个时候如果有一个引路人，可以很好的对他进行投教的科普，一些风险的告知，那么等客户产生理财需求的时候，会更倾向于找这个对他科普的人。</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; &nbsp; 所以投教科普对于我们来说，一方面可以帮助客户获得基本的资产配置的知识，另一方面可以帮助我们把握客户，快速出单。是一个一石二鸟，一箭双雕的精妙之事。</span></p>\n<h3><span style=\"color: #3598db; font-size: 18px;\"><strong>Q5</strong><strong>：朋友圈助手如何使用</strong></span></h3>\n<p><span style=\"font-size: 14px;\">&nbsp; &nbsp; &nbsp;①打开朋友圈助手</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; &nbsp; &nbsp;②选择想要转发的图文，视频等内容，直接点击【转发获客】</span></p>\n<p><span style=\"font-size: 14px;\">&nbsp; &nbsp; &nbsp;③复制文案发布朋友圈或者客户群亦或者单独发给客户，同时配上保存的图片即可</span></p>\n<p>&nbsp;</p>",
    "assistant": "https://pyzylc.pystandard.cn/static/mobile/index.html#/main"
  }
}

module.exports = {
  sysConfig
}

const tabBar = {
  "backgroundColor": "#FFFFFF",
  "color": "#CCCCCC",
  "selectedColor": '#f04b28ff',
  "list": [
    {
      "id": 3149722965773313,
      "name": '首页',
      "ordinal": 0,
      "pageId": 3149722965773313,
      "pageName": "首页",
      "pageShow": true,
      "cardIds": "3175349668316855,3175349668316857,3175349668316859,3175349668316861,3242885546024588",
      "icon": {
        "iconNormal": "https://aim-pic.gffunds.com.cn/image/course/2022-04-01/d1c06b8a-6994-45e6-bb6a-378cb64dedcd.png",
        "iconSelector": "https://aim-pic.gffunds.com.cn/image/course/2022-04-01/3d7874d6-c80f-4e98-9b68-b1ced8c37b1b.png"
      },
      "pageTabBarConf": {
        "name": "首页",
        "tabName": "HOME",
        "type": 0,
      },
      "type": 0,
      "tabName": "HOME",
      "path": "/pages/home/<USER>",
      "tabPath": "/pages/home/<USER>",
      "status": "ENABLE"
    },
    {
      "id": 3150596094039568,
      "name": '产品',
      "ordinal": 1,
      "pageId": 3150596094039568,
      "pageName": "产品",
      "pageShow": true,
      "cardIds": "3175349668316863,3175349668316865,3175349668316889",
      "icon": {
        "iconNormal": "https://aim-pic.gffunds.com.cn/image/course/2022-04-01/f87feda2-84a3-4b82-8822-394f59211c1d.png",
        "iconSelector": "https://aim-pic.gffunds.com.cn/image/course/2022-04-01/0a92e9d3-86bc-4e00-8964-c7caae5572e8.png"
      },
      "pageTabBarConf": {
        "name": "产品",
        "tabName": "PRODUCT",
        "type": 1,
      },
      "type": 1,
      "tabName": "PRODUCT",
      "path": "/pages/product/index",
      "tabPath": "/pages/product/index",
      "status": "ENABLE"
    },
    {
      "id": 3153330889300743,
      "name": "陪伴",
      "ordinal": 2,
      "pageId": 3153330889300743,
      "pageName": "陪伴",
      "pageShow": true,
      "cardIds": "3175349668316871,3180411496991320,3175349668316873,3180411496991314,3180411496991312,3175349668316885,3242885546024586,3242885546024594,3293421339190530",
      "icon": {
        "iconNormal": "https://aim-pic.gffunds.com.cn/image/course/2022-04-01/e07d6ae5-d3cc-4a1a-9151-cde2819514c9.png",
        "iconSelector": "https://aim-pic.gffunds.com.cn/image/course/2022-04-01/ebefa580-8235-4ffd-9dee-550f9c48fe01.png"
      },
      "pageTabBarConf": {
        "name": "陪伴",
        "tabName": "ACCOMPANY",
        "type": 2,
      },
      "tabName": "ACCOMPANY",
      "path": "/pages/accompany/index",
      "tabPath": "/pages/accompany/index",
      "type": 2,
      "status": "ENABLE"
    },
    {
      "id": 3153342490745729,
      "name": "观点",
      "ordinal": 3,
      "pageId": 3153342490745729,
      "pageName": "观点",
      "pageShow": true,
      "cardIds": "3175349668316869,3175349668316875,3205775568967042",
      "icon": {
        "iconNormal": "https://aim-pic.gffunds.com.cn/image/course/2022-04-01/e7fe6bb0-36d8-4b4f-9134-d1d335571e87.png",
        "iconSelector": "https://aim-pic.gffunds.com.cn/image/course/2022-04-01/5ccfc4d3-6720-4885-ba78-8de09f6b00ea.png"
      },
      "pageTabBarConf": {
        "name": "观点",
        "tabName": "VIEWPOINT",
        "type": 3,
      },
      "type": 3,
      "tabName": "VIEWPOINT",
      "path": "/pages/viewPoint/index",
      "tabPath": "/pages/viewPoint/index",
      "status": "ENABLE",
    },
    {
      "id": 3153330889300744,
      "name": "我的",
      "ordinal": 4,
      "pageId": 3153330889300744,
      "pageName": "我的",
      "pageShow": true,
      "cardIds": "3175349668316845,3242885546024578,3175349668316847,3175349668316849,3306358971869056",
      "icon": {
        "iconNormal": "https://aim-pic.gffunds.com.cn/image/course/2022-04-01/6e834f2d-42d5-43bc-854e-afbeeaec2e89.png",
        "iconSelector": "https://aim-pic.gffunds.com.cn/image/course/2022-04-01/00205a40-263c-46b5-804e-b809a6711054.png"
      },
      "pageTabBarConf": {
        "name": "我的",
        "tabName": "MINE",
        "type": 4,
      },
      "type": 4,
      "tabName": "MINE",
      "path": "/pages/mine/mine",
      "tabPath": "/pages/mine/mine",
      "status": "ENABLE"
    }
  ]
}

module.exports = {
  tabBar
}

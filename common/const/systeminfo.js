const {
  platform,
  model,
  windowWidth,
  safeArea,
  screenHeight: screenHeightPx,
  statusBarHeight: statusBarHeightPx,
} = wx.getSystemInfoSync()

/**
 * 小程序顶部导航的相关设置
 */
const config = {
  tabBarHeight: Number(screenHeightPx * 0.105).toFixed(2) * 1 || 98,
  iphoneXSafeHeight: 66,
  titleHeight: 44 + statusBarHeightPx,
  prefix: 24, // iphone X + 24
  shareBottomBar: 80,
  bottomHeight: screenHeightPx - safeArea.bottom
}

const IPHONE_TYPES = [
  'iPhone XS Max',
  'iPhone XS',
  'iPhone X',
  'iPhone XR',
  'iPhone 11',
  'iPhone 11 Pro',
  'iPhone 11 Pro Max',
  'iPhone 12',
  'iPhone 12 Pro',
  'iPhone 12 Pro Max',
  'iPhone 13',
  'iPhone 13 Pro',
  'iPhone 13 Pro Max',
  'iPhone 14',
  'iPhone 14 Pro',
  'iPhone 14 Pro Max',
]

const isX = IPHONE_TYPES.includes(model)
  || !model.split('iPhone X')[0]
  || !model.split('iPhone 11')[0]
  || !model.split('iPhone 12')[0]
  || !model.split('iPhone 13')[0]
  || !model.split('iPhone 14')[0]

function px2rpx(px) {
  return px * 750 / windowWidth
}

function rpx2px(rpx) {
  return windowWidth / 750 * rpx
}

// 顶部导航高度，单位为rpx
function getTitleHeight(unitPx = false) {
  let titleHeightPx = config.titleHeight

  if (unitPx){
    return titleHeightPx
  }
  return px2rpx(titleHeightPx)
}

// 底部高度，单位为rpx
function getFooterHeight() {
  return config.tabBarHeight + (isX ? config.iphoneXSafeHeight : 0)
}

function getBottomHeight() {
  return screenHeightPx - safeArea?.bottom || 0
}

const titleHeight = getTitleHeight()

const titleHeightPx = getTitleHeight(true)

const footHeight = getFooterHeight()

const footHeightPx = rpx2px(footHeight)

const screenHeight = px2rpx(screenHeightPx)


//单位rpx
const statusBarHeight = px2rpx(statusBarHeightPx)

// 分享按钮高度
const shareBottomSize = px2rpx(config.shareBottomBar)

const bottomHeightPx = getBottomHeight()

const {navigationBarHeight, navBottomFloat} = getMBBoundingClientRect()

//获取内容区域高度 单位rpx 默认页面有NavBar 没有TabBar
function getContentHeight(hasNavBar = true, hasTabBar = false, hasShareBar = false) {
  let contentHeight = hasNavBar ? screenHeight - titleHeight : screenHeight
  contentHeight = hasTabBar ? contentHeight - footHeight : contentHeight
  if (hasShareBar){
    contentHeight = contentHeight - shareBottomSize
  }

  return contentHeight
}

function getRegisterPageHeight(){
  return screenHeight - titleHeight - safeArea.bottom
}

function getContentHeightPx(hasNavBar = true, hasTabBar = false, hasShareBar = false) {
  let contentHeightPx = hasNavBar ? screenHeightPx - titleHeightPx : screenHeightPx
  contentHeightPx = hasTabBar ? contentHeightPx - footHeightPx : contentHeightPx
  if (hasShareBar){
    contentHeightPx = contentHeightPx - shareBottomSize
  }
  contentHeightPx = contentHeightPx - bottomHeightPx

  return contentHeightPx
}

function getMBBoundingClientRect() {
  const {
    height,
    top,
    bottom,
  } = wx.getMenuButtonBoundingClientRect()

  let navigationBarHeight = (top - statusBarHeightPx) * 2 + height
  let navBottomFloat = titleHeightPx - bottom
  return {navigationBarHeight, navBottomFloat}
}

export {
  platform,
  model,
  isX,
  titleHeight,
  titleHeightPx,
  footHeight,
  footHeightPx,
  screenHeight,
  screenHeightPx,
  statusBarHeight,
  statusBarHeightPx,
  shareBottomSize,
  getContentHeight,
  getContentHeightPx,
  getRegisterPageHeight,
  navigationBarHeight,
  navBottomFloat,
  bottomHeightPx
}

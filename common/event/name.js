// 消除tabBar
const DISMISS_TAB_BAR = 'DISMISS_TAB_BAR'

const BREAK_IN_INIT_SUCCESS = 'BREAK_IN_INIT_SUCCESS'

const TAB_LISTENER_EVENT = 'TAB_LISTENER_EVENT'

const SEND_WEBVIEW_OPTIONS = 'SEND_WEBVIEW_OPTIONS'
const SEND_REGISTER_OPTIONS = 'SEND_REGISTER_OPTIONS'

const REFRESH_PAGE_DATA = 'REFRESH_PAGE_DATA'

const SEND_EVENT_TO_POLYMERS = 'SEND_EVENT_TO_POLYMERS'
const SEND_EVENT_TO_SHARE_LIST = 'SEND_EVENT_TO_SHARE_LIST'

const FETCH_NET_DATA_ERROR = 'FETCH_NET_DATA_ERROR'

const SET_SCROLL_TO_TARGET = 'SET_SCROLL_TO_TARGET'

const SET_REFRESH_PAGE = 'SET_REFRESH_PAGE'

const SET_PAGE_BACK = 'SET_PAGE_BACK'

const SET_SHOW_RECORD = 'SET_SHOW_RECORD'

const DO_READY_LINE_REFRESH = 'DO_READY_LINE_REFRESH'

const DO_OVER_WAIT_LINE = 'DO_OVER_WAIT_LINE'

const SET_SCROLL_NAV_BAR_SIZE = 'SET_SCROLL_NAV_BAR_SIZE'

export {
  DISMISS_TAB_BAR,
  SEND_WEBVIEW_OPTIONS,
  SEND_REGISTER_OPTIONS,
  REFRESH_PAGE_DATA,
  SEND_EVENT_TO_POLYMERS,
  FETCH_NET_DATA_ERROR,
  SEND_EVENT_TO_SHARE_LIST,
  SET_SCROLL_TO_TARGET,
  SET_REFRESH_PAGE,
  SET_PAGE_BACK,
  DO_READY_LINE_REFRESH,
  DO_OVER_WAIT_LINE,
  TAB_LISTENER_EVENT,
  SET_SCROLL_NAV_BAR_SIZE,
  BREAK_IN_INIT_SUCCESS,
  SET_SHOW_RECORD
}

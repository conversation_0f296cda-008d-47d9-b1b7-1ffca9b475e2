import {
  enums,
  interaction,
  util,
  qs,
  shapeShift,
  vLog,
  wbs,
  breakIn,
  global,
} from '../../../common/index.js'

import {
  getOpenId,
  setCustomerTypeInt,
  getUnionID,
  getUser,
  getWechatInfoId,
  setUser,
  getSystemInfo,
  setToken,
  setUserId,
  setUserLogin,
  setUserRole,
  getUserPhone,
  setCurrRoleType
} from "../../../common/utils/userStorage";

import {
  checkLogin
} from "../../../common/nb/home";

const {
  parseOptions2Object,
  panTransformDCode2Object
} = util

const {
  LoginState,
  ROLE_REGISTER,
  ROLE_CURRENT,
  ROLE_LIST_CHANGE,
  ROLE_LIST_CHOOSE,
  ROLE_TYPE_IMAGE,
  CUSTOMER_CHANGE,
  REGISTER_TYPE,
  GLOBAL_START_PATH,
  INVITER_INFO
} = enums

const app = getApp()

Page({
  data: {
    title: '角色',
    roleInfo: {},
    currRoleInfo: {},
    sysInfo: {},
    list: [],
    wInfo: {},

    isChange: false,
    showLoading: false,
    singlePage: '',
  },

  onLoad(options = {}) {
    vLog.log('ROLE options, >>>>', qs.parse(options))
    let roleInfo = parseOptions2Object(options)
    const wInfo = getUser()
    const sysInfo = getSystemInfo()

    vLog.log('ROLE roleInfo >>>>', roleInfo)
    const { currRole, pageType = '', fromRegister = '', singlePage = '', signinTypeStr = '' } = roleInfo
    if (fromRegister) {
      let fRes = panTransformDCode2Object(roleInfo)
      vLog.log('ROLE fRes #### >>>>', fRes)
      roleInfo = { ...fRes }
    }

    let listChange = []
    let listChoose = ROLE_LIST_CHOOSE
    let currRoleInfo = ROLE_CURRENT.find(item => item && item.mark === currRole * 1)
    const { fullName = '' } = wInfo
    if (fullName === '普通客户' || fullName === INVITER_INFO) {
      currRoleInfo = CUSTOMER_CHANGE
    }

    ROLE_LIST_CHANGE.forEach((item) => {
      const { mark } = item || {}
      if (!isNaN(currRole * 1) && Number(currRole) !== mark) {
        listChange.push(item)
      }
    })

    let isChange = pageType === 'CHANGE'
    vLog.log('ROLE list isChange >>>>', isChange).report()
    this.setData({
      singlePage,
      list: isChange ? listChange : listChoose,
      currRoleInfo,
      isChange,
      roleInfo,
      sysInfo,
      wInfo,
      signinTypeStr,
      title: isChange ? '选择身份' : '角色'
    })
  },

  onShow() {
    wx.hideShareMenu({
      menus: ['shareAppMessage', 'shareTimeline']
    })
  },

  onHide() {
    vLog.info('ROLE onHide').report()
    this.setData({
      showLoading: false
    })
  },

  onUnload() {
    vLog.info('ROLE onUnload').report()
    this.setData({
      showLoading: false
    })
  },

  async onRoleAction(e = {}) {
    vLog.info('ROLE onRoleAction e >>', e).report()
    const {
      dataset: {
        item: {
          mark,
          type,
          truth = '',
        }
      },
    } = e.target
    /**
     * isChange 是否为切换用户，true切换 false注册
     */
    const { isChange, roleInfo = {}, signinTypeStr = '' } = this.data
    const { statusCode } = roleInfo
    vLog.info('ROLE statusCode >>>', statusCode).report()

    const params = {
      openid: getOpenId(),
      unionid: getUnionID(),
      wechatInfoId: getWechatInfoId(),
      phone: getUserPhone(),
      customerType: mark
    }
    const { isBanded, code } = await breakIn({ name: 'doCheckBanding', params })
    if (isBanded) {
      // 后台录入激活
      interaction.showToast('激活成功')
      const pRes = qs.parse(roleInfo)
      vLog.log('ROLE pRes #### >>>>', pRes)
      setCustomerTypeInt(Number(mark))
      setCurrRoleType(type)
      if (pRes.hasOwnProperty('pageType') && pRes.pageType === 'CHANGE') {
        pRes['toTargetPathParams'] = {
          type: LoginState.START_UP,
          way: 'reSetSite',
          targetRole: type
        }
      }
      if (code === LoginState.INREVIEW || code === LoginState.FORBIDDEN) {
        return wx.reLaunch({
          url: `/pages/loginAndRegist/startUp/index`
        })
      }
      return breakIn({ name: 'doRouteTargetPage', type, registerInfo: pRes })
    }

    if (!isChange || statusCode && `${statusCode}` === LoginState.MULTIPLE_CUSTOMER) {
      // 注册方向
      return this.onChooseRoleForRegister({ mark, type, truth, signinTypeStr })
    } else {
      // 切换方向
      this.setData({
        showLoading: true
      }, () => this.onChooseRoleForChange({ mark, type, registerType: REGISTER_TYPE.CHANGE, signinTypeStr }))
    }
  },

  // 注册方向
  async onChooseRoleForRegister(params = {}) {
    vLog.log('ROLE onChooseRoleForRegister this.data,params >>>>', this.data, params)
    const { roleInfo } = this.data
    const { mark, type } = params

    const roleParams = {
      ...params,
      registerType: REGISTER_TYPE.REGISTER
    }

    const { code: wxcode } = await wx.login()
    const { success, data, code } = await checkLogin({ code: wxcode, customerType: mark, wechatCode: global.SOURCE_CODE })
    // 注册方向
    if (!success || !data) {
      switch (code) {
        // 账号审核中，请稍后登录 => 跳转审核中页面
        case LoginState.INREVIEW: {
          setUserLogin(false)
          setUserRole(code)
          let url = encodeURIComponent(`${wbs.gfH5}/share/advInviteRegister?result=${LoginState.INREVIEW}`)
          return wx.reLaunch({
            url: `/pages/common/webview/webPage?url=${url}&hideHomeNav=1&banShare=1`
          })
        }
        //禁用 => 跳转禁用页面
        case LoginState.FORBIDDEN: {
          setUserLogin(false)
          setUserRole(code)
          return wx.reLaunch({
            url: `/packages-user/pages/applyFail/applyFail`
          })
        }
      }
    }

    return wx.navigateTo({
      url: `/packages-user/pages/register/${ROLE_REGISTER[type]}/index?${qs.stringify(roleInfo)}&${qs.stringify(roleParams)}`
    })
  },

  // 切换方向
  async onChooseRoleForChange(params = {}) {
    const { mark, type } = params

    let cParams = {
      unionid: getUnionID(),
      openid: getOpenId(),
      phone: getUserPhone(),
      customerType: mark
    }

    vLog.info('ROLE checkLogin cParams >>>', JSON.stringify(cParams)).report()

    const { code: wxcode } = await wx.login()
    const { msg, success, data, code } = await checkLogin({ code: wxcode, customerType: cParams.customerType, wechatCode: global.SOURCE_CODE })
      .catch((err) => {
        vLog.error('ROLE checkLogin catch err >>>', err).report()
      })
    vLog.log('ROLE checkLogin msg, success, data, code >>>>>', msg, success, data, code)

    let rolePath = '/packages-user/pages/register/agency/index'
    if (type === 'CHANNEL') {
      rolePath = '/packages-user/pages/register/channel/index'
    }
    if (type === 'CUSTOMER') {
      rolePath = '/packages-user/pages/register/customer/index'
    }

    // 注册方向
    if (!success || !data) {
      switch (code) {
        case LoginState.ABSENCE:
        case LoginState.MULTIPLE_CUSTOMER:
        case LoginState.REGISTER: {
          interaction.showToast(msg || "")
          setTimeout(() => {
            return wx.navigateTo({
              url: `${rolePath}?${qs.stringify(params)}`
            })
          }, 1000)
          return
        }

        // 账号审核中，请稍后登录 => 跳转审核中页面
        case LoginState.INREVIEW: {
          setUserLogin(false)
          setUserRole(code)
          let url = encodeURIComponent(`${wbs.gfH5}/share/advInviteRegister?result=${LoginState.INREVIEW}`)
          return wx.reLaunch({
            url: `/pages/common/webview/webPage?url=${url}&hideHomeNav=1&banShare=1`
          })
        }

        //禁用 => 跳转禁用页面
        case LoginState.FORBIDDEN: {
          setUserLogin(false)
          setUserRole(code)
          return wx.reLaunch({
            url: `/packages-user/pages/applyFail/applyFail`
          })
        }
        default:
          break
      }
    }
    const { token = '', userId } = data || {}
    if (token) { setToken(token) }
    if (userId) { setUserId(userId) }

    let wInfo = getUser()
    wInfo = {
      ...wInfo,
      ...cParams,
      ...data,
      wechatInfoId: getWechatInfoId()
    }
    // 已存在用户 切换身份
    setUser(wInfo)
    setCustomerTypeInt(mark)
    const { executed } = await shapeShift(ROLE_TYPE_IMAGE[mark] || 'CUSTOMER', true)
    if (executed === 'DONE') {
      return wx.reLaunch({
        url: `/${GLOBAL_START_PATH}`,
        complete: () => {
          getApp().globalData.refersMark = true
          this.setData({ showLoading: false })
        }
      })
    }
  }
})

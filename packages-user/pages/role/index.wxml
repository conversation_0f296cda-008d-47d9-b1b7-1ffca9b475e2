<!--pages/loginAndRegist/applyFail/applyFail.wxml-->
<nb-page
    navShowBack="{{!singlePage}}"
    showHomeIcon="{{singlePage}}"
    contentUseView="{{false}}"
    navTitle="{{title||'角色'}}"
    inHomePage="{{false}}"
    showTabBar="{{false}}">
  <tdcaptcha id="td-captcha-role" />
  <view class="role-container">
    <view wx:if="{{isChange}}" class="role-curr-block">
      <image src="{{currRoleInfo.icon}}" mode="aspectFill" class="role-header"/>
      <view class="role-tips-txt">{{currRoleInfo.tips || ''}}</view>
    </view>

    <view wx:else class="role-curr-block">
      <image src="{{sysInfo.staff.clientParams.roleConfig.backgroundUrl}}" mode="heightFix" class="bg-tips"/>
    </view>

    <view
        class="role-block"
        wx:for="{{list}}"
        wx:key="idx"
        wx:for-index="idx"
        wx:for-item="item">
      <image
          src="{{item.icon}}"
          mode="heightFix"
          class="role-item"
          data-item="{{item}}"
          bind:tap="onRoleAction"
      />
    </view>
  </view>

  <view class="loading-block" wx:if="{{showLoading}}">
    <van-loading type="spinner" size="40px" color="#FFF"/>
    <view class="loading-tips">{{'切换中...'}}</view>
  </view>
</nb-page>

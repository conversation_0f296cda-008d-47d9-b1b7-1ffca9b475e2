<nb-page
    showNavBar="{{true}}"
    navShowBack="{{true}}"
    navTitle="{{title}}"
    showTabBar="{{false}}">
  <view class='background'>
    <image class="bgImg" src="{{loginImg}}?x-oss-process=image/resize,m_fill,h_1460,w_828/format,jpg"/>
  </view>

  <nb-card card-class="card-container">
    <van-cell-group custom-class="cell" border="{{false}}">
      <van-field
          type="number"
          label=""
          placeholder="请使用员工手机号登录"
          maxlength="11"
          clearable
          value="{{phone}}"
          bind:input="SMSinputPhone"
      />
      <van-field
          label=""
          placeholder="请输入短信验证码"
          maxlength="6"
          clearable
          use-button-slot
          bind:input="SMSinputCode"
          bind:blur="SMSinputCodeBlur"
          border="{{ false }}">
        <view
            slot="button"
            bindtap='SMSsendCode'
            style='color:{{sendLoading ? "#969696" : $state.themeColor}}'>
          {{verifyCode}}
        </view>
      </van-field>
    </van-cell-group>

    <button class='nb-btn block btn'
            disabled='{{disabled}}'
            form-type="submit"
            style='background-color:{{$state.themeColor}}'
            bind:tap="doEmployeeStep1">
      {{btnCode}}
    </button>
  </nb-card>

  <van-toast id="van-toast"/>
</nb-page>

import {
  global,
  storage,
  interaction,
  countDown,
  validator,
  util,
  wbs,
  eventName,
  vLog
} from '../../../common/index.js'
import {employeeLogin, employeeSMSCode} from "../../../common/nb/home";
import {getSystemInfo, setPreviousPath} from "../../../common/utils/userStorage";

const {
  count_down,
  clear_count
} = countDown

const {
  isEmptyObject,
} = util

const {
  SEND_WEBVIEW_OPTIONS
} = eventName

Page({
  data: {
    title: '员工登录',
    loginImg: "",
    phone: '',
    SMScode: '',

    sendLoading: false,
    verifyCode: '获取验证码',
    btnCode: '立即登录使用',
    disabled: true,

    showModal: false,
    currPagePath: '',
  },
  onLoad(options = {}) {
    vLog.log('LOGIN_EMPLOYEES options >>>', options)
    let {currPagePath = ''} = options
    currPagePath = decodeURIComponent(currPagePath)
    this.setData({
      currPagePath
    }, () => this.init())
  },

  async init() {
    const sysInfo = getSystemInfo()
    vLog.log('LOGIN_EMPLOYEES sysInfo >>>', sysInfo)
    if (!isEmptyObject(sysInfo)){
      this.setData({
        loginImg: sysInfo?.staff?.loginImg || ''
      })
    }
  },

  async doEmployeeStep1(event = {}) {
    vLog.log('LOGIN_EMPLOYEES event >>>', event)
    const {phone, SMScode, currPagePath} = this.data
    const params = {
      phone,
      code: SMScode
    }
    if (!validator.isMobile(phone)){
      return interaction.showToast('手机号码格式不正确')
    }
    if (SMScode.length !== 6){
      return interaction.showToast('验证码长度有误，请重试')
    }

    interaction.showLoading('登录中...')
    const {success, code, msg, data} = await employeeLogin(params)
    interaction.hideLoading()
    vLog.log('LOGIN_EMPLOYEES success, code, msg, data >>', success, code, msg, data)
    if (success && code == 0){
      if (Object.keys(data).length){
        storage.setStorage(global.STORAGE_GLOBAL_EMPLOYEE_INFO, data)
        const {token} = data || {}

        let params = {
          perfix: `${wbs.gfH5}/share/wxStaffData`,
          title: 'Bi看板',
          token,
          pageType: 'AppAdvBiBlock',
          banShare: 0,
        }

        setPreviousPath(currPagePath)
        return wx.navigateTo({
          url: "/pages/common/webview/webPage",
          success(res) {
            res.eventChannel.emit(SEND_WEBVIEW_OPTIONS, params)
          }
        });
      }
    }

    return interaction.showToast(msg || '')
  },

  //输入手机号
  SMSinputPhone(e = {}) {
    this.setData({
      phone: e?.detail || ''
    }, () => this.SMScheckAllIsWell());
  },

  // 输入验证码
  SMSinputCode(e = {}) {
    this.setData({
      SMScode: e?.detail || ''
    }, () => this.SMScheckAllIsWell());
  },

  //验证码聚焦
  SMSinputCodeBlur(e = {}) {
    this.setData({
      SMScode: e?.detail?.value || ''
    }, () => this.SMScheckAllIsWell());
  },

  //登录按钮是否可以点击
  SMScheckAllIsWell() {
    const {phone = '', SMScode = ''} = this.data
    if (phone !== '' && phone.length === 11 && SMScode !== '' && SMScode.length >= 4){
      this.setData({disabled: false});
    } else {
      this.setData({disabled: true});
    }
  },

  // 发送验证码判断
  SMSsendCode() {
    const {sendLoading, phone = ''} = this.data
    if (sendLoading)
      return

    if (phone === ''){
      return interaction.showToast('输入手机号码')
    }

    if (!validator.isMobile(phone)){
      return interaction.showToast('手机号码格式不正确')
    }

    let total_micro_second = 60;
    //验证码倒计时
    this.setData({
      sendLoading: true
    })
    this.requestCode()
    count_down(this, total_micro_second);
  },

  //获取验证码
  async requestCode() {
    const {phone = ''} = this.data
    const {success, msg, data, code} = await employeeSMSCode({phone})
    vLog.log('LOGIN_EMPLOYEES requestCode  success, msg, data, code >>', success, msg, data, code)

    if (success){
      return interaction.showToast('发送验证码成功')
    } else {
      clear_count(this);
      return interaction.showToast(msg)
    }
  }
});

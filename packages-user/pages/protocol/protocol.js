import {util} from '../../../common/index.js'
import {getUrl2HtmlInfo} from '../../../common/nb/home'
import {getSystemInfo} from "../../../common/utils/userStorage";

const {isEmptyObject} = util

Page({
  /**
   * 页面的初始数据
   */
  data: {
    name: '',
    richText: '',
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options = {}) {
    const {name = ''} = options
    this.setData({
      name
    })
    this.getData()
  },

  onShow() {
    wx.hideShareMenu({
      menus: ['shareAppMessage', 'shareTimeline']
    })
  },

  async getData() {
    wx.showLoading({
      title: '加载中...',
    })

    let richText = ''
    const sysInfo = getSystemInfo()
    if (!isEmptyObject(sysInfo)){
      const {
        staff: {
          agreementSuccessEditor = ''
        }
      } = sysInfo || {}
      richText = await getUrl2HtmlInfo({url: agreementSuccessEditor})
    }
    wx.hideLoading()

    this.setData({
      richText
    })
  }
})

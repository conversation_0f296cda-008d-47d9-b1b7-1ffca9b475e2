<nb-page
    navShowBack="{{!singleInviterPath}}"
    showHomeIcon="{{!!singleInviterPath}}"
    contentUseView="{{true}}"
    navTitle="{{faName}}邀您注册"
    inHomePage="{{false}}"
    hasPageContainer="{{true}}"
    showTabBar="{{false}}">
  <tdcaptcha id="td-captcha-agency-register" />
  <scroll-view
      class="reg-scroll-container"
      style="position:fixed;width:100%;left:0;height:100vh;"
      enable-flex="{{true}}"
      enable-passive="{{true}}"
      scroll-with-animation
      enable-back-to-top
      scroll-y="true">
    <image
        mode="widthFix"
        class="bgImg"
        src="{{registerImg}}?x-oss-process=image/resize,m_fill,h_730,w_414/format,jpg"
    />

    <nb-card card-class="card-container">
      <van-cell-group custom-class="cell" border="{{false}}">
        <van-field
            wx:if="{{!isChangeReg}}"
            type="text"
            label=""
            required
            data-type="name"
            placeholder="请输入您的真实姓名"
            clearable
            value="{{userName}}"
            bind:input="regInputEvent"
        />

        <van-cell
            class="input-cell"
            required
            title="{{channelName}}"
            is-link
            bind:tap="regChooseChannel"
        />

        <van-field
            type="text"
            label=""
            required
            data-type="department"
            placeholder="请输入您所在部门"
            clearable
            value="{{department}}"
            bind:input="regInputEvent"
        />

        <view
            class="input-config-list"
            wx:if="{{cList.length && regModel==='WHITE_LIST'}}"
            wx:for="{{cList}}"
            wx:key="index"
            wx:for-item="item"
            wx:for-index="index"
            data-item="{{item}}">
          <van-field
              type="text"
              label=""
              required
              style="width: 100%"
              data-type="{{item.type}}"
              placeholder="{{item.placeholder}}"
              clearable
              value="{{cList[index].value}}"
              bind:input="regInputEvent"
          />
        </view>

        <van-field
            type="text"
            label=""
            data-type="posts"
            placeholder="请输入您的职位"
            clearable
            value="{{posts}}"
            bind:input="regInputEvent"
        />
      </van-cell-group>

      <block>
        <view
            wx:if="{{!doGetUserInfo}}"
            class="reg-submit-btn"
            style="background-color: {{$state.themeColor}};opacity: {{!loseWay?1:0.65}}"
            bind:tap="onRegSubmitVerify">
          {{userAvailable ? '提交' : '注册'}}
        </view>

        <button
            wx:else
            class='nb-btn block btn reg-submit-btn'
            disabled="{{!canRegSubmit}}"
            style='background-color:{{$state.themeColor}};opacity: {{!loseWay?1:0.65}}'
            bindtap='getUserProfile'>
          {{userAvailable ? '提交' : '注册'}}
        </button>
      </block>


      <view class="reg-bottom-tips" style="color: {{$state.themeColor}}">
        {{'请您填写真实信息，将有专人核实身份'}}
      </view>

      <view class="reg-bottom-logo">
        <image src="../../../../components/nb-page/images/<EMAIL>" mode="heightFix" class="bottom-logo"/>
      </view>
    </nb-card>

    <!-- <view class="reg-footer-card"/> -->
  </scroll-view>

  <page-container
      show="{{showChannel}}"
      round="{{false}}"
      overlay
      class="channel-block"
      close-on-slide-down="{{false}}"
      bind:afterleave="onAfterLeave"
      bind:clickoverlay="onClickOverlay"
      custom-style="height:65%"
      overlay-style="background-color: rgba(0, 0, 0, 0.75)">
    <view class="channel-title-bar">
      <view class="tips-cancel" bind:tap="onCancel">
        {{'取消'}}
      </view>
      <view class="tips-title">
        {{'请选择机构'}}
      </view>
      <view
          class="tips-confirm"
          style="color: {{canConfirm ? $state.themeColor : '#969696'}}"
          bind:tap="onConfirm">
        {{'确认'}}
      </view>
    </view>

    <view class="channel-search-bar" style="height: {{searchBarSize}}px">
      <van-search
          style="display:flex;flex-direction:column;justify-content:center;width: 100%;height: {{searchBarSize}}px;"
          placeholder="请输入机构名称"
          value="{{channelKey}}"
          input-align="left"
          marginTop="0"
          maxlength="10"
          background="#f2f2f2"
          bind:search="onCSearch"
          bind:change="onCChange"
          bind:clear="onCClear"
      />
      <view
          slot="action"
          style="min-width: 12vw;color: {{channelKey.length<2?'#969696':'#333'}}"
          bind:tap="onCSearch">
        搜索
      </view>
    </view>
    <scroll-view
        class="channel-list-block"
        style="height: {{containerHeight}}px"
        enable-flex="{{true}}"
        enable-passive="{{true}}"
        lower-threshold="{{50}}"
        scroll-top="{{scrollTop}}"
        bind:scrolltolower="onHandleLoadMore"
        scroll-y="true">
      <radio-group bind:change="radioChange">
        <label
            class="list-label"
            wx:for="{{currList}}"
            wx:for-index="index"
            wx:key="agencyId">
          <view class="channel-item">
            <radio
                value="{{item.agencyId}}"
                color="{{$state.themeColor}}"
            />
          </view>
          <view class="channel-name-tips">
            {{item.name}}
          </view>
        </label>
      </radio-group>

      <view wx:if="{{loadingMore}}" class="channel-load-more">
        <image src="../../../../imgs/gif/loading.gif" class="load-more-gif"/>
        <view class="load-more-tips">{{'正在加载更多...'}}</view>
      </view>

      <view wx:if="{{!currList.length && doSearch}}" class="add-channel-block">
        <image src="../../../../imgs/empty/<EMAIL>" mode="aspectFill" class="pic-channel-empty"/>
        <view class="add-channel-title-tips">
          {{'暂无您的公司名称'}}
        </view>

        <view class="add-channel-content-tips">
          {{'请联系您的专属机构销售处理'}}
        </view>
      </view>
    </scroll-view>
  </page-container>

  <van-toast id="van-toast"/>
</nb-page>

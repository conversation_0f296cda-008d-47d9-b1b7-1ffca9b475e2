.background{
    width: 100%;
    height: 100%;
}

.bgImg{
    width: 100%;
}

.card-container{
    display: flex;
    flex-direction: column;
    background-color: #fff;
    margin-top: 10%;
    width: 96%;

    margin-left: 2%;
    border-radius: 8rpx;
    padding: 48rpx 0 0 0;
}

.cell-avatar-btn{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-left: 14px;
    padding: 10rpx 6rpx;
    border-bottom: 1px solid #eee;
}

.cell-avatar-tips{
    font-size: 14Px;
    color: #969696;
    font-family: UICTFontTextStyleBody;
}

.has-choose{
    color: #333;
}

.reg-avatar-btn{
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 75%;
    justify-content: flex-end;
    border: none;
    border-radius: 0;
    padding: 0;
    /*background-color: blueviolet;*/
    background-color: #fff;
    font-size: 0;
}

.reg-avatar{
    width: 72rpx;
    height: 72rpx;
    border-radius: 36rpx;
    margin-right: 10px;
}

.reg-arrow-icon{
    margin-bottom: -4rpx;
    margin-right: 32rpx;
}

.input-cell{
    font-size: 14px;
    line-height: 20px;
    padding: 20px 0 10px;

}

.channel-block{
    display: flex;
    flex-direction: column;
    flex: 1;
}

.channel-title-bar{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 28rpx;
    border: 1rpx solid #eee;
    height: 45px;
}

.tips-cancel{
    font-size: 14Px;
    color: #696969;
    padding-right: 30px;
}

.tips-title{
    font-size: 16Px;
    color: #333;
    font-weight: bold;
}

.tips-confirm{
    font-size: 14Px;
    color: #969696;
    padding-left: 30px;
}

.nav{
    color: #999;
    font-size: 32rpx;
    background-color: #fff;
    white-space: nowrap;
    display: flex;
    flex-direction: row;
    border-bottom: 1px solid #eee;
    border-top-left-radius: 10rpx;
    border-top-right-radius: 10rpx;
}

.nav .current-item{
    display: flex;
    flex: 1;
    flex-direction: column;
    height: 100rpx;
    justify-content: center;
    align-items: center;
    position: relative;
}

.nav .current-item .underline{
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 50rpx;
    height: 4rpx;
}

.text-black{
    font-size: 14Px;
    color: #333;
}

.channel-container{
    padding-bottom: 10vh;
}

.channel-list-block{
    display: flex;
    flex-direction: column;
    flex: 1;
    padding: 0 28rpx 20px 28rpx;
    background-color: #fff;
}

.reg-scroll-container{
    background-color: #fff;
    /*background-color: rgba(0, 0, 0, 0.35);*/
}

.list-label{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    /*background-color: rgba(0,0,0,0.35);*/
    padding: 15rpx 0;
    border-bottom: 2rpx solid #eee;
}

.channel-item{
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 10rpx;
}

.channel-name-tips{
    font-size: 14Px;
    color: #434343;
}

radio-group text{
    font-size: 28rpx;
    color: #434343;
}

radio .wx-radio-input{
    border-radius: 50%; /* 圆角 */
    width: 30rpx;
    border: 1rpx solid #5e5e5e;
    height: 30rpx;
}

radio .wx-radio-input.wx-radio-input-checked{
    /*border: none;*/
    width: 30rpx; /* 选中后对勾大小，不要超过背景的尺寸 */
    height: 30rpx; /* 选中后对勾大小，不要超过背景的尺寸 */
    border-radius: 50%; /* 圆角 */
    border: 1rpx solid #5e5e5e;
}

radio .wx-radio-input.wx-radio-input-checked::before{
    border-radius: 50%; /* 圆角 */
    width: 28rpx; /* 选中后对勾大小，不要超过背景的尺寸 */
    height: 28rpx; /* 选中后对勾大小，不要超过背景的尺寸 */
    line-height: 28rpx;
    text-align: center;
    font-size: 28rpx; /* 对勾大小 30rpx */
    color: #fff; /* 对勾颜色 白色 */
    transform: translate(-50%, -50%) scale(1);
}

.channel-search-bar{
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100vw;
    background-color: #f2f2f2;
}

.add-channel-block{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    padding-bottom: 15vh;
    /*background-color: rgba(0, 0, 0, 0.35);*/
}

.add-channel-title-tips{
    font-size: 14Px;
    color: #E8340F;
    margin-top: 10rpx;
}

.add-channel-content-tips{
    font-size: 14Px;
    color: #333;
    margin-top: 20rpx;
}

.pic-channel-empty{
    width: 100px;
    height: 100px;
}

.channel-load-more{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 20px 0;
}

.load-more-gif{
    width: 20px;
    height: 20px;
    margin-right: 10rpx;
}

.load-more-tips{
    font-size: 14Px;
    color: #696969;
}

.reg-submit-btn{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 16rpx;
    margin: 40px 14px 14px 14px;
    font-size: 14Px;
    color: #fff;
    border-radius: 30px;
}

.input-config-list{
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    width: 100%;
}

.reg-bottom-tips{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    align-self: center;
    font-size: 12Px;
}

.reg-bottom-logo{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.reg-footer-card{
    display: flex;
    width: 100%;
    height: 30vh;
    background-color: #fff;
}

.bottom-logo{
    margin-top: 40px;
    max-height: 36px;
    height: 36px;
}

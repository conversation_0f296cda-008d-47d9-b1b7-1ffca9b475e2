import {
  getSystemInfo,
  getUser,
  setUser,
  getUnionID,
  getOpenId,
  getWechatInfoId,
  setUserRole,
  getUserPhone,
  setSaveUserInfo,
  setCustomerTypeInt,
  setCurrRoleType,
  getTempAgencyAvatar,
  setUserLogin,
  setToken,
  setUserId,
} from "../../../../common/utils/userStorage";

import {
  interaction,
  qs,
  util,
  enums,
  global,
  breakIn,
  wbs,
  vLog,
} from "../../../../common/index.js";

import {
  getContentHeight,
  screenHeightPx,
} from "../../../../common/const/systeminfo";

import {
  doAgencyRegister,
  getAgencyList,
  getUnionId,
  setIdExchange,
  activityGetActivity
} from "../../../../common/nb/home";

const {
  LoginState,
  REGISTER_TYPE,
  ROLE_TYPE,
  AVATAR_URL_DEFAULT,
  REGISTER_MODEL,
  LOSE_TYPE_TOAST_MSG,
  PARAMS_EXCHANGE_FILTER_KEYS,
  REGISTER_MODEL_LIST,
  EnterSource,
} = enums

const {
  rpx2px,
  isEmptyObject,
  parseOptions2Object,
  panTransformDCode2Object,
} = util

const TAB_BAR_SIZE = 45
const SEARCH_BAR_HEIGHT = 50
const FLOAT_PAGE_HEIGHT = 0.65
const CONTAINER_SIZE = Math.floor(Number(screenHeightPx * FLOAT_PAGE_HEIGHT).toFixed(2) * 1 - TAB_BAR_SIZE)

const fpPlugin = requirePlugin('tdfp-plugin')
const app = getApp()

Page({
  data: {
    sysInfo: {},
    registerImg: '',
    faName: '广发木棉花',
    userAvailable: false,
    userName: '',
    department: '',
    posts: '',
    channelName: '请选择您的机构名称',
    cList: [],
    showChannel: false,
    channelKey: '',
    regModel: REGISTER_MODEL[3],
    canRegSubmit: false,
    loseWay: 1,
    scrollTop: 0,
    isLastPage: false,
    loadingMore: false,
    doSearch: false,

    doGetUserInfo: true,
    isChangeReg: false,
    singleInviterPath: '',
    fromRegister: '',
    currList: [],
    canConfirm: false,
    searchBarSize: SEARCH_BAR_HEIGHT,
    containerHeight: CONTAINER_SIZE - SEARCH_BAR_HEIGHT,
    contentHeight: rpx2px(getContentHeight(true, false, false))
  },

  _data: {
    registerInfo: {},
    blackBox: '',
    captchaToken: '',
    totalElements: 0,
    totalPages: 0,
    pageNo: 0,
    currAvatarUrl: AVATAR_URL_DEFAULT,
    holdType: '',
    fromHoldUp: false,
    hasChooseAvatar: false,
    regFrom: '',
    selectedName: '',
    agencyId: '',
    code: '',
  },

  onLoad(options = {}) {
    vLog.info('AGENCY_REGISTER options >>>>', qs.parse(options)).report()

    let isChangeReg = false
    let fromHoldUp = false
    let faName = '广发木棉花'
    let fragment = new fpPlugin.FMAgent(app.globalData._fmOpt)

    let registerInfo = parseOptions2Object(options)
    vLog.log('AGENCY_REGISTER registerInfo >>>>', registerInfo)
    let {
      fromRegister = '',
      fromHoldUp: fHoldUp = '',
      regFrom = '',
      registerType = '',
      singleInviterPath = '',
      inviterName = '',
      holdType = '',
      signinTypeStr = ''
    } = registerInfo

    if (inviterName) {
      faName = inviterName
    }

    if (fHoldUp) {
      fromHoldUp = true
    }

    if (fromRegister || fHoldUp) {
      let fRes = panTransformDCode2Object(registerInfo)
      vLog.log('AGENCY_REGISTER fRes >>>>', fRes)
      registerInfo = { ...fRes }
    }

    if (registerType === REGISTER_TYPE.CHANGE) {
      isChangeReg = true
      registerInfo = {
        ...registerInfo,
        openid: getOpenId(),
        unionid: getUnionID(),
        wechatInfoId: getWechatInfoId(),
        phone: getUserPhone(),
        faId: '',
      }
    }

    const hasWInfoId = !!getWechatInfoId()
    vLog.log(' AGENCY_REGISTER registerInfo,hasWInfoId >>>>', registerInfo)
    if (regFrom === 'START_UP') {
      singleInviterPath = true
    }

    this._data = {
      ...this._data,
      fromHoldUp,
      holdType,
      registerInfo,
      regFrom,
    }
    this.setData({
      faName,
      isChangeReg,
      signinTypeStr,
      singleInviterPath,
      doGetUserInfo: !hasWInfoId,
    })
    this.initSysInfo()
    if (fragment) {
      this.initBlackbox(fragment)
    }
  },

  /**
   * 用户信息
   */
  initSysInfo() {
    const sysInfo = getSystemInfo()
    const {
      agency: {
        registerImg = '',
        newUser,
        whiteConfig: {
          element: wCList = []
        }
      }
    } = sysInfo

    const userAvailable = REGISTER_MODEL_LIST.includes(newUser * 1)
    const regModel = REGISTER_MODEL[newUser * 1]
    const _wCList = [].concat(wCList)

    let _cList = []
    _wCList.forEach((wItem, wIndex) => {
      const params = {
        type: wItem,
        index: wIndex,
        placeholder: wItem,
        value: ''
      }
      _cList.push(params)
    })

    this.setData({
      sysInfo,
      registerImg,
      userAvailable,
      regModel,
      cList: _cList
    })
  },

  initBlackbox(fragment) {
    let that = this
    fragment && fragment.getInfo({
      page: that,
      mode: 'plugin',
      noClipboard: true,
      openid: getOpenId(),
      unionid: getUnionID(),
      success: function (blackBox) {
        that._data['blackBox'] = blackBox
      },
      fail: function (err) {
        vLog.error('AGENCY_REGISTER initBlackbox failed >>>', err).report()
      }
    })
  },

  onShow() {
    wx.hideShareMenu({
      menus: ['shareAppMessage', 'shareTimeline']
    })
  },

  /**
   * 信息输入
   */
  regInputEvent(e = {}) {
    const { cList = [] } = this.data
    const {
      detail = '',
      currentTarget: {
        dataset: {
          type = ''
        }
      }
    } = e

    switch (type) {
      case 'name':
        this.setData({ userName: detail }, () => this.doCheckRegisterSubmit())
        break

      case 'department':
        this.setData({ department: detail }, () => this.doCheckRegisterSubmit())
        break

      case 'posts':
        this.setData({ posts: detail }, () => this.doCheckRegisterSubmit())
        break

      default:
        break
    }

    let _cList = []
    if (cList.length) {
      cList.forEach((cItem) => {
        const { type: cType } = cItem
        let items = {
          ...cItem,
        }
        if (type == cType) {
          items['value'] = detail
        }
        _cList.push(items)
      })

      vLog.log('AGENCY_REGISTER regInputEvent _cList >>>>', _cList)
      this.setData({
        cList: _cList
      }, () => this.doCheckRegisterSubmit())
    }
  },

  /**
   * 选择头像
   */
  async onChooseAvatar(e = {}) {
    vLog.log('AGENCY_REGISTER onChooseAvatar e >>', e)
    const { avatarUrl = '' } = e.detail

    await breakIn({ name: 'uploadImage', file: avatarUrl, role: 'AGENCY' })
    this._data = {
      ...this._data,
      currAvatarUrl: avatarUrl,
      hasChooseAvatar: true
    }
  },

  /**
   * 取消选择
   */
  regChooseChannel() {
    this.setData({
      showChannel: true,
      channelName: '请选择您的机构名称',
    })
  },

  onSetShowStatus(showChannel = false) {
    this.setData({ showChannel })
  },

  // ================================= 无痕验证 =================================//
  onTryToVerify() {
    // 获取验证码插件实例，#id与wxml文件中组件节点id一致
    const that = this
    const td = this.selectComponent('#td-captcha-agency-register');

    // 调用API，触发验证码弹出
    td.captcha.triggerCaptcha({
      partnerCode: 'gffunds', // 同盾合作方
      appName: 'gffund_xcx', // 同盾合作方应用
      env: 1, // 1-线上环境 0-测试环境
      blackbox: that._data.blackBox || "",  // 设备指纹blackbox，非必填
      onSuccess: that.onTryToVerifySuccess, // 自定义验证成功回调
      onFail: that.onTryToVerifyFail, // 自定义验证失败回调，非必填
      onClose: that.onTryToVerifyClose, // 自定义验证码关闭回调，非必填
      lang: 1, // 语言配置项,必填
      maskClose: 0, // 蒙层是否可以关闭
    });
  },

  onTryToVerifySuccess: function (captchaToken = '') {
    const that = this
    interaction.showLoading('加载中...')
    that._data['captchaToken'] = captchaToken
    return that.onAgencyRegisterSubmit()
  },

  onTryToVerifyFail: function (msg = '') {
    if (msg !== 'opFail') {
      interaction.showToast(msg)
    }
  },

  onTryToVerifyClose() {
    vLog.info('AGENCY_REGISTER onTryToVerifyClose').report()
  },
  // ================================= 无痕验证 =================================//

  onRegSubmitVerify() {
    return this.onTryToVerify()
  },

  /**
   * 提交注册
   */
  async onAgencyRegisterSubmit() {
    vLog.log('AGENCY_REGISTER onAgencyRegisterSubmit this.data >>>', this.data)
    const {
      canRegSubmit,
      loseWay,
      isChangeReg,
      userName,
      posts,
      department,
      regModel,
      userAvailable,
      channelName: orgName = '',
      cList = [],
      sysInfo = {},
      signinTypeStr
    } = this.data

    const {
      currAvatarUrl,
      fromHoldUp,
      holdType,
      blackBox = '',
      captchaToken = '',
      registerInfo = {},
      code: orgCode = '',
    } = this._data

    if (!canRegSubmit) {
      return interaction.showToast(LOSE_TYPE_TOAST_MSG[loseWay])
    }
    interaction.showLoading('信息提交中...')

    let {
      phone,
      code,
      openid,
      wechatInfoId,
      unionid,
      faId,
      toTargetPathParams = {},
      type = '',
      mark = '',
      fromPage = ''
    } = registerInfo
    if (!phone) {
      phone = getUserPhone()
    }
    let { avatarUrl = '' } = getUser()
    if (!unionid) {
      unionid = getUnionID()
    }
    if (!openid) {
      openid = getOpenId()
    }
    if (!wechatInfoId) {
      wechatInfoId = getWechatInfoId()
    }
    if (getTempAgencyAvatar()) {
      avatarUrl = getTempAgencyAvatar()
    }
    if (!avatarUrl) {
      avatarUrl = AVATAR_URL_DEFAULT
    }

    let params = {
      openid,
      unionid,
      wechatInfoId,
      phone,
      code,
      blackBox,
      captchaToken,
      orgCode,
      orgName,
      department,
      posts,
      avatar: avatarUrl || currAvatarUrl,
      inviterStaffId: faId,
      name: userName,
    }

    const { id = '' } = await activityGetActivity()
    if (toTargetPathParams?.shareFrom === EnterSource.ACTIVITY || fromPage === "LOTTERY") {
      params['utmJson'] = JSON.stringify({ utm_source: 'wechat', utm_medium: "活动单页", utm_campaign: '注册有礼', utm_term: id, utm_content: toTargetPathParams?.targetPath || 'advLotteryActivityPage' })
    }

    getApp().sensors.track('signup', {
      signin_type: faId ? signinTypeStr ? signinTypeStr : '分享邀请' : '主动',
      invitor_unionid: faId
    })

    if (!isEmptyObject(toTargetPathParams)) {
      const { faId: _faId = '' } = toTargetPathParams
      if (!params.inviterStaffId) {
        params['inviterStaffId'] = _faId
      }
    }

    if (!userAvailable) {
      delete params.name
      delete params.jobTitle
    }

    let inviterInfo = []
    if (cList.length) {
      cList.forEach((lItem) => {
        const { value = '' } = lItem
        inviterInfo.push(value)
      })

      inviterInfo = inviterInfo.join('-')
      params['inviterInfo'] = inviterInfo
    }

    if (regModel === REGISTER_MODEL["5"]) {
      params['inviterInfo'] = department
    }

    vLog.info('AGENCY_REGISTER onAgencyRegisterSubmit params >>>>', params)
    let _regRes = {}

    vLog.info('AGENCY_REGISTER (isChangeReg || fromHoldUp), !holdType >>>>', (isChangeReg || fromHoldUp), !holdType).report()
    if ((isChangeReg || fromHoldUp) && !holdType) {
      let changeParams = {}
      for (const item of Object.entries(params)) {
        let [key, value] = item
        if (!PARAMS_EXCHANGE_FILTER_KEYS.includes(`${key}`) && `${value}`) {
          if (`${key}` === 'posts') {
            key = 'jobTitle'
          }
          changeParams[key] = value
        }
      }
      changeParams['type'] = Number(mark)
      vLog.info(' AGENCY_REGISTER onAgencyRegisterSubmit  changeParams >>>>', changeParams)
      _regRes = await setIdExchange(changeParams)
    } else {
      vLog.info('AGENCY_REGISTER onAgencyRegisterSubmit  params >>>>', params)
      _regRes = await doAgencyRegister(params)
    }

    let { code: regCode, msg: regMsg = '', data: regData, success: regSucc } = _regRes || {}
    vLog.info('AGENCY_REGISTER onAgencyRegisterSubmit _regRes >>>>', regCode, regMsg, regSucc)

    if (!regSucc) {
      if (regCode === LoginState.WHITE_LIST_PASS || regCode === LoginState.AGENCY_UN_OPEN_REGISTER) {
        if (regModel === REGISTER_MODEL["5"]) {
          return interaction.showToast(sysInfo?.agency?.failTips || regMsg)
        } else {
          return interaction.showToast(sysInfo?.agency?.whiteConfig?.failMsg || regMsg)
        }
      }

      if (regCode === LoginState.INREVIEW) {
        interaction.showToast(regMsg || '账号审核中')
        let url = encodeURIComponent(`${wbs.gfH5}/share/advInviteRegister?result=${LoginState.INREVIEW}`)
        return wx.reLaunch({
          url: `/pages/common/webview/webPage?url=${url}&hideHomeNav=1&banShare=1`,
          complete() {
            setCustomerTypeInt(Number(mark))
            getApp().globalData.customerType = type
            setCurrRoleType(type)
          }
        })
      }

      return interaction.showToast(regMsg || '注册失败')
    }
    interaction.hideLoading()
    vLog.log('AGENCY_REGISTER onAgencyRegisterSubmit regCode,regMsg,regData,regSucc >>>>', regCode, regMsg, regData, regSucc)
    switch (regCode) {
      case LoginState.VERIFY_ERROR:
      case LoginState.CODE_ERROR_01:
      case LoginState.CODE_ERROR_02:
      case LoginState.CODE_ERROR_03: {
        return interaction.showToast(regMsg)
      }

      case LoginState.WHITE_LIST_PASS: {
        if (regModel === REGISTER_MODEL["5"]) {
          return interaction.showToast(sysInfo?.agency?.failTips || regMsg)
        } else {
          return interaction.showToast(sysInfo?.agency?.whiteConfig?.failMsg || regMsg)
        }
      }

      case LoginState.INREVIEW: {
        interaction.showToast(regMsg || '账号审核中')
        let url = encodeURIComponent(`${wbs.gfH5}/share/advInviteRegister?result=${LoginState.INREVIEW}`)
        return wx.reLaunch({
          url: `/pages/common/webview/webPage?url=${url}&hideHomeNav=1&banShare=1`,
          complete() {
            setCustomerTypeInt(Number(mark))
            getApp().globalData.customerType = type
            setCurrRoleType(type)
          }
        })
      }

      case LoginState.SUCCESS: {
        const { token = '', userId, userName } = regData || {}
        setToken(token)
        setUserId(userId)

        let wInfo = getUser()
        wInfo = {
          ...wInfo,
          token,
          userId,
          userName,
        }
        setUser(wInfo)
        setUserRole(regCode)
        setUserLogin(true)
        // 切换用户身份
        if (isChangeReg || holdType) {
          vLog.info('AGENCY_REGISTER isChangeReg,holdType >>>>', isChangeReg, holdType).report()
          return breakIn({ name: 'doReloadPageInfo', type: ROLE_TYPE[type] })
        } else {
          vLog.info('AGENCY_REGISTER Number(mark),type >>>>', Number(mark), type).report()
          // 跳转到对应页面
          setCustomerTypeInt(Number(mark))
          setCurrRoleType(type)
          return breakIn({ name: 'doRouteTargetPage', type, registerInfo: registerInfo })
        }
      }

      default:
        break
    }
  },

  //获取用户信息
  getUserProfile() {
    const { canRegSubmit, loseWay } = this.data
    if (!canRegSubmit) {
      return interaction.showToast(LOSE_TYPE_TOAST_MSG[loseWay] || '')
    }

    let that = this
    wx.showLoading({
      title: '加载中...',
      mask: true
    })
    wx.getUserProfile({
      desc: '用于完善会员资料', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
      success: (res) => {
        return that.onGetUnionId(res)
      },
      complete: () => {
        wx.hideLoading()
      }
    })
  },

  //获取UnionId
  async onGetUnionId(wxInfoRes = {}) {
    vLog.log('AGENCY_REGISTER onGetUnionId wxInfoRes,this.data >>>>', wxInfoRes, this.data)
    const that = this
    if (!isEmptyObject(wxInfoRes)) {
      let wInfo = getUser()
      wInfo = {
        ...wInfo,
        ...wxInfoRes?.userInfo
      }
      vLog.log('AGENCY_REGISTER onGetUnionId wInfo >>>', wInfo)
      setUser(wInfo)
    }

    await wx.login({
      success(res) {
        const { code: rCode } = res || {}
        vLog.log('AGENCY_REGISTER onGetUnionId wx.login >>>', res)
        if (rCode) {
          let params = {
            code: rCode,
            wechatCode: global.SOURCE_CODE,
            ...wxInfoRes,
          }

          vLog.log('AGENCY_REGISTER getUnionId  params >', params)
          //发起网络请求
          getUnionId(params)
            .then(result => {
              const { success, msg = '', param, code } = result || {}
              vLog.log('AGENCY_REGISTER getUnionId >>', success, msg, param, code)
              that.setData({ wxInfo: param })
              if (!success) {
                wx.hideLoading()
                return interaction.showToast(msg)
              } else {
                setSaveUserInfo(param, false)
                return that.onAgencyRegisterSubmit()
              }
            })
            .catch(error => {
              vLog.error('AGENCY_REGISTER getUnionId error >', error).report()
            })
        } else {
          vLog.error('AGENCY_REGISTER 登录失败 ', res && res.errMsg).report()
        }
      }
    })
  },

  /**
   * 检测填写数据是否完整
   */
  doCheckRegisterSubmit() {
    vLog.log('AGENCY_REGISTER doCheckRegisterSubmit this.data >>>', this.data)
    const {
      userName,
      department,
      regModel,
      canRegSubmit,
      cList,
      channelName,
      isChangeReg,
    } = this.data

    let _canRegSubmit = canRegSubmit
    if (!isChangeReg && !userName) {
      _canRegSubmit = false
      this.setData({
        canRegSubmit: _canRegSubmit,
        loseWay: 1
      })
      return
    }

    if (regModel === REGISTER_MODEL["5"] && !department) {
      _canRegSubmit = false
      this.setData({
        canRegSubmit: _canRegSubmit,
        loseWay: 4
      })
      return
    }

    // 白名单都是必填
    if (cList.length && regModel === 'WHITE_LIST') {
      _canRegSubmit = !cList.some(item => item && item.value.toString().trim() === '')
      if (!_canRegSubmit) {
        this.setData({
          canRegSubmit: _canRegSubmit,
          loseWay: 4
        })
        return
      }
    }

    if (!channelName || channelName === '请选择您的机构名称') {
      _canRegSubmit = false
      this.setData({
        canRegSubmit: _canRegSubmit,
        loseWay: 3
      })
      return
    }

    this.setData({
      canRegSubmit: true,
      loseWay: 0
    })
  },

  /**
   * 弹窗蒙层点击
   */
  onClickOverlay() {
    this.onSetShowStatus(false)
  },

  /**
   * 弹窗关闭事件监听
   */
  onAfterLeave(e = {}) {
    vLog.log('AGENCY_REGISTER onAfterLeave e >>>', e)
    this._data['selectedName'] = ''
    this.setData({
      currList: [],
      scrollTop: 0,
      channelKey: '',
      doSearch: false,
      showChannel: false,
      canConfirm: false,
    })
  },

  /**
   * 取消渠道弹窗
   */
  onCancel() {
    this._data = {
      ...this._data,
      selectedName: '',
      agencyId: '',
      code: '',
    }
    this.setData({
      doSearch: false,
      channelName: '请选择您的机构名称',
    }, () => this.onSetShowStatus(false))
  },

  /**
   * 渠道树选择确认
   */
  async onConfirm(e = {}) {
    vLog.log('AGENCY_REGISTER onConfirm e,this.data >>>', e, this.data)
    const { canConfirm } = this.data
    const { selectedName } = this._data
    if (!canConfirm) {
      return interaction.showToast('请选择您的机构名称')
    }

    this.setData({
      doSearch: false,
      channelName: selectedName
    }, () => this.onSetShowStatus(false))
  },

  /**
   * 渠道数据加载更多
   * @param refresh 是否刷新
   */
  onHandleLoadMore(refresh = false) {
    vLog.log('AGENCY_REGISTER onHandleLoadMore refresh,this.data >>>', refresh, this.data)
    this.setData({
      loadingMore: false,
    })
  },

  /**
   * 渠道输入清除
   */
  onCClear() {
    this.setData({
      channelKey: '',
      doSearch: false
    })
  },

  /**
   * 渠道搜索输入
   */
  onCChange(e = {}) {
    this.setData({
      channelKey: `${e?.detail}`.trim()
    })
  },

  /**
   * 渠道搜索
   */
  async onCSearch(e = {}) {
    vLog.log('AGENCY_REGISTER onCSearch e,this.data >>>', e, this.data)
    let { channelKey } = this.data
    channelKey = `${channelKey}`.trim()
    if (channelKey.length < 2) {
      return
    }

    if (!channelKey) {
      this._data['pageNo'] = 0
      this.setData({
        currList: [],
        isLastPage: false,
        scrollTop: 0,
      }, () => this.onHandleLoadMore(true))
      return
    }

    interaction.showLoading('加载中...')
    const { msg, data, success } = await getAgencyList({
      name: channelKey,
      page: 0,
      pageSize: 10
    })
    if (!success) {
      interaction.hideLoading()
      this._data = {
        ...this._data,
        totalElements: 0,
        totalPages: 0,
      }
      this.setData({
        doSearch: true,
        currList: [],
        isLastPage: true,
        scrollTop: 0,
        loadingMore: false,
      })
      return interaction.showToast(msg || '')
    }

    let _currList = []
    const { content = [] } = data || {}
    _currList = [].concat(content)
    interaction.hideLoading()
    this._data = {
      ...this._data,
      totalElements: 42,
      totalPages: 2,
    }

    this.setData({
      doSearch: true,
      currList: _currList,
      isLastPage: true,
      loadingMore: false,
      scrollTop: 0
    })
  },

  /**
   * 选择渠道
   */
  radioChange(e = {}) {
    vLog.log('AGENCY_REGISTER radioChange e,this.data >>>', e, this.data)
    const { currList } = this.data
    const { value = '' } = e.detail

    let _sIndex = 0
    let cTarget = currList.find((sItem, sIndex) => {
      const { agencyId } = sItem
      _sIndex = sIndex
      return agencyId == value
    })

    vLog.log('AGENCY_REGISTER radioChange cTarget >>>', cTarget)
    const { agencyId = '', code = '', name = '' } = cTarget || {}
    this._data = {
      ...this._data,
      pageNo: 0,
      agencyId,
      code,
      selectedName: name
    }

    this.setData({
      loadingMore: false,
      canConfirm: true,
    })
  }
});

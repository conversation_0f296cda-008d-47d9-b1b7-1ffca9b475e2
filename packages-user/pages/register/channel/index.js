import {
  getSystemInfo,
  getUser,
  setUser,
  getUnionID,
  getOpenId,
  getWechatInfoId,
  setUserRole,
  setCurrRoleType,
  setCustomerTypeInt,
  getUserPhone,
  setSaveUserInfo,
  getChannelParamsInfo,
  getChannelNameTips,
  setSystemInfo,
  setUserLogin,
  setToken,
  setUserId,
  getTempAgencyAvatar,
} from "../../../../common/utils/userStorage";

import {
  interaction,
  qs,
  util,
  enums,
  breakIn,
  shapeShift,
  global,
  wbs,
  vLog
} from "../../../../common/index.js";

import {
  getContentHeight,
  screenHeightPx,
} from "../../../../common/const/systeminfo";

import {
  doRegister,
  findAllDisplayNode,
  getUCSystemApiConfig,
  findLastNode,
  getQueryOrgNode,
  getUnionId,
  savePlaceByPoint,
  setIdExchange,
  activityGetActivity
} from "../../../../common/nb/home";

const {
  LoginState,
  AVATAR_URL_DEFAULT,
  REGISTER_TYPE,
  ROLE_TYPE,
  ROLE_TYPE_IMAGE,
  REGISTER_MODEL,
  LOSE_TYPE_TOAST_MSG,
  PARAMS_EXCHANGE_FILTER_KEYS,
  REGISTER_MODEL_LIST,
  EnterSource,
} = enums

const {
  doJSON_PARSE,
  rpx2px,
  isEmptyObject,
  parseOptions2Object,
  panTransformDCode2Object,
} = util

const floatTips = {
  name: '请选择',
  index: 9999
}

const TAB_BAR_SIZE = 45
const CHANNEL_TITLE_BAR_SIZE = 45
const SEARCH_BAR_HEIGHT = 50
const PAGE_SIZE = 20
const FLOAT_PAGE_HEIGHT = 0.65

const { windowWidth } = wx.getSystemInfoSync()
const CONTAINER_SIZE = Math.floor(Number(screenHeightPx * FLOAT_PAGE_HEIGHT).toFixed(2) * 1 - TAB_BAR_SIZE - CHANNEL_TITLE_BAR_SIZE)

const fpPlugin = requirePlugin('tdfp-plugin')
const app = getApp()

Page({
  data: {
    singleInviterPath: '',
    faName: '广发木棉花',
    registerImg: 'https://aim-pic.gffunds.com.cn/image/course/2022-11-18/c3053b98-b8e7-461a-a20d-1a28f3c6fa39.jpg',
    isChangeReg: false,
    userName: '',
    channelName: '请选择渠道',
    regModel: REGISTER_MODEL[3],
    sysInfo: {},
    description: '',
    cList: [],
    userJob: '',
    doGetUserInfo: true,
    loseWay: 1,
    userAvailable: false,
    canRegSubmit: false,
    showChannel: false,
    canConfirm: false,
    active: 0,
    channelTabs: [],
    scrollLeft: 0,
    showSearchBar: false,
    searchBarSize: SEARCH_BAR_HEIGHT,
    containerHeight: CONTAINER_SIZE,
    contentHeight: rpx2px(getContentHeight(true, false, false)),
    channelKey: '',
    scrollTop: 0,
    currList: [],
    hasChecked: false,
    channelInfo: [],
    loadingMore: false,
    isLastPage: false,
    crStart: 1,
    canReChoose: true,
    submitWay: ''
  },
  _data: {
    registerInfo: {},
    orgId: '',
    currAvatarUrl: AVATAR_URL_DEFAULT,
    hasChooseAvatar: false,
    blackBox: '',
    captchaToken: '',
    pageNo: 0,
    totalElements: 0,
    totalPages: 0,
    fromHoldUp: false,
    holdType: '',
    regFrom: '',
    configChannel: [],
    hasNext: false,
    hasAdd: false,
    currACName: "请输入支行/营业部名称",
  },

  onLoad(options = {}) {
    vLog.info('======= REGISTER options >>>>', qs.parse(options)).report()
    let isChangeReg = false
    let fromHoldUp = false
    let faName = '广发木棉花'

    let fragment = new fpPlugin.FMAgent(app.globalData._fmOpt)
    let registerInfo = parseOptions2Object(options)
    vLog.log('REGISTER registerInfo >>>>', registerInfo)
    let {
      fromRegister = '',
      fromHoldUp: fHoldUp = '',
      regFrom = '',
      singleInviterPath = '',
      inviterName = '',
      signinTypeStr = ''
    } = registerInfo
    if (inviterName) {
      faName = inviterName
    }
    if (fHoldUp) {
      fromHoldUp = true
    }

    if (fromRegister || fHoldUp) {
      let fRes = panTransformDCode2Object(registerInfo)
      vLog.log('REGISTER fRes >>', fRes)
      registerInfo = { ...fRes }
    }

    let channelName = '请选择渠道'
    let orgId = ''
    let hasWInfoId = !!getWechatInfoId()
    const { registerType = '' } = registerInfo

    if (registerType === REGISTER_TYPE.CHANGE) {
      isChangeReg = true
      registerInfo = {
        ...registerInfo,
        openid: getOpenId(),
        unionid: getUnionID(),
        wechatInfoId: getWechatInfoId(),
        phone: getUserPhone(),
        faId: '',
      }
    }

    if (regFrom === 'START_UP') {
      singleInviterPath = true
    }

    vLog.info('REGISTER registerInfo >>>>', JSON.stringify(registerInfo))

    this._data = {
      ...this._data,
      registerInfo,
      orgId,
      fromHoldUp,
      regFrom,
    }

    this.setData({
      faName,
      isChangeReg,
      channelName,
      signinTypeStr,
      singleInviterPath,
      doGetUserInfo: !hasWInfoId,
    }, () => {
      return Promise.all([this.getAllDisplayNode(), this.initSysInfo()])
    })

    if (fragment) {
      this.initBlackbox(fragment)
    }
  },

  async getAllDisplayNode() {
    interaction.showLoading('加载中...')
    const { success, data, msg = '', code } = await findAllDisplayNode({})
    interaction.hideLoading()
    vLog.log('REGISTER getAllDisplayNode success, data, msg, code >>>>', success, data, msg, code)

    let channelInfo = []
    if (!success) {
      return interaction.showToast(msg)
    }

    const _data = []
    // 赋值为第一层级
    data.forEach((item) => {
      const _item = {
        ...item,
        level: 0
      }
      _data.push(_item)
    })

    // 过滤
    let currList = _data.filter(item => item && item.status === "PUBLISHED")
    // 排序
    currList = currList.sort(function (a, b) {
      return a.ordinal - b.ordinal
    })

    const lParams = {
      level: 0,
      content: currList,
      totalElements: currList.length,
      selectedId: '',
      selectedIndex: '',
    }

    channelInfo.push(lParams)
    vLog.log('REGISTER getAllDisplayNode currList >>>', currList)
    this._data['configChannel'] = currList
    this.setData({
      currList,
      channelInfo
    })
  },

  async initSysInfo() {
    let sysInfo = getSystemInfo()
    const { registerInfo = {} } = this._data
    const { success, data, code, msg } = await getUCSystemApiConfig({ customerType: `${registerInfo?.type}` })
    vLog.log('REGISTER initSysInfo getUCSystemApiConfig >>>', success, data, code, msg)
    if (success && code === 0) {
      sysInfo = { ...data }
      for (const [key, value] of Object.entries(sysInfo)) {
        if (Array.isArray(value)) {
          sysInfo[key] = [].concat(value)
        } else if (value instanceof Object) {
          sysInfo[key] = { ...value }
        } else {
          sysInfo[key] = doJSON_PARSE(value || '{}' + '')
        }
      }
      setSystemInfo(sysInfo)
    }

    const {
      staff: {
        registerImg = 'https://aim-pic.gffunds.com.cn/image/course/2022-11-18/c3053b98-b8e7-461a-a20d-1a28f3c6fa39.jpg',
        clientParams: {
          channelRemarksStart = 1
        },
        newUser,
        whiteConfig: {
          element: wCList = []
        }
      }
    } = sysInfo

    let userAvailable = REGISTER_MODEL_LIST.includes(newUser * 1)
    let regModel = REGISTER_MODEL[newUser * 1]
    let _wCList = [].concat(wCList)
    let _cList = []
    _wCList.forEach((wItem, wIndex) => {
      const params = {
        type: wItem,
        index: wIndex,
        placeholder: wItem,
        value: ''
      }
      _cList.push(params)
    })

    this.setData({
      sysInfo,
      registerImg,
      crStart: channelRemarksStart,
      userAvailable,
      regModel,
      cList: _cList
    })
  },

  initBlackbox(fragment) {
    let that = this
    fragment && fragment.getInfo({
      page: that,
      mode: 'plugin',
      noClipboard: true,
      openid: getOpenId(),
      unionid: getUnionID(),
      success: function (blackBox) {
        that._data['blackBox'] = blackBox
      },
      fail: function (err) {
        vLog.error('REGISTER initBlackbox failed >>>', err).report()
      }
    })
  },

  onShow() {
    let that = this
    wx.hideShareMenu({
      menus: ['shareAppMessage', 'shareTimeline']
    })
    const _submitWay = app.globalData.submitWay
    vLog.log('REGISTER _submitWay >>>>', _submitWay)
    if (_submitWay) {
      that.setData({
        submitWay: _submitWay,
        canReChoose: false
      })
      that.onSetChannelInfo(_submitWay)
    }
  },

  onUnload() {
    app.globalData.submitWay = ""
  },

  /**
   * 信息输入
   */
  regInputEvent(e = {}) {
    const { cList = [] } = this.data
    const {
      detail = '',
      currentTarget: {
        dataset: {
          type = ''
        }
      }
    } = e

    switch (type) {
      case 'name':
        this.setData({ userName: detail }, () => this.doCheckRegisterSubmit())
        break

      case 'job':
        this.setData({ userJob: detail })
        break

      case 'manager':
        this.setData({ description: detail }, () => this.doCheckRegisterSubmit())
        break

      default:
        break
    }

    let _cList = []
    if (cList.length) {
      cList.forEach((cItem) => {
        const { type: cType } = cItem
        let items = {
          ...cItem
        }
        if (type == cType) {
          items['value'] = detail
        }
        _cList.push(items)
      })

      this.setData({
        cList: _cList
      }, () => this.doCheckRegisterSubmit())
    }
  },

  /**
   * 选择头像
   */
  async onChooseAvatar(e = {}) {
    const { avatarUrl = '' } = e.detail
    await breakIn({ name: 'uploadImage', file: avatarUrl, role: 'CHANNEL' })

    this._data = {
      ...this._data,
      currAvatarUrl: avatarUrl,
      hasChooseAvatar: true
    }
  },

  /**
   * 取消选择
   */
  regChooseChannel(e = {}) {
    vLog.log('REGISTER regChooseChannel e >>>', e)
    this.setData({
      showChannel: true,
      channelName: '请选择渠道',
      channelTabs: [floatTips]
    })
    this._data = {
      ...this._data,
      orgId: '',
      hasAdd: false,
    }
  },

  onSetShowStatus(showChannel = false) {
    this.setData({ showChannel })
  },

  // ================================= 无痕验证 =================================//
  onTryToVerify() {
    // 获取验证码插件实例，#id与wxml文件中组件节点id一致
    const that = this
    const td = this.selectComponent('#td-captcha-register');

    // 调用API，触发验证码弹出
    td.captcha.triggerCaptcha({
      partnerCode: 'gffunds', // 同盾合作方
      appName: 'gffund_xcx', // 同盾合作方应用
      env: 1, // 1-线上环境 0-测试环境
      blackbox: that._data.blackBox || "",  // 设备指纹blackbox，非必填
      onSuccess: that.onTryToVerifySuccess, // 自定义验证成功回调
      onFail: that.onTryToVerifyFail, // 自定义验证失败回调，非必填
      onClose: that.onTryToVerifyClose, // 自定义验证码关闭回调，非必填
      lang: 1, // 语言配置项,必填
      maskClose: 0, // 蒙层是否可以关闭
    });
  },

  onTryToVerifySuccess: function (captchaToken = '') {
    interaction.showLoading('加载中...')
    const that = this

    that._data['captchaToken'] = captchaToken
    return that.onRegisterSubmit()
  },

  onTryToVerifyFail: function (msg) {
    if (msg !== 'opFail') {
      interaction.showToast(msg || '')
    }
  },

  onTryToVerifyClose() {
    vLog.info('REGISTER onTryToVerifyClose').report()
  },
  // ================================= 无痕验证 =================================//

  onRegSubmitVerify() {
    const that = this
    return that.onTryToVerify()
  },

  /**
   * 提交注册
   */
  async onRegisterSubmit() {
    vLog.log('REGISTER onRegisterSubmit this.data >>>', this.data)
    vLog.log('REGISTER onRegisterSubmit this.data >>>', this._data)
    const {
      canRegSubmit,
      loseWay,
      isChangeReg,
      userName,
      userJob,
      description,
      regModel,
      channelName,
      userAvailable,
      submitWay = '',
      cList = [],
      sysInfo = {},
      signinTypeStr,
    } = this.data

    const {
      hasAdd,
      fromHoldUp,
      holdType,
      orgId = '',
      currAvatarUrl = '',
      blackBox = '',
      captchaToken = '',
      registerInfo = {},
    } = this._data
    if (!canRegSubmit) {
      return interaction.showToast(LOSE_TYPE_TOAST_MSG[loseWay])
    }
    let { avatarUrl = '' } = getUser()
    interaction.showLoading('信息提交中...')
    let _additionalInfo = ''
    if (hasAdd) {
      _additionalInfo = channelName.split('-').pop();
      _additionalInfo = `补充:${_additionalInfo}`
    }

    let {
      phone = '',
      code = '',
      openid = '',
      wechatInfoId = '',
      unionid = '',
      faId = '',
      toTargetPathParams = {},
      type = '',
      mark = '',
      fromPage = ''
    } = registerInfo
    if (!phone) {
      phone = getUserPhone()
    }
    if (!openid) {
      openid = getOpenId()
    }
    if (!unionid) {
      unionid = getUnionID()
    }
    if (!wechatInfoId) {
      wechatInfoId = getWechatInfoId()
    }
    if (getTempAgencyAvatar()) {
      avatarUrl = getTempAgencyAvatar()
    }
    if (!avatarUrl) {
      avatarUrl = AVATAR_URL_DEFAULT
    }

    let placeInfoParams = {}
    let suplementaryChannelId = ''
    if (submitWay === 'LOCATION') {
      placeInfoParams = {
        placeInfo: getChannelParamsInfo() || {},
        parentOrgId: orgId
      }
      const { success, data, code, msg } = await savePlaceByPoint(placeInfoParams)
      vLog.log('REGISTER savePlaceByPoint placeInfoParams,success, data, code, msg >>>>', placeInfoParams, success, data, code, msg)
      if (success) {
        const { id = '' } = data || {}
        suplementaryChannelId = id
      }
    }

    let params = {
      openid,
      unionid,
      wechatInfoId,
      phone,
      code,
      blackBox,
      captchaToken,
      orgId,
      avatar: avatarUrl || currAvatarUrl,
      additionalInfo: _additionalInfo,
      name: userName,
      jobTitle: userJob,
      inviterStaffId: faId,
    }

    const { id = '' } = await activityGetActivity()
    if (toTargetPathParams?.shareFrom === EnterSource.ACTIVITY || fromPage === "LOTTERY") {
      params['utmJson'] = JSON.stringify({ utm_source: 'wechat', utm_medium: "活动单页", utm_campaign: '注册有礼', utm_term: id, utm_content: toTargetPathParams?.targetPath || 'advLotteryActivityPage' })
    }

    getApp().sensors.track('signup', {
      signin_type: faId ? signinTypeStr ? signinTypeStr : '分享邀请' : '主动',
      invitor_unionid: faId
    })

    if (!isEmptyObject(toTargetPathParams)) {
      const { faId: _faId = '' } = toTargetPathParams
      if (!params.inviterStaffId) {
        params['inviterStaffId'] = _faId
      }
    }

    vLog.log('REGISTER onRegisterSubmit params >>>>', params)
    if (!userAvailable) {
      delete params.name
      delete params.jobTitle
    }

    let inviterInfo = []
    if (cList.length) {
      cList.forEach((lItem) => {
        const { value = '' } = lItem
        inviterInfo.push(value)
      })

      inviterInfo = inviterInfo.join('-')
      params['inviterInfo'] = inviterInfo
    }

    if (regModel === REGISTER_MODEL["5"]) {
      params['inviterInfo'] = description
    }

    if (suplementaryChannelId) {
      params['suplementaryChannelId'] = suplementaryChannelId
    }

    let _regRes = {}
    if ((isChangeReg || fromHoldUp) && !holdType) {
      let changeParams = {}
      for (const item of Object.entries(params)) {
        let [key, value] = item
        if (!PARAMS_EXCHANGE_FILTER_KEYS.includes(`${key}`) && `${value}`) {
          if (`${key}` === 'posts') {
            key = 'jobTitle'
          }
          changeParams[key] = value
        }
      }
      if (!changeParams.hasOwnProperty('orgCode')) {
        changeParams['orgCode'] = orgId
      }
      if (!changeParams.hasOwnProperty('orgName')) {
        changeParams['orgName'] = channelName
      }
      changeParams['type'] = Number(mark)

      vLog.info('REGISTER onRegisterSubmit changeParams >>>>', changeParams).report()
      _regRes = await setIdExchange(changeParams)
    } else {
      vLog.info('REGISTER onRegisterSubmit  params >>>>', params).report()
      _regRes = await doRegister(params)
    }
    vLog.log('REGISTER onRegisterSubmit params >>>>', params)
    let { code: regCode, msg: regMsg = '', data: regData, success: regSucc } = _regRes

    if (!regSucc) {
      if (regCode === LoginState.WHITE_LIST_PASS || regCode === LoginState.AGENCY_UN_OPEN_REGISTER) {
        if (regModel === REGISTER_MODEL["5"]) {
          return interaction.showToast(sysInfo?.staff?.failTips || regMsg)
        } else {
          return interaction.showToast(sysInfo?.staff?.whiteConfig?.failMsg || regMsg)
        }
      }
      if (regCode === LoginState.INREVIEW) {
        interaction.showToast(regMsg || '账号审核中')
        setUserLogin(false)
        setUserRole(regCode)
        let url = encodeURIComponent(`${wbs.gfH5}/share/advInviteRegister?result=${LoginState.INREVIEW}`)
        return wx.reLaunch({
          url: `/pages/common/webview/webPage?url=${url}&hideHomeNav=1&banShare=1`,
          complete() {
            setCustomerTypeInt(Number(mark))
            getApp().globalData.customerType = type
            setCurrRoleType(type)
          }
        })
      }

      return interaction.showToast(regMsg || '注册失败')
    }
    interaction.hideLoading()
    vLog.info('REGISTER onRegisterSubmit regCode >>>>', regCode).report()
    switch (regCode) {
      case LoginState.VERIFY_ERROR:
      case LoginState.OVERDUE: {
        interaction.showToast('验证码已过期')
        let timer = setTimeout(() => {
          clearTimeout(timer)
          return wx.navigateBack()
        }, 750)
        return
      }

      case LoginState.CODE_ERROR_01:
      case LoginState.CODE_ERROR_02:
      case LoginState.CODE_ERROR_03: {
        return interaction.showToast(regMsg)
      }

      case LoginState.WHITE_LIST_PASS: {
        if (regModel === REGISTER_MODEL["5"]) {
          return interaction.showToast(sysInfo?.staff?.failTips || regMsg)
        } else {
          return interaction.showToast(sysInfo?.staff?.whiteConfig?.failMsg || regMsg)
        }
      }

      case LoginState.INREVIEW: {
        interaction.showToast('账号审核中')
        setUserLogin(false)
        setUserRole(regCode)
        let url = encodeURIComponent(`${wbs.gfH5}/share/advInviteRegister?result=${LoginState.INREVIEW}`)
        return wx.reLaunch({
          url: `/pages/common/webview/webPage?url=${url}&hideHomeNav=1&banShare=1`,
          complete() {
            setCustomerTypeInt(Number(mark))
            getApp().globalData.customerType = type
            setCurrRoleType(type)
          }
        })
      }

      case LoginState.SUCCESS: {
        const { token = '', userId, userName } = regData || {}
        setToken(token)
        setUserId(userId)

        let wInfo = getUser()
        wInfo = {
          ...wInfo,
          token,
          userId,
          userName,
        }
        setUser(wInfo)
        setUserRole(regCode)
        setUserLogin(true)
        // 切换用户身份
        if (isChangeReg || holdType) {
          vLog.log('REGISTER isChangeReg,holdType >>>>', isChangeReg, holdType).report()
          return breakIn({ name: 'doReloadPageInfo', type: ROLE_TYPE[type] })
        } else {
          vLog.log('REGISTER Number(mark),type >>>>', Number(mark), type).report()
          // 跳转到对应页面
          setCustomerTypeInt(Number(mark))
          setCurrRoleType(type)

          return breakIn({ name: 'doRouteTargetPage', type, registerInfo: registerInfo })
        }
      }

      default:
        break
    }
  },

  //重新载入页面信息
  reLoadPageInfo(type) {
    vLog.log('REGISTER reLoadPageInfo type >>>', type)
    setCustomerTypeInt(type)
    return shapeShift(ROLE_TYPE_IMAGE[type])
  },

  /**
   * 检测填写数据是否完整
   */
  doCheckRegisterSubmit() {
    const {
      userName,
      description,
      regModel,
      canRegSubmit,
      channelName,
      isChangeReg,
      cList = [],
    } = this.data

    let _canRegSubmit = canRegSubmit
    if (!isChangeReg && !userName) {
      _canRegSubmit = false
      this.setData({
        canRegSubmit: _canRegSubmit,
        loseWay: !userName ? 1 : 2
      })
      return
    }

    if (regModel === REGISTER_MODEL["5"] && !description) {
      _canRegSubmit = false
      this.setData({
        canRegSubmit: _canRegSubmit,
        loseWay: 4
      })
      return
    }

    if (cList.length && regModel === REGISTER_MODEL["3"]) {
      _canRegSubmit = !cList.some(item => item && item.value.toString().trim() === '')
      if (!_canRegSubmit) {
        this.setData({
          canRegSubmit: _canRegSubmit,
          loseWay: 4
        })
        return
      }
    }

    if (!channelName || channelName === '请选择渠道') {
      _canRegSubmit = false
      this.setData({
        canRegSubmit: _canRegSubmit,
        loseWay: 3
      })
      return
    }

    this.setData({
      canRegSubmit: true,
      loseWay: 0
    })
  },

  /**
   * 弹窗蒙层点击
   */
  onClickOverlay() {
    this.onSetShowStatus(false)
  },

  /**
   * 弹窗关闭事件监听
   */
  onAfterLeave(e = {}) {
    vLog.log('REGISTER onAfterLeave e >>>', e)
    const { configChannel = [] } = this._data
    this.data.channelInfo.length = 1
    this._data['currACName'] = '请输入支行/营业部名称'
    this.setData({
      currList: configChannel,
      active: 0,
      scrollLeft: 0,
      scrollTop: 0,
      channelKey: '',
      showChannel: false,
      hasChecked: false,
      canConfirm: false,
      showSearchBar: false,
      channelTabs: [],
      channelInfo: this.data.channelInfo,
      containerHeight: CONTAINER_SIZE,
      canReChoose: true
    })
  },

  onLeave(e = {}) {
  },
  onBeforeLeave(e = {}) {
  },
  onAfterEnter(e = {}) {
  },
  onEnter(e = {}) {
  },
  onBeforeEnter(e = {}) {
  },

  /**
   * 取消渠道弹窗
   */
  onCancel() {
    this.onSetShowStatus(false)
  },

  /**
   * 渠道树选择确认
   */
  async onConfirm(e = {}) {
    vLog.log('REGISTER onConfirm e,this.data >>>', e, this.data)
    const { canConfirm, channelTabs, channelInfo } = this.data
    let orgId = ''
    if (!canConfirm) {
      return interaction.showToast('请选择渠道信息')
    }

    let _cInfo = [].concat(channelInfo).reverse()
    for (let i = 0; i < _cInfo.length; i++) {
      const { selectedId } = _cInfo[i] || {}
      if (selectedId && selectedId != '-1') {
        orgId = selectedId
        break
      }
    }

    vLog.log('REGISTER onConfirm orgId >>>', orgId).report()
    let cName = []
    channelTabs.forEach((cItem) => {
      const { name = '', index } = cItem
      if (index !== 9999) {
        cName.push(name)
      }
    })
    this._data['orgId'] = orgId
    this.setData({
      channelName: cName.join('-')
    }, () => this.onSetShowStatus(false))
  },

  /**
   * 渠道数据加载更多
   * @param refresh 是否刷新
   */
  onHandleLoadMore(refresh = false) {
    vLog.log('REGISTER onHandleLoadMore refresh,this.data >>>', refresh, this.data)
    const { active, currList } = this.data
    const { isLastPage, totalPages, totalElements, pageNo } = this._data
    // 第一层级 或者已全部显示
    if (!active || isLastPage) {
      this.setData({
        loadingMore: false,
      })
      return
    }

    if (pageNo >= totalPages) {
      this.setData({
        loadingMore: false,
      })
      return
    }

    if (currList.length >= totalElements) {
      this.setData({
        loadingMore: false,
      })
      return
    }

    this._data.pageNo++
    if (typeof refresh === 'boolean' && Boolean(refresh)) {
      this._data.pageNo = 0
    }
    this.setData({
      loadingMore: true,
      pageNo: this._data.pageNo
    }, () => this.loadNextPage())
  },

  async loadNextPage() {
    const { active, currList, channelInfo, channelKey } = this.data
    const { pageNo } = this._data
    vLog.log('REGISTER loadNextPage this.data >>>>', this.data)
    let ocIdIndex = active && active - 1
    let ocId = channelInfo[ocIdIndex]?.selectedId || ''

    let _currList = [].concat(currList)
    let params = {
      page: pageNo,
      pageSize: PAGE_SIZE,
    }

    if (!ocIdIndex) {
      params['orgCategoryId'] = ocId
    } else {
      params['id'] = ocId;
    }

    if (channelKey) {
      params['name'] = channelKey
    }

    interaction.showLoading('加载中...')
    const { success, data = {}, msg = '', code } = await getQueryOrgNode(params)
    vLog.log('REGISTER loadNextPage getQueryOrgNode >>>>', success, data, msg, code)
    interaction.hideLoading()
    if (!success) {
      this.setData({
        loadingMore: false,
      })
      return interaction.showToast(msg)
    }

    const { content = [], totalElements, last, totalPages } = data
    _currList = _currList.concat(content)
    this._data = {
      ...this._data,
      totalElements,
      totalPages,
    }
    this.setData({
      loadingMore: false,
      isLastPage: last,
      currList: _currList
    })
  },

  //获取用户信息
  getUserProfile() {
    const { canRegSubmit, loseWay } = this.data
    if (!canRegSubmit) {
      return interaction.showToast(LOSE_TYPE_TOAST_MSG[loseWay])
    }

    wx.showLoading({
      title: '加载中...',
      mask: true
    })
    let that = this
    wx.getUserProfile({
      desc: '用于完善会员资料', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
      success: (res) => {
        vLog.log('REGISTER wx.getUserProfile res >>>>', res)
        return that.onGetUnionId(res)
      },
      complete: () => {
        wx.hideLoading()
      }
    })
  },


  //获取UnionId
  async onGetUnionId(wxInfoRes = {}) {
    vLog.log('REGISTER onGetUnionId wxInfoRes,this.data >>>>', wxInfoRes, this.data)
    const that = this
    if (!isEmptyObject(wxInfoRes)) {
      let wInfo = getUser()
      wInfo = {
        ...wInfo,
        ...wxInfoRes?.userInfo
      }
      vLog.log('REGISTER onGetUnionId wInfo >>', wInfo)
      setUser(wInfo)
    }

    await wx.login({
      success(res) {
        const { code: rCode } = res || {}
        vLog.log('REGISTER onGetUnionId wx.login >>', res)
        if (rCode) {
          let params = {
            code: rCode,
            wechatCode: global.SOURCE_CODE,
            ...wxInfoRes,
          }

          vLog.log('REGISTER getUnionId  params >', params)
          //发起网络请求
          getUnionId(params)
            .then(result => {
              const { success, msg, param, code } = result || {}
              vLog.log('REGISTER getUnionId >>', success, msg, param, code)
              that.setData({ wxInfo: param })
              if (!success) {
                wx.hideLoading()
                return interaction.showToast(msg || '')
              } else {
                setSaveUserInfo(param, false)
                return that.onRegisterSubmit()
              }
            })
            .catch(error => {
              vLog.error('REGISTER getUnionId >>>>', error).report()
            })
        } else {
          vLog.error('REGISTER 登录失败 >>', res && res.errMsg).report()
        }
      }
    })
  },

  /**
   * 渠道输入清除
   */
  onCClear() {
    this.setData({
      channelKey: ''
    })
  },

  /**
   * 渠道搜索
   */
  async onCSearch(e = {}) {
    vLog.log('REGISTER onCSearch e,this.data >>>', e, this.data)
    const { channelInfo, channelKey, active } = this.data
    if (!channelKey) {
      this._data['pageNo'] = 0
      this.setData({
        currList: [],
        hasChecked: false,
        isLastPage: false,
        scrollTop: 0,
      }, () => this.onHandleLoadMore(true))
      return
    }
    let ocIdIndex = active && active - 1
    let ocId = channelInfo[ocIdIndex]?.selectedId || ''

    let _currList = []
    let params = {
      page: 0,
      pageSize: PAGE_SIZE,
      name: channelKey,
    }

    if (!ocIdIndex) {
      params['orgCategoryId'] = ocId
    } else {
      params['id'] = ocId;
    }
    vLog.log('REGISTER onCSearch params >>>', params)
    interaction.showLoading('加载中...')
    const { success, data, msg, code } = await getQueryOrgNode(params)
    interaction.hideLoading()
    vLog.log('REGISTER onCSearch getQueryOrgNode success, data, msg, code >>>', success, data, msg, code)
    if (success) {
      const { content = [], totalElements, last, totalPages } = data || {}
      _currList = [].concat(content)

      this._data = {
        ...this._data,
        totalElements,
        totalPages,
      }

      this.setData({
        currList: _currList,
        isLastPage: last,
        loadingMore: false,
        scrollTop: 0
      })
    }
  },

  /**
   * 渠道搜索输入
   */
  onCChange(e = {}) {
    this.setData({
      channelKey: `${e?.detail}`.trim()
    })
  },

  onSetChannelInfo(way = '') {
    vLog.log('REGISTER onSetChannelInfo way >>>>', way)
    const { currList, channelInfo, channelTabs } = this.data
    const { hasAdd, currACName = '' } = this._data
    let _targetIndex = channelInfo.length - 1 || 0
    let _level = channelInfo[_targetIndex]?.level || 1
    let _channelInfo = [].concat(channelInfo)
    let _channelTabs = [].concat(channelTabs)
    let _currList = [].concat(currList)

    let _content = currACName || ''
    switch (way) {
      case 'LOCATION':
        let _cInfo = getChannelParamsInfo() || {}
        const { title: content = '' } = _cInfo || {}
        _content = `${content}`.trim()
        break

      case "WRITE":
        let _cTips = getChannelNameTips() || ''
        _content = `${_cTips}`.trim()
        break

      default:
        break
    }

    const acParams = {
      level: _level,
      name: _content,
      id: -1
    }
    const tabParams = {
      name: _content,
      index: _level
    }
    _channelTabs.pop()
    _channelTabs.push(tabParams)
    // 修改
    if (hasAdd) {
      let clLastIndex = currList.length - 1
      _currList[clLastIndex].name = _content
    } else { //新增
      _currList.push(acParams)
      _channelInfo[_targetIndex].selectedId = -1
      _channelInfo[_targetIndex].selectedIndex = _currList.length - 1
    }
    this._data = {
      ...this._data,
      hasAdd: true,
      hasNext: false,
      currACName: _content,
    }

    this.setData({
      currList: _currList,
      channelInfo: _channelInfo,
      channelTabs: _channelTabs,
      hasChecked: true,
    })
  },

  /**
   * 手动添加渠道
   */
  onAddChannel() {
    return wx.navigateTo({
      url: '/packages-common/pages/common/location/index'
    })
  },

  /**
   * 切换渠道
   */
  async tabSelect(event = {}) {
    vLog.log('======== REGISTER tabSelect event,this.data >>>', event, this.data)
    const { channelInfo, channelTabs, crStart, canConfirm } = this.data
    const { index = 0 } = event.detail

    let _channelInfo = [].concat(channelInfo)
    let _channelTabs = [].concat(channelTabs)
    let len = channelInfo.length - 1
    let _canConfirm = canConfirm

    let isLastTab = _channelTabs[index].index
    if (isLastTab === 9999 || index == len) {
      this.setData({ scrollTop: 0 })
      return true
    }

    // 第一个
    if (index === 0) {
      this.data.channelInfo.length = 1
      this._data['hasAdd'] = false

      this.setData({
        hasChecked: false,
        canConfirm: false,
        channelInfo: this.data.channelInfo,
        channelTabs: [floatTips],
        active: index,
        currList: this._data.configChannel,
        channelKey: '',
        showSearchBar: false,
        containerHeight: CONTAINER_SIZE,
        scrollTop: 0,
        canReChoose: true
      })
    }

    // 中间的某个
    if (index && index !== len) {
      let params = {
        page: 0,
        pageSize: PAGE_SIZE,
      }

      let level = index - 1
      let cutLength = index + 1
      _channelInfo.length = cutLength

      if (index <= _channelInfo.length - 1) {
        _channelInfo[index * 1]['selectedId'] = ''
        _channelInfo[index * 1]['selectedIndex'] = 0
      }

      let id = _channelInfo[level]?.selectedId || ''

      _channelTabs.length = index
      _channelTabs.push(floatTips)

      vLog.log('REGISTER _channelTabs >>>>', _channelTabs)
      let _tabLen = _channelTabs.length
      if (_tabLen >= crStart) {
        const { index: _cIndex } = _channelTabs[crStart - 1] || {}
        if (_cIndex * 1 < 9999) {
          _canConfirm = true
        }
      }

      // 第二层
      if (!level) {
        params['orgCategoryId'] = id
      } else {
        // 其他子层
        params['id'] = id
      }
      vLog.log('REGISTER tabSelect params >>>', params)
      const { success, data } = await getQueryOrgNode(params)
      interaction.hideLoading()
      let _currList = []
      let _isLastPage = 0
      let _totalElements = 0
      let _totalPages = 0

      if (success) {
        const { content = [], totalElements, last, totalPages } = data || {}
        _currList = [].concat(content)
        _isLastPage = last
        _totalElements = totalElements
        _totalPages = totalPages
      }

      if (index < crStart) {
        _canConfirm = false
      }
      vLog.log(' REGISTER tabSelect index, crStart, index < crStart, _canConfirm >>>', index, crStart, index < crStart, _canConfirm)

      this._data = {
        ...this._data,
        hasAdd: false,
        totalElements: _totalElements,
        totalPages: _totalPages,
        pageNo: 0,
      }

      this.setData({
        hasChecked: false,
        canConfirm: _canConfirm,
        channelInfo: _channelInfo,
        channelTabs: _channelTabs,
        active: index,
        currList: _currList,
        scrollTop: 0,
        isLastPage: _isLastPage,
        channelKey: '',
        canReChoose: true
      })
    }
  },

  /**
   * 选择渠道
   */
  async radioChange(e = {}) {
    vLog.log('REGISTER radioChange e,this.data >>>', e, this.data)

    const {
      currList,
      channelTabs,
      active,
      scrollLeft,
      hasChecked,
      canConfirm,
      channelInfo,
      isLastPage,
      crStart,
      channelKey
    } = this.data

    const {
      totalElements,
      totalPages,
      hasNext,
    } = this._data

    const { value = '' } = e.detail
    let _sIndex = 0
    let cTarget = currList.find((sItem, sIndex) => {
      const { id } = sItem
      _sIndex = sIndex
      return id == value
    })

    let rInstall = channelInfo[channelInfo.length - 1] || {}
    rInstall.selectedId = value
    rInstall.selectedIndex = _sIndex
    let _channelInfo = [].concat(channelInfo)
    _channelInfo.pop()
    _channelInfo.push(rInstall)

    vLog.log('REGISTER radioChange _channelInfo,cTarget >>>', _channelInfo, cTarget)
    const { id = '', level = 0, name = '', } = cTarget || {}
    const tabChoose = {
      name,
      index: level + 1
    }
    let _channelTabs = [].concat(channelTabs)
    _channelTabs.pop()
    _channelTabs.push(tabChoose)

    let _hasNext = hasNext
    let _active = active
    let _canConfirm = canConfirm
    let _hasChecked = hasChecked
    let _scrollLeft = scrollLeft
    let _channelKey = channelKey
    interaction.showLoading('加载中...')
    const { success: nSucc, data: nData } = await findLastNode({ id, isNode: level !== 0 })
    if (nSucc) {
      _hasNext = nData
    }

    // 如果还有子层
    if (_hasNext) {
      _active = level + 1
      _channelTabs.push(floatTips)
      _hasChecked = false
      _scrollLeft += Math.floor(windowWidth * 0.65)
    } else {
      _hasChecked = true
    }

    let params = {
      page: 0,
      pageSize: PAGE_SIZE,
    }

    // 第二层
    if (!level) {
      params['orgCategoryId'] = id
    } else {
      // 其他子层
      params['id'] = id
    }
    vLog.log('REGISTER radioChange params >>>', params)
    const { success, data, msg, code } = await getQueryOrgNode(params)
    interaction.hideLoading()
    vLog.log('REGISTER radioChange getQueryOrgNode success, data, msg, code >>>', success, data, msg, code)
    vLog.log('REGISTER radioChange _hasNext,_channelTabs >>>', _hasNext, _channelTabs)
    // 有子层级
    if (_hasNext) {
      let _currList = []
      let _isLastPage = isLastPage
      let _totalElements = totalElements
      let _totalPages = totalPages
      _channelKey = ''

      if (success) {
        const { content = [], totalElements, last, totalPages } = data || {}
        _currList = [].concat(content)
        _isLastPage = last
        _totalElements = totalElements
        _totalPages = totalPages
      }
      const lParams = {
        level: _active,
        content: _currList,
        totalElements: _currList.length,
        selectedId: '',
        selectedIndex: '',
      }
      _channelInfo.push(lParams)

      vLog.log('REGISTER radioChange _currList >>>', _currList)
      this._data = {
        ...this._data,
        totalElements: _totalElements,
        totalPages: _totalPages,
      }
      this.setData({
        currList: _currList,
        isLastPage: _isLastPage,
      })
    }

    let _tabLen = _channelTabs.length
    if (_tabLen >= crStart) {
      const { index: _cIndex } = _channelTabs[crStart - 1] || {}
      if (_cIndex * 1 < 9999) {
        _canConfirm = true
      }
    }
    this._data['pageNo'] = 0
    this._data['hasNext'] = _hasNext
    this.setData({
      active: _active,
      canConfirm: _canConfirm,
      scrollLeft: _scrollLeft,
      hasChecked: _hasChecked,
      channelTabs: _channelTabs,
      channelInfo: _channelInfo,
      containerHeight: CONTAINER_SIZE - SEARCH_BAR_HEIGHT,
      showSearchBar: true,
      loadingMore: false,
      channelKey: _channelKey
    })
  }
});

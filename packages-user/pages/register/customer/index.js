import {
  getSystemInfo,
  getUser,
  setUser,
  getUnionID,
  getOpenId,
  getWechatInfoId,
  setUserRole,
  getUserPhone,
  setSaveUserInfo,
  setCustomerTypeInt,
  setCurrRoleType,
  setUserLogin,
  setToken,
  setUserId,
} from "../../../../common/utils/userStorage";

import {
  interaction,
  qs,
  util,
  enums,
  global,
  breakIn,
  wbs,
  vLog
} from "../../../../common/index.js";

import {
  activityGetActivity,
  doNormalRegister,
  getUnionId,
  setIdExchange
} from "../../../../common/nb/home";

const {
  EnterSource,
} = enums

const {
  LoginState,
  REGISTER_TYPE,
  ROLE_TYPE,
  APP_ID,
  NORMAL_REGISTER_ORG_ID,
  REGISTER_MODEL,
  LOSE_TYPE_TOAST_MSG,
  PARAMS_EXCHANGE_FILTER_KEYS,
  REGISTER_MODEL_LIST,
  AVATAR_URL_DEFAULT,
  INVITER_INFO
} = enums

const {
  isEmptyObject,
  parseOptions2Object,
  panTransformDCode2Object,
} = util

/**
 * 注册审核模式KEY
 * @type {number[]}
 */

const fpPlugin = requirePlugin('tdfp-plugin')
const app = getApp()

const { appId = '' } = wx.getAccountInfoSync().miniProgram || {}

Page({
  data: {
    singleInviterPath: '',
    faName: '广发木棉花',
    registerImg: '',
    userName: '',
    doGetUserInfo: true,
    loseWay: 1,
    userAvailable: false,
    canRegSubmit: false,
  },

  _data: {
    registerInfo: {},
    sysInfo: {},
    wInfo: {},
    channelName: '普通客户-普通客户',
    orgId: NORMAL_REGISTER_ORG_ID.PRO,
    selectedName: '',
    holdType: '',
    fromHoldUp: false,

    blackBox: '',
    captchaToken: '',
    regModel: REGISTER_MODEL[3],

    regFrom: '',
    fromRegister: '',
    currList: [],
    canConfirm: false,
    isChangeReg: false
  },

  onLoad(options = {}) {
    vLog.info('NORMAL_REGISTER options >>>>', qs.parse(options)).report()
    let isChangeReg = false
    let faName = '广发木棉花'
    let fromHoldUp = false
    var fragment = new fpPlugin.FMAgent(app.globalData._fmOpt)

    let registerInfo = parseOptions2Object(options)
    vLog.log(' NORMAL_REGISTER registerInfo >>>>', registerInfo)
    let {
      fromRegister = '',
      fromHoldUp: fHoldUp = '',
      regFrom = '',
      singleInviterPath = '',
      inviterName = '',
      signinTypeStr = ''
    } = registerInfo

    if (inviterName) {
      faName = inviterName
    }
    if (fHoldUp) {
      fromHoldUp = true
    }

    if (fromRegister || fHoldUp) {
      let fRes = panTransformDCode2Object(registerInfo)
      vLog.log('NORMAL_REGISTER fRes >>>>', fRes)
      registerInfo = { ...fRes }
    }

    let orgId = NORMAL_REGISTER_ORG_ID.NEW_DEV
    let hasWInfoId = !!getWechatInfoId()
    const { registerType = '' } = registerInfo
    vLog.log('NORMAL_REGISTER registerInfo,hasWInfoId,appId >>>>', registerInfo, hasWInfoId, appId)
    if (appId === APP_ID.PRO) {
      orgId = NORMAL_REGISTER_ORG_ID.PRO
    }

    if (registerType === REGISTER_TYPE.CHANGE) {
      isChangeReg = true
      registerInfo = {
        ...registerInfo,
        openid: getOpenId(),
        unionid: getUnionID(),
        wechatInfoId: getWechatInfoId(),
        phone: getUserPhone(),
        faId: '',
      }
    }

    if (regFrom === 'START_UP') {
      singleInviterPath = true
    }

    this.setData({
      singleInviterPath,
      faName,
      signinTypeStr,
      doGetUserInfo: !hasWInfoId,
    })

    this._data = {
      ...this._data,
      regFrom,
      fromHoldUp,
      registerInfo,
      isChangeReg,
      orgId
    }
    this.initSysInfo()
    if (fragment) {
      this.initBlackbox(fragment)
    }
  },

  /**
   * 用户信息
   */
  initSysInfo() {
    const sysInfo = getSystemInfo()
    const {
      agency: {
        registerImg = '',
        newUser,
      }
    } = sysInfo

    let userAvailable = REGISTER_MODEL_LIST.includes(newUser * 1)
    let regModel = REGISTER_MODEL[newUser * 1]

    this.setData({
      registerImg,
      userAvailable,
    })

    this._data = {
      ...this._data,
      sysInfo,
      regModel,
    }
  },

  initBlackbox(fragment) {
    let that = this
    fragment && fragment.getInfo({
      page: that,
      mode: 'plugin',
      noClipboard: true,
      openid: getOpenId(),
      unionid: getUnionID(),
      success: function (blackBox) {
        that._data['blackBox'] = blackBox
      },
      fail: function (err) {
        vLog.error('NORMAL_REGISTER initBlackbox failed >>>', err).report()
      }
    })
  },

  onShow() {
    wx.hideShareMenu({
      menus: ['shareAppMessage', 'shareTimeline']
    })
  },

  /**
   * 信息输入
   */
  regInputEvent(e = {}) {
    const {
      detail = '',
      currentTarget: {
        dataset: {
          type = ''
        }
      }
    } = e

    if (type === 'name') {
      this.setData({ userName: detail }, () => this.doCheckRegisterSubmit())
    }
  },

  // ================================= 无痕验证 =================================//
  onTryToVerify() {
    // 获取验证码插件实例，#id与wxml文件中组件节点id一致
    const that = this
    const td = this.selectComponent('#td-captcha-normal-register');

    // 调用API，触发验证码弹出
    td.captcha.triggerCaptcha({
      partnerCode: 'gffunds', // 同盾合作方
      appName: 'gffund_xcx', // 同盾合作方应用
      env: 1, // 1-线上环境 0-测试环境
      blackbox: that._data.blackBox || "",  // 设备指纹blackbox，非必填
      onSuccess: that.onTryToVerifySuccess, // 自定义验证成功回调
      onFail: that.onTryToVerifyFail, // 自定义验证失败回调，非必填
      onClose: that.onTryToVerifyClose, // 自定义验证码关闭回调，非必填
      lang: 1, // 语言配置项,必填
      maskClose: 0, // 蒙层是否可以关闭
    });
  },

  onTryToVerifySuccess: function (captchaToken = '') {
    interaction.showLoading('加载中...')
    const that = this
    that._data['captchaToken'] = captchaToken
    return that.onNormalRegisterSubmit()
  },

  onTryToVerifyFail: function (msg = '') {
    if (msg !== 'opFail') {
      interaction.showToast(msg)
    }
  },

  async onTryToVerifyClose() {
    vLog.info('NORMAL_REGISTER onTryToVerifyClose').report()
  },
  // ================================= 无痕验证 =================================//

  onRegSubmitVerify() {
    return this.onTryToVerify()
  },

  /**
   * 提交注册
   */
  async onNormalRegisterSubmit() {
    vLog.log('NORMAL_REGISTER onNormalRegisterSubmit this.data >>>', this.data, this._data)
    const {
      canRegSubmit,
      loseWay,
      userName,
      signinTypeStr
    } = this.data

    const {
      blackBox = '',
      captchaToken = '',
      registerInfo,
      fromHoldUp,
      holdType,
      isChangeReg,
      orgId,
      channelName
    } = this._data

    if (!canRegSubmit) {
      return interaction.showToast(LOSE_TYPE_TOAST_MSG[loseWay])
    }
    interaction.showLoading('信息提交中...')

    let {
      phone = '',
      code = '',
      openid = '',
      wechatInfoId = '',
      unionid = '',
      faId,
      toTargetPathParams = {},
      mark = '',
      type = '',
      fromPage = ''
    } = registerInfo || {}

    if (!phone) {
      phone = getUserPhone()
    }
    if (!openid) {
      openid = getOpenId()
    }
    if (!unionid) {
      unionid = getUnionID()
    }
    if (!wechatInfoId) {
      wechatInfoId = getWechatInfoId()
    }

    let params = {
      openid,
      unionid,
      wechatInfoId,
      phone,
      code,
      blackBox,
      captchaToken,
      name: userName || '',
      inviterStaffId: faId,
      inviterInfo: INVITER_INFO || '普通客户'
    }
    const { id = '' } = await activityGetActivity()
    if (toTargetPathParams?.shareFrom === EnterSource.ACTIVITY || fromPage === "LOTTERY") {
      params['utmJson'] = JSON.stringify({ utm_source: 'wechat', utm_medium: "活动单页", utm_campaign: '注册有礼', utm_term: id, utm_content: toTargetPathParams?.targetPath || 'advLotteryActivityPage' })
    }

    getApp().sensors.track('signup', {
      signin_type: faId ? signinTypeStr ? signinTypeStr : '分享邀请' : '主动',
      invitor_unionid: faId
    })

    if (!isEmptyObject(toTargetPathParams)) {
      const { faId: _faId = '' } = toTargetPathParams
      if (!params.inviterStaffId) {
        params['inviterStaffId'] = _faId
      }
    }

    vLog.info('NORMAL_REGISTER onNormalRegisterSubmit params >>>>>', JSON.stringify(params)).report()
    let _regRes = {}
    if ((isChangeReg || fromHoldUp) && !holdType) {
      let changeParams = {}
      for (const item of Object.entries(params)) {
        let [key, value] = item
        if (!PARAMS_EXCHANGE_FILTER_KEYS.includes(`${key}`) && `${value}`) {
          if (`${key}` === 'posts') {
            key = 'jobTitle'
          }
          changeParams[key] = value
        }
      }
      if (!changeParams.hasOwnProperty('orgCode')) {
        changeParams['orgCode'] = orgId
      }
      if (!changeParams.hasOwnProperty('orgName')) {
        changeParams['orgName'] = channelName
      }
      changeParams['type'] = Number(mark)
      vLog.log('NORMAL_REGISTER setIdExchange changeParams >>>>>', changeParams)
      _regRes = await setIdExchange(changeParams)
    } else {
      vLog.log('NORMAL_REGISTER doNormalRegister params >>>>>', params)

      _regRes = await doNormalRegister(params)
    }

    const { success, data, msg = '', code: rCode } = _regRes || {}
    vLog.info('====== NORMAL_REGISTER onNormalRegisterSubmit success, msg, code >>>>>', success, msg, rCode).report()

    if (!success) {
      if (rCode === LoginState.INREVIEW) {
        interaction.showToast(msg || '账号审核中')
        let url = encodeURIComponent(`${wbs.gfH5}/share/advInviteRegister?result=${LoginState.INREVIEW}`)
        return wx.reLaunch({
          url: `/pages/common/webview/webPage?url=${url}&hideHomeNav=1&banShare=1`,
          complete() {
            setCustomerTypeInt(Number(mark))
            setCurrRoleType('CUSTOMER')
          }
        })
      }

      return interaction.showToast(msg || '注册失败')
    }
    interaction.hideLoading()

    switch (rCode) {
      // 验证码不通过
      case LoginState.VERIFY_ERROR:
      case LoginState.OVERDUE: {
        interaction.showToast('验证码已过期')
        let timer = setTimeout(() => {
          clearTimeout(timer)
          return wx.navigateBack()
        }, 750)
        return
      }

      // 同盾验证失败
      case LoginState.CODE_ERROR_01:
      case LoginState.CODE_ERROR_02:
      case LoginState.CODE_ERROR_03: {
        return interaction.showToast(msg)
      }

      // 待审核
      case LoginState.INREVIEW: {
        interaction.showToast('账号审核中')
        setUserLogin(false)
        setUserRole(rCode)
        let url = encodeURIComponent(`${wbs.gfH5}/share/advInviteRegister?result=${LoginState.INREVIEW}`)
        return wx.reLaunch({
          url: `/pages/common/webview/webPage?url=${url}&hideHomeNav=1&banShare=1`,
          complete() {
            setCustomerTypeInt(Number(mark))
            setCurrRoleType('CUSTOMER')
          }
        })
      }

      // 成功
      case LoginState.SUCCESS: {
        const { token = '', userId, userName } = data || {}
        setToken(token)
        setUserId(userId)

        let wInfo = getUser()
        wInfo = {
          ...wInfo,
          token,
          userId,
          userName,
        }
        setUser(wInfo)
        setUserRole(rCode)
        setUserLogin(true)
        if (isChangeReg || holdType) {
          return breakIn({ name: 'doReloadPageInfo', type: ROLE_TYPE[type] })
        } else {
          vLog.info('REGISTER Number(mark),type >>>>', Number(mark), type).report()
          setCustomerTypeInt(Number(mark))
          setCurrRoleType(type || 'CUSTOMER')
          return breakIn({ name: 'doRouteTargetPage', type: type || 'CUSTOMER', registerInfo: registerInfo })
        }
      }

      default:
        break
    }
  },

  //获取用户信息
  getUserProfile() {
    const { canRegSubmit, loseWay } = this.data
    if (!canRegSubmit) {
      return interaction.showToast(LOSE_TYPE_TOAST_MSG[loseWay])
    }
    let that = this
    wx.showLoading({
      title: '加载中...',
      mask: true
    })
    wx.getUserProfile({
      desc: '用于完善会员资料', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
      success: (res) => {
        vLog.log(' NORMAL_REGISTER wx.getUserProfile res >>>>', res)
        return that.onGetUnionId(res)
      },
      complete: () => {
        wx.hideLoading()
      }
    })
  },

  //获取UnionId
  async onGetUnionId(wxInfoRes = {}) {
    vLog.log('NORMAL_REGISTER onGetUnionId wxInfoRes,this.data >>>>', wxInfoRes, this.data)
    const that = this
    if (!isEmptyObject(wxInfoRes)) {
      let wInfo = getUser()
      wInfo = {
        ...wInfo,
        ...wxInfoRes?.userInfo
      }
      vLog.log('NORMAL_REGISTER onGetUnionId wInfo >>', wInfo)
      setUser(wInfo)
    }

    await wx.login({
      success(res) {
        const { code: rCode } = res || {}
        vLog.log('NORMAL_REGISTER onGetUnionId wx.login >>>', res)
        if (rCode) {
          let params = {
            code: rCode,
            wechatCode: global.SOURCE_CODE,
            ...wxInfoRes,
          }

          vLog.log('NORMAL_REGISTER getUnionId  params >>', params)
          //发起网络请求
          getUnionId(params)
            .then(result => {
              const { success, msg, param, code } = result || {}
              vLog.log('NORMAL_REGISTER getUnionId >>', success, msg, param, code)
              that._data.wInfo = param
              if (!success) {
                wx.hideLoading()
                return interaction.showToast(msg || '')
              } else {
                setSaveUserInfo(param, false)
                return that.onNormalRegisterSubmit()
              }
            })
            .catch(error => {
              vLog.error('NORMAL_REGISTER getUnionId error >>>>>', error).report()
            })
        } else {
          vLog.error('NORMAL_REGISTER 登录失败 ', res && res.errMsg).report()
        }
      }
    })
  },

  /**
   * 检测填写数据是否完整
   */
  doCheckRegisterSubmit() {
    vLog.log('NORMAL_REGISTER doCheckRegisterSubmit this.data >>>', this.data)
    const {
      userName,
      canRegSubmit,
    } = this.data

    let _canRegSubmit = canRegSubmit
    if (!userName) {
      _canRegSubmit = false
      this.setData({
        canRegSubmit: _canRegSubmit,
        loseWay: 1
      })
      return
    }

    this.setData({
      canRegSubmit: true,
      loseWay: 0
    })
  },
});

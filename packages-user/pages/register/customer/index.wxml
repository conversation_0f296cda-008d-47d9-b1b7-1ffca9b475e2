<nb-page
    navShowBack="{{!singleInviterPath}}"
    showHomeIcon="{{!!singleInviterPath}}"
    contentUseView="{{true}}"
    navTitle="{{faName}}邀您注册"
    inHomePage="{{false}}"
    hasPageContainer="{{true}}"
    showTabBar="{{false}}">
  <tdcaptcha id="td-captcha-normal-register" />
  <scroll-view
      class="reg-scroll-container"
      style="position:fixed;width:100%;left:0;height:100vh;"
      enable-flex="{{true}}"
      enable-passive="{{true}}"
      scroll-with-animation
      enable-back-to-top
      scroll-y="true">
    <image
        mode="widthFix"
        class="bgImg"
        src="{{registerImg}}?x-oss-process=image/resize,m_fill,h_730,w_414/format,jpg"
    />

    <nb-card card-class="card-container">
      <van-cell-group custom-class="cell" border="{{false}}">
        <van-field
            type="text"
            label=""
            required
            data-type="name"
            placeholder="请输入您的真实姓名"
            clearable
            value="{{userName}}"
            bind:input="regInputEvent"
        />
      </van-cell-group>

      <block>
        <view
            wx:if="{{!doGetUserInfo}}"
            class="reg-submit-btn"
            style="background-color: {{$state.themeColor}};opacity: {{!loseWay?1:0.65}}"
            bind:tap="onRegSubmitVerify">
          {{userAvailable ? '提交' : '注册'}}
        </view>

        <button
            wx:else
            class='nb-btn block btn reg-submit-btn'
            disabled="{{!canRegSubmit}}"
            style='background-color:{{$state.themeColor}};opacity: {{!loseWay?1:0.65}}'
            bindtap='getUserProfile'>
          {{userAvailable ? '提交' : '注册'}}
        </button>
      </block>


      <view class="reg-bottom-tips" style="color: {{$state.themeColor}}">
        {{'请您填写真实信息，将有专人核实身份'}}
      </view>

      <view class="reg-bottom-logo">
        <image src="../../../../components/nb-page/images/<EMAIL>" mode="heightFix" class="bottom-logo"/>
      </view>
    </nb-card>

    <!-- <view class="reg-footer-card"/> -->
  </scroll-view>

  <van-toast id="van-toast"/>
</nb-page>

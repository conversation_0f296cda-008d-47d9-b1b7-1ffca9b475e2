import {interaction, util, vLog} from '../../../common/index.js'
import {getCurrRoleType, getSystemInfo} from "../../../common/utils/userStorage";

const {isEmptyObject} = util

Page({
  data: {
    disableUserTipImg: '',
    agreement: '',
    customerType: 'CHANNEL',
  },

  onLoad(options = {}) {
    vLog.log('APPLY_FAIL options >>>>', options)
    let customerType = getCurrRoleType()
    this.setData({
      customerType
    }, () => this.getConfigType())
  },

  onShow() {
    wx.hideShareMenu({
      menus: ['shareAppMessage', 'shareTimeline']
    })
  },

  //获取系统配置
  getConfigType() {
    const {customerType} = this.data
    const sysInfo = getSystemInfo()
    if (!isEmptyObject(sysInfo)){
      let agreementSuccessContent
      let disableUserTipImg
      let {
        staff: {
          agreementSuccessContent: sContent = '',
          disableUserTipImg: sTipImg = ''
        },
        agency: {
          agreementSuccessContent: aContent = '',
          disableUserTipImg: dTipImg = ''
        }
      } = sysInfo || {}

      if (customerType === 'AGENCY'){
        agreementSuccessContent = aContent
        disableUserTipImg = dTipImg
      } else {
        agreementSuccessContent = sContent
        disableUserTipImg = sTipImg
      }

      this.setData({
        disableUserTipImg,
        agreement: `${agreementSuccessContent}`.split('&&')
      })
    } else {
      return interaction.showToast('当前网络异常，请稍后再试')
    }
  },
})

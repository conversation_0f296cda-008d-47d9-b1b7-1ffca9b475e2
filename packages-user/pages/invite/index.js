import {
  global,
  util,
  systemtInfo,
  interaction,
  qs,
  eventName,
  enums,
  breakIn,
  vLog
} from "../../../common/index.js";
import {
  addSceneParam,
  getWXACode
} from "../../../common/nb/home";

import {
  getUser,
  getSystemInfo,
  getUserRole,
  setAppHoldStatus,
  getUserId,
  getCurrRoleType,
  getUserLogin
} from "../../../common/utils/userStorage";

import InviteCard from "./painter/card";

const {
  GLOBAL_START_PATH,
  EnterSource
} = enums

const {
  REFRESH_PAGE_DATA,
} = eventName

const app = getApp()
const { windowWidth } = wx.getSystemInfoSync()
const { screenHeight, footHeight, titleHeight } = systemtInfo

const { rpx2px } = util

const inviteParams = {
  type: 'INVITE',
  inviterStaffId: getUserId()
}

Page({
  data: {
    contentHeight: rpx2px(screenHeight - footHeight - titleHeight),
    customActionStyle: {},
    width: Math.ceil(windowWidth),
    height: Math.ceil(rpx2px(screenHeight - titleHeight)),

    inviteData: {
      path: `${GLOBAL_START_PATH}?${qs.stringify(inviteParams)}`,
      imageUrl: '',
      title: ''
    },
    floatTitle: "",
    floatImg: '',
    invitationImg: '',
    fromTab: '',

    wInfo: {},
    sysInfo: {},
    hasLogin: false,
    userRole: null,

    progress: 0,
    building: true,
    qrCode: '',
    src: '',
    saveImg: '',
    envVersion: "",
    omitMPage: 0,
    customerType: 'CHANNEL'
  },

  onLoad(options = {}) {
    vLog.log('INVITE options >>>>', options)
    const { fromTab } = options

    const userRole = getUserRole()
    const customerType = getCurrRoleType()
    const hasLogin = getUserLogin()

    this.setData({
      sysInfo: getSystemInfo(),
      wInfo: getUser(),
      userRole,
      hasLogin,
      fromTab,
      customerType
    }, () => this.initInvite())
    this.onProbe()
  },

  onProbe() {
    let _ProbeTimer = setTimeout(() => {
      clearTimeout(_ProbeTimer)

      let userRole = getUserRole()
      if (typeof userRole === 'string') {
        userRole = ''
      }

      this.setData({
        userRole
      })
    }, 1500)
  },

  async initInvite() {
    interaction.showLoading('加载中...')
    const { sysInfo = {}, wInfo = {}, customerType = '' } = this.data
    const that = this
    const { envVersion = '' } = wx.getAccountInfoSync().miniProgram
    breakIn({ name: 'initVersionInfo' })

    const { staff = {}, enterprise = {}, agency = {}, common = {} } = sysInfo
    const { abbreviation = '', icon } = enterprise

    let data
    if (customerType === 'AGENCY') {
      data = agency
    } else {
      data = staff
    }

    let {
      popupContent = '',
      invitationImg = '',
      popupImg = '',
      invitationShareImg = '',
    } = staff

    const {
      popupContent: aPContent = '',
      invitationImg: aIImg = '',
      popupImg: aPImg = '',
      invitationShareImg: aISImg = '',
    } = agency

    const {
      popupContent: cPContent = '',
      invitationImg: cIImg = '',
      popupImg: cPImg = '',
      invitationShareImg: cISImg = '',
    } = common

    if (customerType === 'AGENCY') {
      popupContent = aPContent
      invitationImg = aIImg
      popupImg = aPImg
      invitationShareImg = aISImg
    }

    if (customerType === 'CUSTOMER') {
      popupContent = cPContent
      invitationImg = cIImg
      popupImg = cPImg
      invitationShareImg = cISImg
    }

    const {
      userName = '',
      name: uName = '',
      nickName = '',
      userId
    } = wInfo || {}

    const inviteParams = {
      type: 'INVITE',
      inviterStaffId: userId || getUserId(),
      inviterRole: customerType,
      inviterName: `${userName || uName || nickName}`,
    }

    let inviteData = {
      imageUrl: invitationShareImg || icon,
      title: `${userName || uName || nickName}邀您使用${abbreviation}`,
      path: `${GLOBAL_START_PATH}?${qs.stringify(inviteParams)}`
    }

    if (`${invitationImg}`.includes('?x-oss-process=image')) {
      let [cutUrl] = `${invitationImg}`.split('?x-oss-process=image')
      vLog.log('INVITE_FILTER_OSS cutUrl >>>', cutUrl)
      invitationImg = cutUrl
    }

    await wx.request({
      url: `${invitationImg}?x-oss-process=image/info`,
      method: 'GET',
      success(res) {
        const {
          data: {
            ImageWidth: {
              value: wImg = 1,
            },
            ImageHeight: {
              value: hImg = 1,
            }
          }
        } = res || {}

        vLog.log('NVITE windowWidth / wImg * hImg >>>', windowWidth / wImg * hImg)
        that.setData({
          height: Math.ceil(windowWidth / wImg * hImg)
        })
      }
    })

    this.setData({
      envVersion,
      floatTitle: (popupContent && popupImg) ? popupContent : '',
      invitationImg,
      inviteData,
      floatImg: popupImg,
    }, () => this.getWXACodeImg())
  },

  async getWXACodeImg() {
    const { envVersion = '', customerType = '', wInfo = {}, fromTab = '' } = this.data
    const {
      userName = '',
      name: uName = '',
      nickName = '',
    } = wInfo

    const inviteParams = {
      routerPage: `/pages/user/login/index`,
      shareFrom: EnterSource.INVITE,
      inviterStaffId: getUserId(),
      fromTab,
      QR_INVITE: "QR_INVITE",
      inviterRole: customerType,
      inviterName: `${userName || uName || nickName}`,
    }

    const channelParams = {
      params: `${qs.stringify(inviteParams)}`,
      day: -1,
    }
    let _scene = getUserId()
    const { success: pSucc, param: pData, msg: pMsg } = await addSceneParam(channelParams)
    vLog.log('INVITE addSceneParam  >>>>>', pSucc, pData, pMsg)
    if (!pSucc) {
      interaction.hideLoading()
      return interaction.showToast(pMsg || '')
    }
    _scene = pData

    const params = {
      sourceCode: global.SOURCE_CODE,
      scene: `${_scene}`,
      page: GLOBAL_START_PATH,
      env_version: envVersion
    }

    const { msg, success, param } = await getWXACode(params)
    vLog.log('INVITE  getWXACodeImg >>>>', msg, success, param)
    if (!success) {
      interaction.hideLoading()
      return interaction.showToast(msg || '')
    }

    interaction.hideLoading()
    this.setData({
      qrCode: param
    }, () => this.onPainter())
  },

  onPainter() {
    vLog.log('INVITE_PALETTE onPainter >>>>', new InviteCard({ ...this.data }).palette())
    this.setData({
      paintPalette: new InviteCard({ ...this.data }).palette(),
      building: false
    })
  },

  onImgOK(e = {}) {
    vLog.log('INVITE_PALETTE onImgOK e >>>', e)
    const { path: saveImg = '' } = e.detail
    this.setData({
      saveImg
    });
  },

  onImgErr(e = {}) {
    vLog.log('INVITE_PALETTE onImgErr e >>>>', e)
  },

  onHide() {
    setAppHoldStatus(true)
  },

  onUnload() {
    const { omitMPage = '' } = this.data
    setAppHoldStatus(false)

    if (omitMPage) {
      app.globalData.emitter.emit(REFRESH_PAGE_DATA, {
        isRefresh: true,
        pageType: 'AppAdvProfileInfo',
        registerRouter: true
      })
      return wx.navigateBack({
        delta: 2
      })
    }
  },

  onFloatClick() {
    const { floatImg } = this.data
    return wx.previewImage({
      current: floatImg,
      urls: [floatImg]
    })
  },

  onShowInviteList() {
    return wx.navigateTo({
      url: `/packages-user/pages/invite/list/index`
    })
  },

  onShareAppMessage() {
    getApp().sensors.track('invite', {
      type: "分享邀请注册"
    })
    const { inviteData } = this.data
    vLog.log('inviteData >>>', inviteData)
    vLog.log('INVITE onShareAppMessage this.data >>>', this.data)
    return {
      ...inviteData,
    }
  },

  // 批量保存图片
  onSavePhoto() {
    getApp().sensors.track('invite', {
      type: "保存图片"
    })
    const self = this
    const { saveImg = '' } = this.data
    if (!saveImg) {
      return interaction.showToast('二维码加载中，请稍后再试')
    }
    //获取相册授权
    wx.getSetting({
      success(res) {
        if (!res.authSetting['scope.writePhotosAlbum']) {
          wx.authorize({
            scope: 'scope.writePhotosAlbum',
            success() {
              // 这里是用户同意授权后的回调
              self.saveImg()
            },
            fail() { // 这里是用户拒绝授权后的回调
              self.openSetting()
            }
          })
        } else { // 用户已经授权过了
          self.saveImg()
        }
      }
    })
  },

  // 前往设置页面
  openSetting() {
    wx.showModal({
      title: '提示',
      content: '是否前往设置页面开启相册授权？',
      cancelText: '否',
      confirmText: '是',
      success(res) {
        if (res.confirm) {
          wx.openSetting({})
        } else if (res.cancel) {
        }
      }
    })
  },

  // 保存图片到本地
  saveImg() {
    const { saveImg = '' } = this.data
    wx.saveImageToPhotosAlbum({
      filePath: saveImg,
      success: function () {
        interaction.showToast('保存成功')
      },
    })
  },
});

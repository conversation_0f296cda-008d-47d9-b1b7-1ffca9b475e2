.list-container{
    padding-bottom: 10vw;
}

.invite-list{
    display: flex;
    flex-direction: column;
    width: 100%;
    padding-top: 28rpx;
}

.invite-item{
    display: flex;
    flex-direction: column;
    width: 100%;
    background-color: transparent;
    padding: 0 28rpx;

}

.item-content-block{
    display: flex;
    flex-direction: column;
    width: 100%;
    background-color: #fff;
    border-bottom: 1rpx solid #eee;
    padding: 28rpx;
}

.item-header{
    display: flex;
    flex-direction: row;
    align-items: center;
    max-height: 90rpx;
}

.item-desc{
    display: flex;
    flex-direction: column;
}

.header-img{
    width: 80rpx;
    height: 80rpx;
    border-radius: 40rpx;
    border: 1rpx solid rgba(255, 255, 255, 0.2); /* #D81830 */
}

.item-name-block{
    display: flex;
    flex-direction: column;
    margin-left: 12rpx;
}

.name-tips{
    font-size: 16Px;
    color: #333;
    font-weight: 500;
    font-family: PingFangSC-Medium;
}

.mobile-tips{
    font-size: 12Px;
    color: #696969;
    font-weight: 400;
    font-family: PingFangSC-Regular;
}

.desc-title-tips{
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: 12Px;
    color: #333;
    font-weight: 400;
    font-family: PingFangSC-Regular;
}

.empty-block{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    background-color: transparent;
}

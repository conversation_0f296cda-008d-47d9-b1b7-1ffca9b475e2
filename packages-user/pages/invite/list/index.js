import {interaction, systemtInfo, util, vLog} from "../../../../common/index.js";
import {getStaffInviteList} from "../../../../common/nb/home";
import {setAppHoldStatus} from "../../../../common/utils/userStorage";

const {rpx2px} = util

const {
  screenHeight,
  footHeight,
} = systemtInfo

Page({
  data: {
    list: [],
    title: '我的邀请记录',

    page: 0,
    pageSize: 20,
    refreshing: false,
    nomore: false,
    contentHeight: rpx2px(screenHeight - footHeight),
  },

  onLoad() {
    return this.initList()
  },

  onShow() {
    setAppHoldStatus(false)
  },

  async initList(index = 1) {
    const {page = 0, pageSize = 20, list = []} = this.data

    const {success, data, msg} = await getStaffInviteList({page, pageSize})
    this.setData({
      refreshing: false
    })
    vLog.log('INVITE_LIST getStaffInviteList success, data, msg >>>>', success, data, msg)
    if (!success || !data){
      return interaction.showToast(msg || '')
    }

    let {content = []} = data || {}
    if (content.length < pageSize){
      this.setData({
        nomore: true
      })
    }

    if (index > 1){
      content = list.concat(content)
    }

    this.setData({
      list: content,
    })
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  handlePullDownRefresh: function() {
    this.initList()
    this.setData({
      page: 0,
      nomore: false
    })
  },

  handleLoadMore: function() {
    let page = this.data.page++;
    return this.initList(page)
  },
});

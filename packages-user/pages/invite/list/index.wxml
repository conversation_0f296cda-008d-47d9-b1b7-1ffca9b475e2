<wxs src="../../../../wxs/common.wxs" module="tools"/>
<nb-page showNavBar="{{true}}" navShowBack="{{true}}" navTitle="{{title}}">
  <x-scroll-view
      class="list-container"
      style="padding-bottom: 10vw;"
      refreshing="{{refreshing}}"
      nomore="{{nomore}}"
      enableLoadMore="{{true}}"
      resetting="{{true}}"
      elementHeight="{{contentHeight}}"
      bindpulldownrefresh="handlePullDownRefresh"
      bindpulluploadmore="handleLoadMore">
    <view class="invite-list" wx:if="{{list.length>0}}">
      <view
          class="invite-item"
          wx:for="{{list}}"
          wx:for-item="item"
          wx:for-index="id"
          wx:key="id">

        <view class="item-content-block">
          <view class="item-header">
            <image src="{{item.avatar || '../../../../imgs/icon/<EMAIL>'}}" mode="aspectFill"
                   class="header-img"/>
            <view class="item-name-block">
              <view class="name-tips">{{item.name || ''}}</view>
              <view class="mobile-tips">{{item.phone || ''}}</view>
            </view>
          </view>

          <view class="item-desc">
            <view class="desc-title-tips">{{'激活时间：'}}
              <view class="mobile-tips">{{item.activatedTime || '-'}}</view>
            </view>

            <view class="desc-title-tips">{{'渠道：'}}
              <view class="mobile-tips">{{item.orgFullName || '普通客户'}}</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <view wx:else style="height: {{contentHeight}}px" class="empty-block">
      <emptyBlock tips="{{'暂无邀请记录'}}"/>
    </view>
  </x-scroll-view>
  <van-toast id="van-toast"/>
</nb-page>

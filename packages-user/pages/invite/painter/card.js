class InviteCard {
  constructor(props) {
    this.data = {...props}
  }

  palette() {
    const {
      width,
      height,
      invitationImg,
      qrCode,
      wInfo = {},
    } = this.data

    const {
      avatar = '',
      avatarUrl = '',
      userName = '',
      name = '',
      nickName = ''
    } = wInfo

    return ({
      width: `${Math.ceil(width)}px`,
      height: `${Math.ceil(height)}px`,
      views: [
        {
          id: `image-background`,
          type: 'image',
          url: `${invitationImg}`,
          css: {
            width: `${Math.ceil(width)}px`,
            height: `${Math.ceil(height)}px`,
            mode: 'aspectFill',
          },
        },
        {
          id: 'rect-user-info',
          type: 'rect',
          css: {
            width: `${Math.ceil(width * 0.8)}px`,
            height: `${Math.ceil(width * 0.8)}px`,
            position: 'absolute',
            left: `${Math.ceil(width * 0.1)}px`,
            bottom: '120px',
            borderRadius: '20px',
            color: 'white'
          }
        },
        {
          type: 'image',
          id: 'qr-image',
          url: `data:image/png;base64,${qrCode}`,
          css: {
            width: '200px',
            height: '200px',
            left: `${Math.ceil((width - 200) / 2)}px`,
            bottom: `${Math.ceil(width * 0.8) - 200 + 120 - 20}px`
          },
        },
        {
          type: 'image',
          id: 'avatar-image',
          url: `${avatar || avatarUrl}`,
          css: {
            width: '64px',
            height: '64px',
            borderRadius: '40px',
            left: `${Math.ceil(width * 0.1 + 30)}px`,
            bottom: `${Math.ceil(width * 0.8) - 300 + 120}px`
          }
        },
        {
          type: "text",
          text: `${userName || name || nickName || '普客客户'}`,
          css: {
            fontSize: "20px",
            fontWeight: 'bold',
            color: '#333',
            left: `${Math.ceil(width * 0.1 + 125)}px`,
            bottom: `${Math.ceil(width * 0.8) - 135}px`
          }
        },
        {
          type: "text",
          text: `邀请您注册「木棉花」`,
          css: {
            fontSize: "16px",
            color: '#333',
            left: `${Math.ceil(width * 0.1 + 125)}px`,
            bottom: `${Math.ceil(width * 0.8) - 160}px`
          }
        }
      ]
    })
  }
}

export default InviteCard

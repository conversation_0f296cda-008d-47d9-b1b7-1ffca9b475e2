<wxs src="../../../wxs/common.wxs" module="tools"/>
<nb-page showNavBar="{{true}}" navShowBack="{{true}}" navTitle="邀请注册">
  <view class="invite-painter">
    <painter
        customActionStyle="{{customActionStyle}}"
        palette="{{paintPalette}}"
        bind:imgOK="onImgOK"
        bind:imgErr="onImgErr"
        widthPixels="1000"
    />

    <view class="canvas-block">
      <image
          class="img-header"
          src="{{invitationImg}}"
          style="height: {{height}}px; width: {{width}}px"
          mode="aspectFill"
      />

      <view class="user-info-block">
        <image
            class="qr-code"
            src="data:image/png;base64,{{qrCode}}"
        />

        <view class="user-info">
          <image
              src="{{wInfo.avatar||wInfo.avatarUrl}}"
              class="head-code"
          />

          <view class="user-info-name">
            <view class="head-title">
              {{wInfo.userName || wInfo.name || wInfo.nickName || '-'}}
            </view>
            <view class="head-text">
              {{'邀请您注册「木棉花」'}}
            </view>
          </view>
        </view>

      </view>
    </view>
  </view>

  <view class="footer-block">
    <view class="footer-list-bar" bind:tap="onShowInviteList">
      <van-icon size="25px" name="notes-o"/>
      <view class="notes-tips">{{'邀请记录'}}</view>
    </view>

    <view class="footer-list-bar" bind:tap="onSavePhoto">
      <van-icon size="25px" name="photo-o"/>
      <view class="notes-tips">{{'保存图片'}}</view>
    </view>

    <view class="footer-invite-bar" style='background-color:{{$state.themeColor}}'>
      <button class='nb-btn block btn' open-type="share" style="background-color: transparent;border: none;">
        <view class="invite-tips">{{'邀请注册'}}</view>
      </button>
    </view>
  </view>

  <cover-view
      wx:if="{{floatTitle}}"
      class="invite-float"
      style="background-color:{{$state.themeColor}}"
      bind:tap="onFloatClick">
    <cover-view class="invite-tips">{{floatTitle}}</cover-view>
  </cover-view>

  <view class="loading-block" wx:if="{{building}}">
    <van-loading type="spinner" size="40px" color="#FFF"/>
    <view style="color: #FFF;font-size: 16Px; margin-top: 10rpx">{{progress ? progress + '%' : '生成中...'}}</view>
  </view>
</nb-page>

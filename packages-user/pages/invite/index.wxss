.head-code{
    width: 64px;
    height: 64px;
    border-radius: 40px;
}

.head-title{
    font-size: 20Px;
    font-weight: bold;
    color: #333;
}

.head-text{
    font-size: 16Px;
    color: #333;
}

.invite-float{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 0;
    right: 0;
    margin-top: 5vh;
    max-height: 10vw;
    border-bottom-left-radius: 5vw;
    border-top-left-radius: 5vw;
    padding: 10rpx 40rpx;
    z-index: 9999;
}

.invite-img{
    width: 100%;
}

.footer-block{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 98px;
    background-color: #fff;
    position: fixed;
    left: 0;
    bottom: 0;
    z-index: 9999;
}

.footer-list-bar{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    margin-left: 28rpx;
}

.notes-tips{
    font-size: 24rpx;
    color: #333;
    font-weight: 400;
    margin-top: -6rpx;
    font-family: PingFangSC-Regular;
}

.footer-invite-bar{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 60vw;
    height: 80rpx;
    border-radius: 45rpx;
    margin-right: 28rpx;
}

.invite-tips{
    font-size: 28rpx;
    color: #fff;
    font-weight: 400;
    font-family: PingFangSC-Regular;
}

.user-info-name{
    display: flex;
    flex-direction: column;
    margin-left: 20px;
}

.invite-painter{
    display: flex;
    flex-direction: column;
    width: 100vw;
    height: 100%;
}

.canvas-block{
    display: flex;
    flex-direction: column;
}

.img-header{
    width: 100%;
    height: 100%;
}

.qr-code{
    width: 200px;
    height: 200px;
    border-radius: 10px;
    margin-top: 20px;
}

.user-info{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    margin-top: 16px;
}

.user-info-block{
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 80vw;
    height: 80vw;
    background-color: #FFFFFF;
    border-radius: 20Px;
    margin-left: 10vw;
    position: absolute;
    bottom: 0;
    left: 0;
    margin-bottom: 150px;
}

.loading-block{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: absolute;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100%;
    z-index: 9999;
    background-color: rgba(0, 0, 0, 0.55);
}

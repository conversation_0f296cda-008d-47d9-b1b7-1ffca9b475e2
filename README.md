#WBS-wechat-investor

###Action地址配置说明

####营销线索

    action://share/AppAdvMarketingClue

####智能名片

    action://share/AppAdvCard

####邀请注册

path: pages/loginAndRegist/invite/index

    action://share/AppAdvRegister

####Bi看板

    action://share/AppAdvBiBlock

####活动拉新

path: pages/loginAndRegist/activity/activity

    action://share/AppAdvActivity

####营销资料、基金解读、股票、运作报告等搜索页面

path: pages/common/search/search

    action://share/SearchList
    params: placeholder,channelIds,keyword
    
    eg:基金解读（keyword 为空格，搜全量数据）
    action://share/SearchList?placeholder=基金解读&keyword= &channelIds=["fundProductGf"]
    eg:股票
    action://share/SearchList?placeholder=股票&keyword=270002&channelIds=["stockGf"]
    eg:运作列表
    action://share/SearchList?placeholder=运作报告&keyword= &channelIds=["reportGf"]
    eg:营销资料、基金解读、股票、运作报告
    action://share/SearchList?placeholder=${title}&keyword=${keyword}&channelIds=["marketPlan","fundProductGf”,"stockGf","reportGf"]

####定投择时器

    action://share/wechatFixedInvestmentCalculator

####定投择基器

    action://share/wechatProfitProbabilityCalculator

####内容库

    action://share/ContentLibrary

####分享企业微信

    action://share/shareQY?type=  1  2  3

####定投解套器

    action://share/advFixedCaster
    
####目标盈定投
    
    action://share/advTargetProfit
    
####定投计算器

    action://share/advFixedCompass    

####喜报

    action://share/calcGladPosterMaker   

####定投罗盘

path: pages/mine/compass/index

    action://share/AppAdvCompass

####跳转其他小程序、H5（跳转/半屏打开）

    action://share/AppAdvOpenMini

- 规则：
  1. 其他参数正常拼接，path值需要通过encodeURIComponent转译

- 目标小程序 appid

- 版本标识  envVersion
      envVersion = {
        develop, // 开发版
        trial, // 体验版
        release // 正式版
      }
  - 跳转方式 openWay
    1. jump
    2. half
    需要通过shortLink目标地址
        // 列如：
        action://share/AppAdvOpenMini?appid=wx9aa09d80fda0f087&envVersion=trial&openWay=half&path=encodeURIComponent(【shortLink的值，例如：shortLink: '#小程序://E点通测试/客户列表/pWgsVMhiqeXO1UB'，取【#小程序://E点通测试/客户列表/pWgsVMhiqeXO1UB】，不带单引号，没有空格，没有前后'【】'】)
        🔽
        action://share/AppAdvOpenMini?appId=wx9aa09d80fda0f087&envVersion=trial&openWay=half&path=%23%E5%B0%8F%E7%A8%8B%E5%BA%8F%3A%2F%2FE%E7%82%B9%E9%80%9A%E6%B5%8B%E8%AF%95%2F%E5%9F%BA%E9%87%91%E8%A7%A3%E8%AF%BB%2F5i0YjVbGwNo08CF
        
        action://share/AppAdvOpenMini?appId=wx9aa09d80fda0f087&envVersion=trial&openWay=jump&path=%23%E5%B0%8F%E7%A8%8B%E5%BA%8F%3A%2F%2FE%E7%82%B9%E9%80%9A%E6%B5%8B%E8%AF%95%2F%E5%9F%BA%E9%87%91%E8%A7%A3%E8%AF%BB%2F5i0YjVbGwNo08CF
    
    3. jumpOthers
    需要通过path目标地址
        action://share/AppAdvOpenMini?appId=wxbd5e15b5e756b177&envVersion=release&openWay=jumpOthers&path=pages/index/index
        🔽
        action://share/AppAdvOpenMini?appId=wxbd5e15b5e756b177&envVersion=release&openWay=jumpOthers&path=pages%2Findex%2Findex
  - 参考文档
  1. 在线encodeURIComponent
  2. wx.navigateToMiniProgram
  3. wx.openEmbeddedMiniProgram

####WeData 上报
```javascript
break_in_wx_login
break_in_init_unionid
break_in_get_staff_auth_record
break_in_get_page_template_info
break_in_get_template_by_id
break_in_check_login
app_get_page_template_info
app_get_template_by_id
convert_get_page_template_info
convert_get_template_by_id
start_up_android_reload
start_up_get_scene_param
start_up_init_config
login_init_blackbox
init_unionid
login_wx_login
login_get_uc_system_api_config
login_get_user_profile
login_rebinding
login_get_adv_staff_card_info
login_params
login_res
login_verify_fail
login_send_sms_code
register_get_all_display_node
register_get_uc_system_api_config
register_init_blackbox
register_verify_fail
register_save_place_by_point
register_exchange_params
register_do_submit_params
register_res
register_reroute_target_page_params
register_get_query_org_node
register_get_user_profile
register_get_unionid
register_wx_login
register_find_last_node
agency_register_init_blackbox
agency_register_verify_fail
agency_register_exchange_params
agency_register_do_submit_params
agency_register_res
agency_register_get_user_profile
agency_register_get_unionid
agency_register_wx_login
agency_register_reroute_target_page_params
agency_register_get_agency_list
normal_register_init_blackbox
normal_register_verify_fail
normal_register_do_submit_params
normal_register_res
normal_register_get_user_profile
normal_register_get_unionid
normal_register_wx_login
normal_register_reroute_target_page_params
role_register_role_params
role_choose_role_check_login
hold_up_check_login
hold_up_check_login_res
union_list_get_unionid
union_list_wx_login
topic_do_refresh_info
webview_init_res_info
webview_init_final_url
webview_to_mini_program
webview_post_message_event_org
webview_post_message_data
webview_share_info
webview_load_done_time
webview_load_error_url
utils_open_file

newLogin
setIdExchange
doRegister
doAgencyRegister
getUnionId
getPageTemplateInfo
getStaffAuthRecord

```


####附录

#####A. 常用测试跳转小程序信息

    广发基金木棉花(对方生产环境)
    APPID: wxf9d1a7ff0a2ba02b
    
    组合健诊: {
          appId: 'wxf9d1a7ff0a2ba02b',
          shortLink: '#小程序://广发基金木棉花/选择投资期望/iwI7yQ6mRNiROEx' 
        }
    木棉智投: {
          appId: 'wxf9d1a7ff0a2ba02b',
          shortLink: '#小程序://广发基金木棉花/客户列表/dzm2j84n9rhOXia' 
        }
    基金解读: {
          appId: 'wxf9d1a7ff0a2ba02b',
          shortLink: '#小程序://广发基金木棉花/基金解读/bIqwdwH6ZlfFPPu'
        }

    E点通测试(对方测试环境)
    APPID: wx9aa09d80fda0f087
    
    基金解读: {
          appId: 'wx9aa09d80fda0f087',
          shortLink: '#小程序://E点通测试/基金解读/5i0YjVbGwNo08CF' 
    }
    组合健诊: {
          appId: 'wx9aa09d80fda0f087',
          shortLink: '#小程序://E点通测试/选择投资期望/Av9b6yp6hFDYzQh' 
        }
    木棉智投: {
          appId: 'wx9aa09d80fda0f087',
          shortLink: '#小程序://E点通测试/客户列表/pWgsVMhiqeXO1UB' 
        }


### UI组件
以按钮组件为例，只需要在 json 文件中引入按钮对应的自定义组件即可

```json
{
  "usingComponents": {
    "van-button": "/path/to/vant-weapp/dist/button/index"
  }
}
```
接着就可以在 wxml 中直接使用组件

```vue
<van-button type="primary">按钮</van-button>
```

[查看更多](https://github.com/youzan/vant-weapp)

### 全局状态
> 可用功能:
> 1.全局状态state支持所有Page和Component，状态完全同步，可使用api更新状态
> 2.周期监听pageLisener能监听所有页面的onLoad，onShow等周期事件
> 3.全局事件methods，全局可用的方法。
> 通常为全局可用信息配置，尽量少，负责更新state刷新页面会缓慢
> 目前全局UI、用户信息、Tabar通过

**wxml中使用**

```jsx
<view>{{$state.user.name}}：{{$state.msg}}</view>
```

**template文件中使用**(需在属性data中引用$state)

```html
 <!-- 这是一个template -->
  <template name="t1">
    <view>{{$state.msg}}</view>
  </template>

<!-- 这是引用位置 -->
  <template is="t1" data="{{$state,arg1,arg2}}" />
<!--   相当于<template is="t1" data="{{$state:$state,arg1:arg1,arg2:arg2}}" /> -->
```
**修改状态**

```javascript
const app = getApp()
Page({
  data: {

  },
  onLoad: function () {
    //所有wxml中的$state.msg会同步更新
    app.store.setState({
       msg: "我被修改了，呜呜..."
    });
  }
});
```

**周期监听**(详情见/common/store/index.js 文件)

```javascript
// store中
let store = new Store({
    //状态
    state: {
		//...
    },
    //方法
    methods: {
		//...
    },
    //页面监听
    pageLisener: {
        onLoad(options){
            console.log('我在' + this.route, '参数为', options);
        }
    }
})
```

> 先执行pageLisener监听，后执行原本页面中周期。
还支持其他周期事件 ['onLoad', 'onShow', 'onReady', 'onHide', 'onUnload', 'onPullDownRefresh', 'onReachBottom', 'onShareAppMessage', 'onPageScroll', 'onTabItemTap']

### 自定义组件
> 尽量将所有可见Ui都封装为组件，封装原则是单一责任、UI样式可通过Json动态配置、复用性强、可替换

**nb-page**
一般性页面wxml根节点。通常添加所有页面都可用组件和方法。
**navigation**
页面导航
**tabbar**
底部tabbar
**image**
封装oss图片加载
**nb-background**
通用背景色，通过变量控制模式 纯色-渐变-图片
**nb-card**
卡片模式组件
...

### css库
### 事件管理
```perl
common/event
```
### 工具类
```perl
common/utils
```
### 常量
```perl
common/const
```
事件管理、工具类、常量统一由common下的index.js文件导出，使用的时候只需要引用这个index文件即可，示例代码：

```javascript
import {
  userStorage,
  md5,
  base64,
  format,
} from '../common/index.js'

```
### 网络请求
网络请求沿用了RN端的请求方式，无缝对接。
示例代码如下：

**1 在api.js里面先export要请求的api方法**
```javascript
export function login(data) {
  return post('investor/login.json', data, false, true)
}
```
## 业务说明

## 版本维护
<details>
<summary>展开查看</summary>
<pre><code>
**v2.4.1**
1. 投教
2. 小程序转发服务产品
3. 顾问首页增加推荐内容
> https://jira.newbanker.cn/browse/WBS-10583


活动二维码生成
1. 
curl --location --request GET 'https://aim-share-dev.gffunds.com.cn/marketing-api/api/v1/wechat/addString?day=-1&params=H5' \
--header 'Connection: keep-alive' \
--header 'Pragma: no-cache' \
--header 'Cache-Control: no-cache' \
--header 'Accept: application/json;charset=UTF-8' \
--header 'X-Requested-With: XMLHttpRequest' \
--header 'User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2307260 MicroMessenger/8.0.5 webview/' \
--header 'token: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJjbGllbnQiOiIxMzEiLCJ0b2tlbl90eXBlIjoidG9rZW4iLCJ1c2VyTmFtZSI6Ium7hOWugSIsInVzZXJJZCI6IjM1ODU1NTE3MDIwMjA1MjUiLCJ1c2VyIjoiMzAwIiwiYWNjb3VudCI6IjM1ODU1NTE3MDIwMjA1MjYiLCJpYXQiOjE3MDA3MDU1MDMsIm5iZiI6MTcwMDcwNTUwMywiZXhwIjoxNzAxMzEwMzAzfQ.2FtcIOkJKvLgyg-1dojPyZzszCtl09-JeafkQFwxXoI' \
--header 'content-type: application/json;charset=utf-8' \
--header 'Sec-Fetch-Site: cross-site' \
--header 'Sec-Fetch-Mode: cors' \
--header 'Sec-Fetch-Dest: empty' \
--header 'Referer: https://servicewechat.com/wx3501ac822988bb64/devtools/page-frame.html'

curl --location --request GET 'https://aim-share-dev.gffunds.com.cn/marketing-api/api/v1/wechat/addString?day=-1&params=type=ACTIVITY&fromTab=HOME&routerStatus=MiniCode&routerPage=pages%2Fcommon%2Fintroduce%2Findex' \

curl --location --request GET 'https://aim-share-dev.gffunds.com.cn/marketing-api/api/v1/wechat/addString?day=-1&params=type=ACTIVITY&fromTab=HOME&routerStatus=MiniCode&routerPage=pages%2FloginAndRegist%2Factivity%2Factivity' \

2. 
curl --location --request POST 'https://aim-share-dev.gffunds.com.cn/marketing-api/api/v1/wechat/wxacode' \
--header 'Content-Type: application/json' \
--data-raw '{
    "sourceCode": "wbs_investor",
    "scene": "AC6D62B18056F67D1E08A7783F9BBE93",
    "page": "pages/loginAndRegist/startUp/index",
    "env_version": "release"
}'

3. 把base64转换成图片
</code></pre>
</details>


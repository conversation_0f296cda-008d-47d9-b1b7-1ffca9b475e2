<!--
此文件为开发者工具生成，生成时间: 2022/4/18 下午2:12:37
使用方法：
在 /Users/<USER>/Desktop/work/wbs-wechat-investor/pages/viewPoint/index.wxml 引入模板

```
<import src="index.skeleton.wxml"/>
<template is="skeleton" wx:if="{{loading}}" />
```

在 /Users/<USER>/Desktop/work/wbs-wechat-investor/pages/viewPoint/index.wxss 中引入样式
```
@import "./index.skeleton.wxss";
```

更多详细信息可以参考文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/skeleton.html
-->
<template name="skeleton">
  <view class="sk-container">
    <view is="components/nb-page/nb-page">
      <view class="container page--container" style="background-color:#f7f7f7;">
        <scroll-view scroll-y="true" style="height:642px;margin-top:88px; margin-bottom:82px;">
          <view is="components/x-scroll-view/x-scroll-view" class="list-container">
            <view class="scroll-view--refresh-block scroll-view--x-common" style="height:0px;">
              <view>
                <image class="scroll-view--refresh sk-image"></image>
              </view>
              <view>
                <text class="sk-transparent sk-text-18-7500-840 sk-text">下拉刷新</text>
              </view>
              <view></view>
            </view>
            <scroll-view class="scroll-view--scroll_container" scroll-top="0" scroll-y="true" style="position:fixed;width:100%;left:0;height:642px;top:88px;bottom:0px;">
              <view style="min-height:100%;position: absolute;width:100%;">
                <view>
                  <view is="components/polymers/polymers">
                    <view class="polymers--polymers-block">
                      <view is="components/polymers/cards/content/content">
                        <view is="components/nb-card/nb-card">
                          <view class="nb-card card--nb-card case card--case ">
                            <view class="nb-item card--nb-item ">
                              <view is="components/polymers/components/titleBar/titleBar">
                                <view class="titleBar--float-title-block" style="margin-top: 20px">
                                  <view class="titleBar--title-left-block" style="margin-left:0px;margin-right:0px;">
                                    <text class="titleBar--title-txt sk-transparent sk-text-18-7500-527 sk-text" style="margin-left:0px;">市场解读</text>
                                    <text class="titleBar--subTitle-txt sk-transparent sk-text-18-7500-790 sk-text">广发基金独家观点</text>
                                  </view>
                                  <view class="titleBar--title-right-block" data-info="[object Object]">
                                    <text class="titleBar--more-txt sk-transparent sk-text-18-7500-380 sk-text">更多</text>
                                    <view is="miniprogram_npm/vant-weapp/icon/index" class="titleBar--title-next-icon">
                                      <view class="van-icon icon-index--van-icon van-icon-arrow icon-index--van-icon-arrow sk-pseudo sk-pseudo-circle" style="color: #999;font-size: 12px;"></view>
                                    </view>
                                  </view>
                                </view>
                              </view>
                              <view class="content--content-nav" style="margin-top: 0px">
                                <view is="components/scrollable-tabview/scrollable-tabview" class="content--current-item">
                                  <view class="tabview--container tabview--navBar" style="background-color:#fff;">
                                    <scroll-view scroll-with-animation="true" scroll-x="true" scroll-into-view="tabbar0">
                                      <view class="tabview--tab sk-transparent" data-index="0" data-value="0" id="70ee033c--tabbar0" style="font-weight:500;color: #333;background-color:#fff;padding-top: 10px;">
                                        行业主题
                                        <view class="tabview--indicator" style="background-color:rgba(242, 77, 40, 1);margin-top:5px"></view>
                                      </view>
                                      <view class="tabview--tab sk-transparent" data-index="1" data-value="1" id="70ee033c--tabbar1" style="font-weight:normal;color: #999;background-color:#fff;padding-top: 10px;">
                                        A股市场
                                        <view class="tabview--indicator" style="background-color:;margin-top:5px"></view>
                                      </view>
                                      <view class="tabview--tab sk-transparent" data-index="2" data-value="2" id="70ee033c--tabbar2" style="font-weight:normal;color: #999;background-color:#fff;padding-top: 10px;">
                                        债券市场
                                        <view class="tabview--indicator" style="background-color:;margin-top:5px"></view>
                                      </view>
                                      <view class="tabview--tab sk-transparent" data-index="3" data-value="3" id="70ee033c--tabbar3" style="font-weight:normal;color: #999;background-color:#fff;padding-top: 10px;">
                                        海外市场
                                        <view class="tabview--indicator" style="background-color:;margin-top:5px"></view>
                                      </view>
                                    </scroll-view>
                                    <view class="tabview--border-line"></view>
                                  </view>
                                </view>
                              </view>
                              <view class="content--content-list">
                                <view is="components/polymers/items/news/news">
                                  <view class="news--card" style="border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;border-bottom-width: 1px;border-top-left-radius: 0px;border-top-right-radius: 0px;">
                                    <view class="news--card-box" data-item="[object Object]" style="margin-top:0px;padding-top:0px;border-bottom-color: rgba(238, 238, 238, 0.3)">
                                      <view class="news--card-left">
                                        <view class="news--card-left-title news--text-two-line sk-transparent sk-text-18-7500-911 sk-text">每周行业快评——TMT（2022.4.18）</view>
                                        <view class="news--card-news-bottom">
                                          <view class="news--card-subtitle-txt news--text-one-line sk-transparent sk-text-18-7500-578 sk-text">
                                            20220411-20220415TMT行业快评
                                          </view>
                                        </view>
                                      </view>
                                      <image class="news--image sk-image" mode="aspectFill"></image>
                                    </view>
                                  </view>
                                </view>
                              </view>
                              <view class="content--content-list">
                                <view is="components/polymers/items/news/news">
                                  <view class="news--card" style="border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;border-bottom-width: 1px;border-top-left-radius: 0px;border-top-right-radius: 0px;">
                                    <view class="news--card-box" data-item="[object Object]" style="margin-top:0px;padding-top:14px;border-bottom-color: rgba(238, 238, 238, 0.3)">
                                      <view class="news--card-left">
                                        <view class="news--card-left-title news--text-two-line sk-transparent sk-text-18-7500-276 sk-text">每周行业快评——制造业（2022.4.18）</view>
                                        <view class="news--card-news-bottom">
                                          <view class="news--card-subtitle-txt news--text-one-line sk-transparent sk-text-18-7500-721 sk-text">
                                            20220411-20220415制造业行业快评
                                          </view>
                                        </view>
                                      </view>
                                      <image class="news--image sk-image" mode="aspectFill"></image>
                                    </view>
                                  </view>
                                </view>
                              </view>
                              <view class="content--content-list">
                                <view is="components/polymers/items/news/news">
                                  <view class="news--card" style="border-bottom-left-radius: 6px;border-bottom-right-radius: 6px;border-bottom-width: 0px;border-top-left-radius: 0px;border-top-right-radius: 0px;">
                                    <view class="news--card-box" data-item="[object Object]" style="margin-top:0px;padding-top:14px;border-bottom-color: #fff">
                                      <view class="news--card-left">
                                        <view class="news--card-left-title news--text-two-line sk-transparent sk-text-18-7500-694 sk-text">每周行业快评——周期（2022.4.18）</view>
                                        <view class="news--card-news-bottom">
                                          <view class="news--card-subtitle-txt news--text-one-line sk-transparent sk-text-18-7500-410 sk-text">
                                            20220411-20220415周期行业快评
                                          </view>
                                        </view>
                                      </view>
                                      <image class="news--image sk-image" mode="aspectFill"></image>
                                    </view>
                                  </view>
                                </view>
                              </view>
                            </view>
                          </view>
                        </view>
                      </view>
                    </view>
                    <view class="polymers--polymers-block">
                      <view is="components/polymers/cards/content/content">
                        <view is="components/nb-card/nb-card">
                          <view class="nb-card card--nb-card case card--case ">
                            <view class="nb-item card--nb-item ">
                              <view is="components/polymers/components/titleBar/titleBar">
                                <view class="titleBar--float-title-block" style="margin-top: 10px">
                                  <view class="titleBar--title-left-block" style="margin-left:0px;margin-right:0px;">
                                    <text class="titleBar--title-txt sk-transparent sk-text-18-7500-921 sk-text" style="margin-left:0px;">基金经理说</text>
                                    <text class="titleBar--subTitle-txt sk-transparent sk-text-18-7500-405 sk-text">基金经理市场观点</text>
                                  </view>
                                  <view class="titleBar--title-right-block" data-info="[object Object]">
                                    <text class="titleBar--more-txt sk-transparent sk-text-18-7500-215 sk-text">更多</text>
                                    <view is="miniprogram_npm/vant-weapp/icon/index" class="titleBar--title-next-icon">
                                      <view class="van-icon icon-index--van-icon van-icon-arrow icon-index--van-icon-arrow sk-pseudo sk-pseudo-circle" style="color: #999;font-size: 12px;"></view>
                                    </view>
                                  </view>
                                </view>
                              </view>
                              <view class="content--content-nav" style="margin-top: 0px">
                                <view is="components/scrollable-tabview/scrollable-tabview" class="content--current-item">
                                  <view class="tabview--container tabview--navBar" style="background-color:#fff;">
                                    <scroll-view scroll-with-animation="true" scroll-x="true" scroll-into-view="tabbar0">
                                      <view class="tabview--tab sk-transparent" data-index="0" data-value="0" id="c7165aae--tabbar0" style="font-weight:500;color: #333;background-color:#fff;padding-top: 10px;">
                                        刘格菘
                                        <view class="tabview--indicator" style="background-color:rgba(242, 77, 40, 1);margin-top:5px"></view>
                                      </view>
                                      <view class="tabview--tab sk-transparent" data-index="1" data-value="1" id="c7165aae--tabbar1" style="font-weight:normal;color: #999;background-color:#fff;padding-top: 10px;">
                                        李巍
                                        <view class="tabview--indicator" style="background-color:;margin-top:5px"></view>
                                      </view>
                                      <view class="tabview--tab sk-transparent" data-index="2" data-value="2" id="c7165aae--tabbar2" style="font-weight:normal;color: #999;background-color:#fff;padding-top: 10px;">
                                        武幼辉
                                        <view class="tabview--indicator" style="background-color:;margin-top:5px"></view>
                                      </view>
                                      <view class="tabview--tab sk-transparent" data-index="3" data-value="3" id="c7165aae--tabbar3" style="font-weight:normal;color: #999;background-color:#fff;padding-top: 10px;">
                                        李耀柱
                                        <view class="tabview--indicator" style="background-color:;margin-top:5px"></view>
                                      </view>
                                      <view class="tabview--tab sk-transparent" data-index="4" data-value="4" id="c7165aae--tabbar4" style="font-weight:normal;color: #999;background-color:#fff;padding-top: 10px;">
                                        杨喆
                                        <view class="tabview--indicator" style="background-color:;margin-top:5px"></view>
                                      </view>
                                      <view class="tabview--tab sk-transparent" data-index="5" data-value="5" id="c7165aae--tabbar5" style="font-weight:normal;color: #999;background-color:#fff;padding-top: 10px;">
                                        林英睿
                                      </view>
                                    </scroll-view>
                                    <view class="tabview--border-line"></view>
                                  </view>
                                </view>
                              </view>
                              <view class="content--content-list">
                                <view is="components/polymers/items/news/news">
                                  <view class="news--card" style="border-bottom-left-radius: 6px;border-bottom-right-radius: 6px;border-bottom-width: 0px;border-top-left-radius: 0px;border-top-right-radius: 0px;">
                                    <view class="news--card-box" data-item="[object Object]" style="margin-top:0px;padding-top:0px;border-bottom-color: #fff">
                                      <view class="news--card-left">
                                        <view class="news--card-left-title news--text-two-line sk-transparent sk-text-18-7500-962 sk-text">见证时间的力量丨广发基金刘格菘：乘全球比较优势制造业东风</view>
                                        <view class="news--card-news-bottom">
                                          <view class="news--card-subtitle-txt news--text-one-line sk-transparent sk-text-18-7500-569 sk-text">
                                            用价值投资的方法做成长投资
                                          </view>
                                        </view>
                                      </view>
                                      <image class="news--image sk-image" mode="aspectFill"></image>
                                    </view>
                                  </view>
                                </view>
                              </view>
                            </view>
                          </view>
                        </view>
                      </view>
                    </view>
                    <view class="polymers--polymers-block">
                      <view is="components/polymers/cards/content/content">
                        <view is="components/nb-card/nb-card">
                          <view class="nb-card card--nb-card case card--case ">
                            <view class="nb-item card--nb-item ">
                              <view is="components/polymers/components/titleBar/titleBar">
                                <view class="titleBar--float-title-block" style="margin-bottom: 14px;margin-top: 10px">
                                  <view class="titleBar--title-left-block" style="margin-left:0px;margin-right:0px;">
                                    <text class="titleBar--title-txt sk-transparent sk-text-18-7500-953 sk-text" style="margin-left:0px;">基金推荐</text>
                                    <text class="titleBar--subTitle-txt sk-transparent sk-text-18-7500-413 sk-text">季度产品推荐</text>
                                  </view>
                                  <view class="titleBar--title-right-block" data-info="[object Object]">
                                    <text class="titleBar--more-txt sk-transparent sk-text-18-7500-746 sk-text">更多</text>
                                    <view is="miniprogram_npm/vant-weapp/icon/index" class="titleBar--title-next-icon">
                                      <view class="van-icon icon-index--van-icon van-icon-arrow icon-index--van-icon-arrow sk-pseudo sk-pseudo-circle" style="color: #999;font-size: 12px;"></view>
                                    </view>
                                  </view>
                                </view>
                              </view>
                              <view class="content--content-nav" style="margin-top: 0px"></view>
                              <view class="content--content-list">
                                <view is="components/polymers/items/news/news">
                                  <view class="news--card" style="border-bottom-left-radius: 6px;border-bottom-right-radius: 6px;border-bottom-width: 0px;border-top-left-radius: 6px;border-top-right-radius: 6px;">
                                    <view class="news--card-box" data-item="[object Object]" style="margin-top:-6px;padding-top:12px;border-bottom-color: #fff">
                                      <view class="news--card-left">
                                        <view class="news--card-left-title news--text-two-line sk-transparent sk-text-18-7500-110 sk-text">推荐长图</view>
                                      </view>
                                      <image class="news--image sk-image" mode="aspectFill"></image>
                                    </view>
                                  </view>
                                </view>
                              </view>
                            </view>
                          </view>
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </scroll-view>
          </view>
          <view is="miniprogram_npm/vant-weapp/toast/index" id="van-toast">
            <view is="miniprogram_npm/vant-weapp/transition/index"></view>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
</template>
.list-container{
    padding-bottom: 20vh;
}

.background{
    width: 100%;
    height: 100%;
    position: relative;
}

.bgImg{
    width: 100%;
    height: 100%;
}

.card-container{
    position: absolute;
    background-color: #fff;
    top: 30%;
    left: 5%;
    width: 90%;
    border-radius: 8rpx;
    padding: 48rpx 0 0 0;
}

.footer{
    display: flex;
    flex-direction: row;
    font-size: 26rpx;
    line-height: 36rpx;
    color: #969696;
    margin-top: 46rpx;
    margin-left: 28rpx;
}

.protocal{
    font-weight: 500;
}

.van-checkbox__icon{
    border-radius: 4rpx;
}

.btn{
    margin: 48rpx 0;
    height: 100rpx;
    border-radius: 50rpx;
}

.modal-footer{
    display: flex;
    height: 100rpx;
    flex-direction: row;
    align-items: center;
    font-size: 32rpx;
}

.modal-btn{
    width: 50%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-title{
    font-size: 32rpx;
    font-weight: 500;
}

.modal-sub{
    font-size: 26rpx;
    margin-top: 28rpx;
}

.modal-btn-text{
    color: orange;
}

.bigTitle{
    font-size: 48rpx;
    margin-left: 28rpx;
}

.img-box{
    width: 90%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    margin: 60rpx 5%;
}

.img-logo{
    width: 60%;
    height: 120rpx;
}

.img-title{
    font-size: 36rpx;
    color: #969696;
}

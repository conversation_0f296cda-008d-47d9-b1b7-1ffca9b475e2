<nb-page
    navShowBack="{{true}}"
    contentUseView="{{false}}"
    navTitle=""
    inHomePage="{{false}}"
    showTabBar="{{false}}">

  <tdcaptcha id="td-captcha"/>
  <view class='background'>
    <image class="bgImg" src="{{sysInfo.staff.loginImg}}?x-oss-process=image/resize,m_fill,h_1460,w_828/format,jpg"/>
  </view>
  <nb-card card-class="card-container" style="top:'30%'">
    <van-cell-group custom-class="cell" border="{{false}}">
      <van-field
          type="number"
          label=""
          placeholder="请输入此微信绑定的手机号"
          maxlength="11"
          clearable
          value="{{phone}}"
          bind:input="SMS_InputPhone"
      />
      <van-field
          label=""
          placeholder="请输入短信验证码"
          clearable
          maxlength="6"
          use-button-slot
          bind:input="SMS_InputCode"
          bind:blur="SMS_InputCodeBlur"
          border="{{ false }}">
        <view
            slot="button"
            bindtap='SMS_SendCode'
            style='color:{{(!sendLoading && phone.length===11) ?$state.themeColor: "#969696"}}'>
          {{verifyCode}}
        </view>
      </van-field>
    </van-cell-group>

    <view class='footer'>
      <van-checkbox
          shape="round"
          value="{{isAgree}}"
          checked-color="{{$state.themeColor}}"
          bind:change="onChange">
      </van-checkbox>
      <view style='margin-left: 18rpx;'>
        {{agreement[0]}}
        <text style='color:{{$state.themeColor}}'
              data-url='/packages-user/pages/protocol/protocol?name={{agreement[1]}}'
              bindtap='goAnyWhere'>《{{agreement[1]}}》</text>
      </view>
    </view>
    <button class='nb-btn block btn'
            disabled='{{disabled}}'
            style='background-color:{{$state.themeColor}}'
            bindtap='getUserProfile'>
      <text wx:if="{{btnLoading}}" class='icon-loading2 iconfont-spin'></text>{{btnCode}}
    </button>
    <view class="noLogin"><text class="text" style='border-bottom-color:{{$state.themeColor}};color:{{$state.themeColor}};' bind:tap="back">暂不登录</text></view>
  </nb-card>
  <van-toast id="van-toast"/>
</nb-page>


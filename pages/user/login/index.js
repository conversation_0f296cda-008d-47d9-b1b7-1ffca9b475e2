import {
  validator,
  countDown,
  enums,
  eventName,
  global,
  storage,
  interaction,
  util,
  wbs,
  qs,
  vLog
} from '../../../common/index.js'

import {
  getUnionID,
  getOpenId,
  getToken,
  getWechatInfoId,
  setAppHoldStatus,
  getFirstPath,
  setSystemInfo,
  setUser,
  getUser,
  setSaveUserInfo,
  setViolationsPhone,
  getSystemInfo,
  setUserRole,
  setRouterInRegister,
  getCurrRoleType,
  getCustomerTypeInt,
  setUserLogin,
  setToken,
  setUserId,
  setOpenId,
  setUnionID,
  setWechatInfoId,
} from "../../../common/utils/userStorage";

import {
  getUnionId,
  reBinding,
  newLogin,
  checkLogin,
  gflogincode,
  getUCSystemApiConfig,
  getAdvStaffCardInfo,
} from '../../../common/nb/home'

import sysConfig from '../../../common/const/local.system.config'

const {
  count_down,
  clear_count
} = countDown

const {
  LoginState,
  FILTER_KEYS_SHARE_LIST,
  ROLE_TYPE
} = enums

const {
  SEND_REGISTER_OPTIONS,
  REFRESH_PAGE_DATA
} = eventName

const {
  isEmptyObject,
  doUrlDeCode,
  doJSON_PARSE,
  panDClone,
  repairLaunchPath
} = util

const fpPlugin = requirePlugin('tdfp-plugin')
const app = getApp()

Page({
  data: {
    sysInfo: {},
    phone: '',
    sendLoading: false,
    verifyCode: '获取验证码',
    isAgree: false,
    agreement: [],
    disabled: true,
    btnCode: '立即登录使用',
    btnLoading: false,
  },
  _data: {
    SMS_Code: '',
    loginCode: LoginState.ABSENCE, // 默认状态

    shouldLogin: '',
    shouldInvite: '',

    faId: '', //邀请注册id
    inviterName: '', //邀请注册人name
    inviterRole: '',
    blackBox: '',
    captchaToken: '',
    fromPage: '',
    rolePage: '',

    authInfo: {},
    pervParams: {},
    wxInfo: {},
    sParams: null,
    casterParams: {},
    signinTypeStr: ""
  },

  onLoad(opts = {}) {
    vLog.info('LOGIN options >>>>', opts).report()
    const {
      inviterStaffId = '',
      inviterName = '',
      inviterRole = '',
      shareParams = '',
      targetPath = '',
      trunk = '',
      shouldLogin = '',
      fromPage = '',
      rolePage = ''
    } = opts || {}
    let faId = inviterStaffId || ''
    const signinTypeStr = inviterStaffId ? '二维码邀请' : ''
    let fragment = new fpPlugin.FMAgent(app.globalData._fmOpt)
    this._data = {
      ...this._data,
      faId,
      inviterRole,
      shouldLogin,
      inviterName,
      signinTypeStr,
    }
    this.getConfigType()
    vLog.info('LOGIN onLoad shareParams >>>>', shareParams).report()

    if (shareParams) {
      let sParams = qs.parse(decodeURIComponent(shareParams) || "")
      const { hasInvite: shouldInvite = '' } = sParams || {}
      vLog.log('LOGIN onLoad sParams >>>', sParams)

      this._data = {
        ...this._data,
        sParams,
        shouldInvite
      }
      return this.initRegisterOptions(sParams)
    }

    vLog.log('LOGIN onLoad trunk >>> ', trunk)
    if (trunk) {
      const casterParams = storage.getStorage(global.STORAGE_GLOBAL_CASTER_PARAMS) || {}
      this._data = {
        ...this._data,
        casterParams,
      }
      return this.initRegisterOptions(casterParams, opts)
    }

    const eventChannel = this.getOpenerEventChannel()
    vLog.log('LOGIN onLoad eventChannel >>> ', eventChannel && eventChannel.hasOwnProperty('on')).report()
    if (eventChannel && eventChannel.hasOwnProperty('on')) {
      eventChannel.on(SEND_REGISTER_OPTIONS, (data) => {
        vLog.log('LOGIN SEND_REGISTER_OPTIONS data >>>', data)
        if (data) {
          this.initRegisterOptions(data, opts)
        }
      })
    }
    vLog.info('LOGIN onLoad targetPath >>> ', targetPath).report()
    if (targetPath) {
      let options = {}
      for (const item of Object.entries(qs.parse(opts))) {
        let [key, value] = item
        key = doUrlDeCode(key)
        value = doUrlDeCode(value)
        options[key] = value
      }
      vLog.log('LOGIN targetPath options >>>', options)
      return this.initRegisterOptions(options, opts)
    }

    if (fragment) {
      this.initBlackbox(fragment)
    }

    if (fromPage && fromPage == 'LOTTERY') {
      this._data.fromPage = 'LOTTERY'
    }

    if (rolePage) {
      this._data.rolePage = rolePage
    }

  },

  initBlackbox(fragment) {
    let that = this
    fragment && fragment.getInfo({
      page: that,
      mode: 'plugin',
      noClipboard: true,
      openid: getOpenId(),
      unionid: getUnionID(),
      success: function (blackBox) {
        vLog.log('LOGIN initBlackbox blackBox >>>', blackBox)
        that._data = {
          ...that._data,
          blackBox
        }
      },
      fail: function (err) {
        vLog.error('LOGIN onLoad initBlackbox failed >>>', err).report()
      }
    })
  },

  async initRegisterOptions(data, options) {
    vLog.log('LOGIN initRegisterOptions data, options >>>>', data, options)
    let pervParams = {
      ...options,
      ...data
    }

    vLog.log('LOGIN initRegisterOptions pervParams >>', pervParams)
    this._data.pervParams = pervParams
    this.code_Login()
  },

  //获取UnionId
  async onGetUnionId(wxInfoRes = {}) {
    vLog.log('LOGIN onGetUnionId this.data  >>>>', this._data)
    vLog.info('LOGIN onGetUnionId wxInfoRes >>>>', wxInfoRes).report()
    const that = this
    if (!isEmptyObject(wxInfoRes)) {
      let wInfo = getUser()
      wInfo = {
        ...wInfo,
        ...wxInfoRes?.userInfo
      }
      vLog.log('LOGIN onGetUnionId wInfo >>>', wInfo)
      setUser(wInfo)
    }

    await wx.login({
      success(res) {
        const { code: rCode } = res || {}
        vLog.log('LOGIN onGetUnionId wx.login >>>', res)
        if (rCode) {
          let params = {
            code: rCode,
            wechatCode: global.SOURCE_CODE,
            ...wxInfoRes,
          }

          vLog.log('LOGIN getUnionId  params >>>', params)
          //发起网络请求
          getUnionId(params)
            .then(result => {
              const { success, msg, param, code } = result || {}
              vLog.log('LOGIN getUnionId >>', success, msg, param, code)
              that._data.wxInfo = param
              if (!success) {
                wx.hideLoading()
                return interaction.showToast(msg || '')
              } else {
                setSaveUserInfo(param, false)
                const {
                  openid = '',
                  unionid = '',
                  wechatInfoId = ''
                } = param || {}

                setOpenId(openid)
                setUnionID(unionid)
                setWechatInfoId(wechatInfoId)

                return that.SMS_Login(param)
              }
            })
            .catch(error => {
              vLog.error('LOGIN getUnionId error >>>', error).report()
            })
        } else {
          vLog.error('LOGIN 登录失败 >>>', res && res.errMsg).report()
        }
      }
    })
  },

  //获取系统配置
  //获取系统配置
  async getConfigType() {
    let sysInfo = getSystemInfo()
    vLog.log('LOGIN getConfigType sysInfo >>', sysInfo)

    if (!sysInfo || isEmptyObject(sysInfo)) {
      let customerType = getCurrRoleType()
      const { success, data } = await getUCSystemApiConfig({ customerType })
      if (success) {
        let sysInfo = { ...data }
        for (const item of Object.entries(sysInfo)) {
          const [key, value] = item
          if (Array.isArray(value)) {
            sysInfo[key] = [].concat(value)
          } else if (value instanceof Object) {
            sysInfo[key] = { ...value }
          } else {
            sysInfo[key] = doJSON_PARSE(value || '{}' + '')
          }
        }
        vLog.log('LOGIN getConfigType res >>>', sysInfo)
        setSystemInfo(sysInfo)
      }
    }

    if (isEmptyObject(sysInfo)) {
      sysInfo = { ...sysConfig.sysConfig }
    }
    this.setData({
      sysInfo,
      agreement: `${sysInfo?.staff?.agreementSuccessContent || ''}`.split('&&')
    })
  },

  //跳转到主页
  goHome() {
    const _path = repairLaunchPath(getFirstPath())
    setUserRole(0)
    setUserLogin(true)
    setRouterInRegister({})
    app.globalData.emitter.emit(REFRESH_PAGE_DATA, { isRefresh: true, registerRouter: true })
    vLog.log('LOGIN goHome this.data >>>>', this._data, _path)
    return wx.switchTab({
      url: `${_path}`,
    })
  },

  onHide() {
    setAppHoldStatus(true)
  },

  back() {
    if (getCurrentPages().length > 1) {
      wx.navigateBack()
    } else {
      wx.reLaunch({
        url: '/pages/loginAndRegist/startUp/index'
      })
    }
  },

  onShow() {
    wx.hideShareMenu({
      menus: ['shareAppMessage', 'shareTimeline']
    })
  },

  onUnload() {
    setAppHoldStatus(false)
    storage.setStorage(global.STORAGE_GLOBAL_CASTER_PARAMS, {})
  },

  //用户协议是否同意
  onChange(event) {
    const { detail } = event || {}
    this.setData({ isAgree: detail }, () => this.SMS_CheckAllIsWell());
  },

  //获取用户信息
  getUserProfile() {
    const { phone } = this.data
    const { SMS_Code, loginCode, captchaToken = '' } = this._data
    if (loginCode === LoginState.ABSENCE) {
      if (!validator.isMobile(phone)) {
        return interaction.showToast('手机号码格式不正确')
      }
      if (SMS_Code.length !== 6) {
        return interaction.showToast('验证码长度有误，请重试')
      }
    }

    let that = this
    interaction.showLoading('加载中...')
    wx.getUserProfile({
      desc: '用于完善会员资料', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
      success: (res) => {
        vLog.log('LOGIN wx.getUserProfile res >>>>', res)
        that._data.authInfo = res
        if (captchaToken) {
          return that.onGetUnionId(res)
        }
        return that.tryToVerify('LOGIN')
      },
      fail: (err) => {
        vLog.error('LOGIN wx.getUserProfile fail err >>>>', err).report()
      },
      complete: () => {
        interaction.hideLoading()
      }
    })
  },

  //重新绑定
  async reBindingCode() {
    const { phone } = this.data
    const { SMS_Code, pervParams } = this._data

    let params = {
      phone,
      code: SMS_Code,
      openid: getOpenId(),
      unionid: getUnionID(),
      wechatInfoId: getWechatInfoId(),
      customerType: getCustomerTypeInt()
    }
    const { success, data: loginData = {}, msg, code } = await reBinding(params)
    vLog.info('LOGIN reBinding success, msg, code >>>', success, msg, code).report()

    switch (code) {
      case LoginState.INREVIEW: {
        setUserLogin(false)
        setUserRole(code * 1)
        interaction.showToast(msg || '账号审核中')
        let url = encodeURIComponent(`${wbs.gfH5}/share/advInviteRegister?result=${LoginState.INREVIEW}`)
        return setTimeout(() => {
          wx.reLaunch({
            url: `/pages/common/webview/webPage?url=${url}&hideHomeNav=1&banShare=1`,
          })
        }, 800)
      }

      case LoginState.FORBIDDEN: {
        setUserLogin(false)
        setUserRole(code * 1)
        return wx.reLaunch({
          url: '/packages-user/pages/applyFail/applyFail',
        })
      }

      case LoginState.NOTACTIVATED:
      case LoginState.ACTIVATED:
      case LoginState.SUCCESS: {
        const { userId = '', token = '' } = loginData
        setToken(token)
        setUserId(userId)

        const { success: staffSucc, data: staffData = {} } = await getAdvStaffCardInfo({ timeStamp: new Date().getTime() })
        if (staffSucc) {
          setSaveUserInfo({
            ...staffData,
            ...loginData,
          })
        }

        params = {
          ...params,
          customerType: getCurrRoleType(),
          token: loginData?.token,
          pageFlag: 'REGISTER'
        }

        for (const item of Object.entries(panDClone(pervParams))) {
          let [key, value] = item
          if (!FILTER_KEYS_SHARE_LIST.includes(key)) {
            params[key] = doUrlDeCode(value + '')
          }
        }

        const { targetPath = '', perfix = '' } = pervParams
        return wx.reLaunch({
          url: `${targetPath}?url=${perfix}&${qs.stringify(params)}`,
          complete: () => {
            setUserRole(0)
            setUserLogin(true)
            setRouterInRegister({})
            app.globalData.emitter.emit(REFRESH_PAGE_DATA, { isRefresh: true, registerRouter: true })
          }
        })
      }

      default:
        break
    }

    if (!isEmptyObject(data)) {
      setUserRole(0)
      setUserLogin(true)
      setRouterInRegister({})
      setSaveUserInfo(data)
      return this.goHome()
    }
  },

  //检测是否允许解绑
  checkBind(note = '换绑微信信息？') {
    const that = this
    let {
      sysInfo: {
        staff: {
          userChangeWeChat = '',
          userChangeWeChatTip = ''
        }
      }
    } = this.data

    if (userChangeWeChat == 2) {
      wx.showModal({
        title: '提示',
        content: userChangeWeChatTip,
        showCancel: false,
      })
    } else {
      wx.showModal({
        title: '提示',
        content: note,
        success(res) {
          const { confirm, cancel } = res || {}
          if (confirm) {
            return that.reBindingCode()
          }
          if (cancel) {
            vLog.error('用户点击取消').report()
          }
        }
      })
    }
  },

  /*************** 验证码登录 ***************/
  async SMS_Login(wInfo = {}) {
    vLog.log('LOGIN SMS_Login this.data >>>>', this._data, this.data)
    vLog.info('LOGIN SMS_Login wInfo >>>>', wInfo).report()

    const {
      SMS_Code,
      faId,
      inviterName,
      inviterRole,
      shouldLogin,
      shouldInvite,
      pervParams,
      sParams,
      rolePage,
      wxInfo = {},
      blackBox = '',
      captchaToken = '',
      fromPage = '',
      signinTypeStr = '',
    } = this._data

    const { phone } = this.data

    let params = {
      phone,
      blackBox,
      captchaToken,
      code: SMS_Code,
      customerType: getCustomerTypeInt(),
      ...wInfo
    }
    setViolationsPhone(phone)
    storage.setStorage('phone', phone)
    vLog.info('LOGIN SMS_Login params >>>>', params).report()
    const { success, data: loginData, msg, code } = await newLogin(params)
    vLog.log('LOGIN SMS_Login newLogin success, data, msg, code >>>', success, loginData, msg, code)
    interaction.hideLoading()
    this._data.loginCode = code

    switch (code) {
      // 同盾验证不通过
      case LoginState.VERIFY_ERROR:
      // 验证码失败
      case LoginState.CODEERROR: {
        return interaction.showToast(msg || '')
      }

      // 关联多个用户
      case LoginState.MULTIPLE_CUSTOMER:
      // 注册
      case LoginState.REGISTER: {
        const regParams = {
          ...params,
          faId,
          inviterName,
          shouldLogin,
          shouldInvite,
          statusCode: code,
          customerType: getCurrRoleType(),
        }

        /**
         * 目标地址页参数
         * toTargetPathUrl （地址）
         * toTargetPathParams （参数）
         */
        let tParams = {
          toTargetPathUrl: pervParams?.targetPath || faId ? '/pages/home/<USER>' : '',
          toTargetPathParams: {
            ...pervParams,
            ...params,
            ...wxInfo,
            customerType: getCurrRoleType(),
            token: getToken(),
          },
          fromRegister: true
        }

        let staffType = getCustomerTypeInt()
        const roleParams = {
          pageType: 'REGISTER',
          currRole: staffType,
          fromPage
        }

        if (sParams && !isEmptyObject(sParams)) {
          vLog.log('LOGIN SMS_Login sParams >>> ', sParams)
          const { newUrl = '' } = sParams
          tParams.toTargetPathUrl = '/pages/common/webview/webPage'
          tParams.toTargetPathParams['perfix'] = newUrl
          for (const item of Object.entries(panDClone(sParams))) {
            const [key, value] = item
            if (`${key}` !== 'newUrl') {
              tParams.toTargetPathParams[`${key}`] = doUrlDeCode(value + '')
            }
          }

          vLog.log('LOGIN SMS_Login REGISTER tParams >>>>', tParams)
          return wx.navigateTo({
            url: `/packages-user/pages/role/index?${qs.stringify(regParams)}&${qs.stringify(tParams)}&${qs.stringify(roleParams)}`
          })
        }

        // 邀请注册
        if (faId) {
          let path = `/packages-user/pages/role/index`
          const rParams = {
            mark: ROLE_TYPE[inviterRole],
            type: inviterRole,
            signinTypeStr
          }

          return wx.navigateTo({
            url: `${path}?${qs.stringify(regParams)}&${qs.stringify(tParams)}&${qs.stringify(roleParams)}&${qs.stringify(rParams)}`
          })
        }

        // 跳转指定首页的注册
        if (rolePage) {
          let path = `/packages-user/pages/role/index`
          const rParams = {
            mark: ROLE_TYPE[rolePage],
            type: rolePage,
          }

          return wx.navigateTo({
            url: `${path}?${qs.stringify(regParams)}&${qs.stringify(tParams)}&${qs.stringify(roleParams)}&${qs.stringify(rParams)}&rolePage=${rolePage}`
          })
        }

        vLog.log('LOGIN SMS_Login REGISTER regParams,tParams >>>>', regParams, tParams)
        return wx.navigateTo({
          url: `/packages-user/pages/role/index?${qs.stringify(regParams)}&${qs.stringify(tParams)}&${qs.stringify(roleParams)}`
        })
      }

      // 账号审核中
      case LoginState.INREVIEW: {
        interaction.showToast(msg || '账号审核中')
        setUserLogin(false)
        setUserRole(code)
        let url = encodeURIComponent(`${wbs.gfH5}/share/advInviteRegister?result=${LoginState.INREVIEW}`)
        return wx.reLaunch({
          url: `/pages/common/webview/webPage?url=${url}&hideHomeNav=1&banShare=1`,
        })
      }

      // 禁用
      case LoginState.FORBIDDEN: {
        setUserLogin(false)
        setUserRole(code)
        return wx.reLaunch({
          url: '/packages-user/pages/applyFail/applyFail',
        })
      }

      // 已绑定过微信，是否绑定当前微信
      case LoginState.HASBINDWECHAT: {
        return this.checkBind(msg)
      }

      // 已激活
      case LoginState.ACTIVATED:
      // 通过
      case LoginState.SUCCESS: {
        const { userId = '', token = '' } = loginData || {}
        vLog.info('LOGIN SUCCESS loginData,token,userId >>>>', loginData, token, userId)
        setToken(token)
        setUserId(userId)

        interaction.showLoading('加载中...')
        const { success: staffSucc, data: staffData = {} } = await getAdvStaffCardInfo({ timeStamp: new Date().getTime() })
        vLog.info('LOGIN getAdvStaffCardInfo success, data >>>>', staffSucc, staffData)
        interaction.hideLoading()
        if (staffSucc) {
          setSaveUserInfo({
            ...staffData,
            ...wInfo,
            ...loginData,
          })
        }

        vLog.log('LOGIN SUCCESS pervParams >>>>', pervParams)
        const { targetPath = '/pages/common/webview/webPage', perfix = '', newUrl = '' } = pervParams
        let rParams = {
          token: loginData?.token,
          pageFlag: 'REGISTER',
          customerType: getCurrRoleType(),
          ...wInfo
        }

        for (const item of Object.entries(panDClone(pervParams))) {
          const [key, value] = item
          if (!FILTER_KEYS_SHARE_LIST.includes(key)) {
            rParams[key] = doUrlDeCode(value + '')
          }
        }

        vLog.log('LOGIN rParams >>>>', rParams)

        return wx.reLaunch({
          url: `${targetPath}?url=${perfix || newUrl}&${qs.stringify(rParams)}`,
          complete: () => {
            setUserRole(0)
            setUserLogin(true)
            setRouterInRegister({})
            app.globalData.emitter.emit(REFRESH_PAGE_DATA, { isRefresh: true, registerRouter: true })
          }
        })
      }

      default:
        break
    }
  },

  /*************** 微信登录 ***************/
  async code_Login() {
    vLog.log('LOGIN code_Login this.data >>>>', this._data, this.data)
    const {
      faId,
      inviterName,
      inviterRole,
      shouldLogin,
      shouldInvite,
      pervParams,
      sParams,
      rolePage,
      wxInfo = {},
      fromPage = '',
      signinTypeStr = '',
    } = this._data
    const { phone } = this.data
    setViolationsPhone(phone)
    storage.setStorage('phone', phone)
    const { code: wxcode } = await wx.login()
    const { msg, success, data: loginData, code } = await checkLogin({ code: wxcode, customerType: getCustomerTypeInt(), wechatCode: global.SOURCE_CODE })
    vLog.log('LOGIN SMS_Login newLogin success, data, msg, code >>>', success, loginData, msg, code)
    interaction.hideLoading()
    this._data.loginCode = code
    // let wInfo = {}
    switch (code) {
      // 同盾验证不通过
      case LoginState.VERIFY_ERROR:
      // 验证码失败
      case LoginState.CODEERROR: {
        return interaction.showToast(msg || '')
      }

      // 关联多个用户
      case LoginState.MULTIPLE_CUSTOMER:
      // 注册
      case LoginState.REGISTER: {
        const regParams = {
          ...params,
          faId,
          inviterName,
          shouldLogin,
          shouldInvite,
          statusCode: code,
          customerType: getCurrRoleType(),
        }

        /**
         * 目标地址页参数
         * toTargetPathUrl （地址）
         * toTargetPathParams （参数）
         */
        let tParams = {
          toTargetPathUrl: pervParams?.targetPath || faId ? '/pages/home/<USER>' : '',
          toTargetPathParams: {
            ...pervParams,
            ...params,
            ...wxInfo,
            customerType: getCurrRoleType(),
            token: getToken(),
          },
          fromRegister: true
        }

        let staffType = getCustomerTypeInt()
        const roleParams = {
          pageType: 'REGISTER',
          currRole: staffType,
          fromPage
        }

        if (sParams && !isEmptyObject(sParams)) {
          vLog.log('LOGIN SMS_Login sParams >>> ', sParams)
          const { newUrl = '' } = sParams
          tParams.toTargetPathUrl = '/pages/common/webview/webPage'
          tParams.toTargetPathParams['perfix'] = newUrl
          for (const item of Object.entries(panDClone(sParams))) {
            const [key, value] = item
            if (`${key}` !== 'newUrl') {
              tParams.toTargetPathParams[`${key}`] = doUrlDeCode(value + '')
            }
          }

          vLog.log('LOGIN SMS_Login REGISTER tParams >>>>', tParams)
          return wx.navigateTo({
            url: `/packages-user/pages/role/index?${qs.stringify(regParams)}&${qs.stringify(tParams)}&${qs.stringify(roleParams)}`
          })
        }

        // 邀请注册
        if (faId) {
          let path = `/packages-user/pages/role/index`
          const rParams = {
            mark: ROLE_TYPE[inviterRole],
            type: inviterRole,
            signinTypeStr
          }

          return wx.navigateTo({
            url: `${path}?${qs.stringify(regParams)}&${qs.stringify(tParams)}&${qs.stringify(roleParams)}&${qs.stringify(rParams)}`
          })
        }

        // 跳转指定首页的注册
        if (rolePage) {
          let path = `/packages-user/pages/role/index`
          const rParams = {
            mark: ROLE_TYPE[rolePage],
            type: rolePage,
          }

          return wx.navigateTo({
            url: `${path}?${qs.stringify(regParams)}&${qs.stringify(tParams)}&${qs.stringify(roleParams)}&${qs.stringify(rParams)}&rolePage=${rolePage}`
          })
        }

        vLog.log('LOGIN SMS_Login REGISTER regParams,tParams >>>>', regParams, tParams)
        return wx.navigateTo({
          url: `/packages-user/pages/role/index?${qs.stringify(regParams)}&${qs.stringify(tParams)}&${qs.stringify(roleParams)}`
        })
      }

      // 账号审核中
      case LoginState.INREVIEW: {
        interaction.showToast(msg || '账号审核中')
        setUserLogin(false)
        setUserRole(code)
        let url = encodeURIComponent(`${wbs.gfH5}/share/advInviteRegister?result=${LoginState.INREVIEW}`)
        return wx.reLaunch({
          url: `/pages/common/webview/webPage?url=${url}&hideHomeNav=1&banShare=1`,
        })
      }

      // 禁用
      case LoginState.FORBIDDEN: {
        setUserLogin(false)
        setUserRole(code)
        return wx.reLaunch({
          url: '/packages-user/pages/applyFail/applyFail',
        })
      }

      // 已绑定过微信，是否绑定当前微信
      case LoginState.HASBINDWECHAT: {
        return this.checkBind(msg)
      }

      // 已激活
      case LoginState.ACTIVATED:
      // 通过
      case LoginState.SUCCESS: {
        const { userId = '', token = '' } = loginData || {}
        vLog.info('LOGIN SUCCESS loginData,token,userId >>>>', loginData, token, userId)
        setToken(token)
        setUserId(userId)

        interaction.showLoading('加载中...')
        const { success: staffSucc, data: staffData = {} } = await getAdvStaffCardInfo({ timeStamp: new Date().getTime() })
        vLog.info('LOGIN getAdvStaffCardInfo success, data >>>>', staffSucc, staffData)
        interaction.hideLoading()
        if (staffSucc) {
          setSaveUserInfo({
            ...staffData,
            // ...wInfo,
            ...loginData,
          })
        }
        vLog.log('LOGIN SUCCESS pervParams >>>>', pervParams)
        const { targetPath = '/pages/common/webview/webPage', perfix = '', newUrl = '' } = pervParams
        let rParams = {
          token: loginData?.token,
          pageFlag: 'REGISTER',
          customerType: getCurrRoleType(),
          // ...wInfo
        }
        for (const item of Object.entries(panDClone(pervParams))) {
          const [key, value] = item
          if (!FILTER_KEYS_SHARE_LIST.includes(key)) {
            rParams[key] = doUrlDeCode(value + '')
          }
        }
        vLog.log('LOGIN rParams >>>>', rParams)
        return wx.reLaunch({
          url: `${targetPath}?url=${perfix || newUrl}&${qs.stringify(rParams)}`,
          complete: () => {
            setUserRole(0)
            setUserLogin(true)
            setRouterInRegister({})
            app.globalData.emitter.emit(REFRESH_PAGE_DATA, { isRefresh: true, registerRouter: true })
          }
        })
      }
    }
  },

  // ================================= 无痕验证 =================================//
  tryToVerify(submitType = '') {
    // 获取验证码插件实例，#id与wxml文件中组件节点id一致
    let that = this
    const td = this.selectComponent('#td-captcha');

    // 调用API，触发验证码弹出
    td.captcha.triggerCaptcha({
      partnerCode: 'gffunds', // 同盾合作方
      appName: 'gffund_xcx', // 同盾合作方应用
      env: 1, // 1-线上环境 0-测试环境
      blackbox: that.data.blackBox || "",  // 设备指纹blackbox，非必填
      onSuccess: submitType === 'LOGIN' ? that.onTryToVerifyLoginSuccess : that.onTryToVerifySMSCodeSuccess, // 自定义验证成功回调
      onFail: that.onTryToVerifyFail, // 自定义验证失败回调，非必填
      onClose: that.onTryToVerifyClose, // 自定义验证码关闭回调，非必填
      lang: 1, // 语言配置项,必填
      maskClose: 0, // 蒙层是否可以关闭
    });
  },

  onTryToVerifyLoginSuccess: function (captchaToken = '') {
    const { authInfo } = this._data
    interaction.showLoading('加载中...')
    this._data.captchaToken = captchaToken
    return this.onGetUnionId(authInfo)
  },

  onTryToVerifySMSCodeSuccess: function (captchaToken = '') {
    const that = this
    let total_micro_second = 60;
    //验证码倒计时
    that._data = {
      ...that._data,
      captchaToken
    }
    this.requestCode()
    count_down(that, total_micro_second);
  },

  onTryToVerifyFail: function (msg) {
    vLog.error('LOGIN onTryToVerifyFail msg >>>', msg).report()
    if (msg !== 'opFail') {
      interaction.showToast(msg || '')
    }
  },

  onTryToVerifyClose() {
    vLog.info('LOGIN onTryToVerifyClose  >>>')
  },
  // ================================= 无痕验证 =================================//

  //输入手机号
  SMS_InputPhone(e) {
    const { detail = '' } = e || {}
    this.setData({
      phone: detail || ''
    }, () => this.SMS_CheckAllIsWell());
  },

  // 输入验证码
  SMS_InputCode(e) {
    const { detail = '' } = e || {}
    this._data.SMS_Code = detail || ""
    return this.SMS_CheckAllIsWell()
  },

  //验证码聚焦
  SMS_InputCodeBlur(e) {
    const {
      value = ''
    } = e.detail || {}

    this._data.SMS_Code = value || ""
    return this.SMS_CheckAllIsWell()
  },

  //登录按钮是否可以点击
  SMS_CheckAllIsWell() {
    const { phone, isAgree } = this.data
    const { loginCode, SMS_Code } = this._data
    let disabled = true
    if (loginCode === LoginState.ABSENCE) {
      if (phone !== '' && phone.length === 11 && SMS_Code !== '' && SMS_Code.length === 6 && isAgree) {
        disabled = false
      }
    } else {
      if (isAgree) {
        disabled = false
      }
    }

    this.setData({ disabled })
  },

  // 发送验证码判断
  SMS_SendCode() {
    const { sendLoading, phone = '', isAgree, agreement } = this.data
    if (sendLoading) {
      return
    }
    if (phone === '') {
      return interaction.showToast('输入手机号码')
    }
    if (!isAgree) {
      return interaction.showToast('请先同意《' + agreement[1] + "》")
    }
    if (!validator.isMobile(phone)) {
      return interaction.showToast('手机号码格式不正确')
    }
    return this.tryToVerify('SMSCode')
  },

  //获取验证码
  async requestCode() {
    const { phone = '' } = this.data
    const { success, msg } = await gflogincode({ phone })

    if (success) {
      return interaction.showToast('发送验证码成功')
    } else {
      clear_count(this);
      return interaction.showToast(msg)
    }
  },
})

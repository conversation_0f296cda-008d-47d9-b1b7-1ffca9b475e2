.background{
    width: 100%;
    height: 100%;
    position: relative;
}

.role-container{
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
}

.role-content-tips{
    display: flex;
    flex-direction: row;
    width: 100vw;
    padding: 20px 0;
    align-items: center;
    justify-content: center;
}

.role-left-tips{
    font-size: 18Px;
    color: #333;
    margin-left: 4rpx;
    margin-right: 4rpx;
}

.role-main-tips{
    font-size: 18Px;
    color: #333;
    font-weight: bold;
}

.role-tips-float{
    margin-top: -24rpx;
}

.role-change-btn{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 80%;
    height: 80rpx;
    border-radius: 40rpx;
    margin-top: 15vw;

    font-size: 16Px;
    color: #fff;
    font-weight: bold;
}

.role-back-home{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 80%;
    height: 80rpx;
    border-radius: 40rpx;
    margin-top: 10vw;

    font-size: 16Px;
    color: #E8340F;
    font-weight: bold;
    border: 1px solid #E8340F;
}

.role-curr-block{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    min-height: 30vh;
}

.bg-block{
    background-color: #FF690B;
    margin-bottom: 30px;
}

.bg-tips{
    height: 103px;
}


.role-tips-txt{
    font-size: 14Px;
    color: #999999;
    margin-top: 20px;
}

.role-header{
    width: 104px;
    height: 104px;
}

.role-block{
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 18rpx 14px;
    align-items: center;
}

.role-item{
    height: 76px;
}

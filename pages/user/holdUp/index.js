import {
  enums,
  interaction,
  util,
  qs,
  shapeShift,
  vLog,
  wbs,
  breakIn,
  global,
} from '../../../common/index.js'

import {
  getOpenId,
  setCustomerTypeInt,
  getUnionID,
  getUser,
  getWechatInfoId,
  setUser,
  getFirstPath,
  setToken,
  setUserId,
  getRouterInRegister,
  setRouterInRegister,
  getUserLogin,
  setUserLogin,
  setUserRole,
  getCurrRoleType
} from "../../../common/utils/userStorage";

import {
  checkLogin,
} from "../../../common/nb/home";

const {
  doUrlDeCode,
  panDClone,
  repairLaunchPath
} = util

const {
  ROLE_TYPE,
  ROLE_VISITOR,
  LoginState
} = enums

const TEMP_PATH = [
  '/pages/home/<USER>/index',
  '/pages/common/webview/webPage'
]

const HEAD_TIPS = '您即将访问'
const FOOT_TIPS = '的内容'

const app = getApp()

Page({
  data: {
    title: '角色',
    currRoleInfo: {},
    hTips: HEAD_TIPS,
    fTips: FOOT_TIPS
  },
  _data: {
    roleInfo: {},
  },

  onLoad(options) {
    vLog.log('HOLD_UP options, >>>>', qs.parse(options))
    let roleInfo = {}
    for (const [key, value] of Object.entries(panDClone(qs.parse(options)))) {
      if (typeof value !== 'object') {
        roleInfo[key] = doUrlDeCode(value + '')
      } else {
        let temp = {}
        for (let [tKey, tValue] of Object.entries(panDClone(value))) {
          temp[tKey] = doUrlDeCode(tValue + '')
        }
        roleInfo[key] = { ...temp }
      }
    }

    vLog.log('HOLD_UP roleInfo >>>>', roleInfo)
    this._data = {
      ...this._data,
      roleInfo
    }
    const { targetRole = '', customerType = '' } = roleInfo || {}
    let currRoleInfo = {}
    ROLE_VISITOR.forEach((rItem) => {
      const { type, targetRoleType } = rItem || {}
      if (type === customerType && targetRoleType === targetRole) {
        currRoleInfo = Object.assign({}, rItem)
      }
    })
    vLog.log('HOLD_UP currRoleInfo >>>>', currRoleInfo)
    this.setData({
      currRoleInfo,
      roleInfo,
    })
  },

  onShow() {
    wx.hideShareMenu({
      menus: ['shareAppMessage', 'shareTimeline']
    })
  },

  async onActionChange() {
    vLog.log('HOLD_UP onActionChange >>>', this.data, this._data)
    const {
      currRoleInfo: {
        targetRole = 0,
        targetRoleType = ''
      }
    } = this.data || {}
    const { roleInfo } = this._data || {}

    const { path = '', way = '', holdType = '', type: routeType = '' } = roleInfo || {}
    let cParams = {
      unionid: getUnionID(),
      openid: getOpenId(),
      customerType: targetRole * 1
    }

    interaction.showLoading('加载中...')
    const { code: wxcode } = await wx.login()
    const { msg, success, data, code } = await checkLogin({ code: wxcode, customerType: cParams.customerType, wechatCode: global.SOURCE_CODE })
      .catch(error => {
        vLog.error('HOLD_UP checkLogin catch error >>>', error).report()
      })
    vLog.log('HOLD_UP onActionChange  checkLogin  msg, success, data, code >>>', msg, success, data, code)
    interaction.hideLoading()

    let rolePath = '/packages-user/pages/register/agency/index'
    if (targetRole === ROLE_TYPE['CHANNEL']) {
      rolePath = '/packages-user/pages/register/channel/index'
    }
    if (targetRole === ROLE_TYPE['CUSTOMER']) {
      rolePath = '/packages-user/pages/register/customer/index'
    }

    // 切换目标用户不存在
    if (!success || !data) {
      vLog.log('HOLD_UP onActionChange checkLogin code,roleInfo >>>>>', code, roleInfo)

      const params = {
        toTargetPathParams: {
          ...roleInfo,
          targetPath: path || "/pages/common/topic/topic"
        },
        shouldLogin: '1',
        shouldInvite: '1',
        fromPage: 'HOLD_UP',
        rolePage: '',
        fromHoldUp: true,
        mark: targetRole,
        type: targetRoleType
      }

      if (holdType) {
        params['holdType'] = holdType
      }

      vLog.log('HOLD_UP onActionChange params >>>>>', params)
      switch (code) {
        case LoginState.ABSENCE:
        case LoginState.MULTIPLE_CUSTOMER:
        case LoginState.REGISTER: {
          interaction.showToast(msg || "")
          const hasLogin = getUserLogin()
          vLog.info('HOLD_UP !hasLogin,(way && way === reSetSite)  >>>>>', !hasLogin, (way && way === 'reSetSite')).report()
          if (!hasLogin || (way && way === 'reSetSite')) {
            params['holdType'] = ''
          }

          setTimeout(() => {
            return wx.navigateTo({
              url: `${rolePath}?${qs.stringify(params)}`
            })
          }, 1000)
          return
        }

        // 账号审核中，请稍后登录 => 跳转审核中页面
        case LoginState.INREVIEW: {
          setUserLogin(false)
          setUserRole(code)
          let url = encodeURIComponent(`${wbs.gfH5}/share/advInviteRegister?result=${LoginState.INREVIEW}`)
          return wx.reLaunch({
            url: `/pages/common/webview/webPage?url=${url}&hideHomeNav=1&banShare=1`
          })
        }

        //禁用 => 跳转禁用页面
        case LoginState.FORBIDDEN: {
          setUserLogin(false)
          setUserRole(code)
          return wx.reLaunch({
            url: `/packages-user/pages/applyFail/applyFail`
          })
        }

        default:
          break
      }
    } else {
      const { token = "", userId } = data || {}
      const platformParams = {
        openid: getOpenId(),
        unionid: getUnionID(),
        wechatInfoId: getWechatInfoId(),
      }

      setToken(token)
      setUserId(userId)
      let wInfo = getUser()
      wInfo = {
        ...wInfo,
        ...platformParams,
        ...data
      }
      // 已存在用户 切换身份
      setUser(wInfo)
      setCustomerTypeInt(targetRole)
      if (way && way === 'reSetSite') {
        if ((routeType && routeType == LoginState.START_UP) && path) {
          return shapeShift(targetRoleType, false, false, path)
        }
        return shapeShift(targetRoleType, false)
      }
      const { executed } = await shapeShift(targetRoleType, true)

      let [targetPath = '', ...others] = `${path}`.split('?')
      let tParams = qs.parse(`${others}`)

      tParams = {
        ...tParams,
        ...platformParams,
        token,
        customerType: targetRoleType,
      }

      targetPath = repairLaunchPath(targetPath)
      vLog.log('HOLD_UP targetPath org >>>>', targetPath)
      if (targetPath && `${targetPath}`.toLowerCase().includes('topic')) {
        const _regRes = getRouterInRegister()
        tParams = {
          ...tParams,
          ..._regRes
        }
      } else if (!TEMP_PATH.includes(targetPath) && targetPath !== getFirstPath()) {
        targetPath = getFirstPath()
      }

      if (executed && executed === 'DONE') {
        return wx.reLaunch({
          url: `${targetPath}?${qs.stringify(tParams)}`,
          complete: () => {
            getApp().globalData.refersMark = true
            setRouterInRegister({})
          }
        })
      }
    }
  },

  async onActionBack(e = {}) {
    vLog.log('HOLD_UP onActionBack e >>>', e)
    let customerType = getCurrRoleType()
    interaction.showLoading('加载中...')
    const { executed } = await shapeShift(`${customerType}`, true)
    interaction.hideLoading()
    if (executed && executed === 'DONE') {
      return breakIn({ name: 'doSetSiteBack' })
    }
  },
})

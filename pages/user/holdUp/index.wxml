<nb-page
    navShowBack="{{false}}"
    showHomeIcon="{{true}}"
    contentUseView="{{false}}"
    navTitle="{{title||'角色'}}"
    inHomePage="{{false}}"
    showTabBar="{{false}}">
  <view class="role-container">
    <view class="role-curr-block">
      <image src="{{currRoleInfo.icon}}" mode="aspectFill" class="role-header"/>
      <view class="role-tips-txt">{{currRoleInfo.tips || ''}}</view>
    </view>

    <view class="role-content-tips">
      <view class="role-left-tips">
        {{hTips}}
      </view>
      <view class="role-main-tips">
        {{currRoleInfo.checkRole}}
      </view>
      <view class="role-left-tips">
        {{fTips}}
      </view>
    </view>
    <view class="role-left-tips role-tips-float">
      {{'切换身份访问？'}}
    </view>

    <view
        class="role-change-btn"
        style="background-color: {{$state.themeColor}}"
        bind:tap="onActionChange">
      {{'切换身份访问'}}
    </view>

    <view class="role-back-home" bind:tap="onActionBack">
      {{'返回首页'}}
    </view>

  </view>
</nb-page>

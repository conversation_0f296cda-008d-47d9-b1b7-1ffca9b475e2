<!--
此文件为开发者工具生成，生成时间: 2022/4/18 下午2:44:53
使用方法：
在 /Users/<USER>/Desktop/work/wbs-wechat-investor/pages/home/<USER>

```
<import src="home.skeleton.wxml"/>
<template is="skeleton" wx:if="{{loading}}" />
```

在 /Users/<USER>/Desktop/work/wbs-wechat-investor/pages/home/<USER>
```
@import "./home.skeleton.wxss";
```

更多详细信息可以参考文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/skeleton.html
-->
<template name="skeleton">
  <view class="sk-container">
    <view is="components/nb-page/nb-page">
      <view class="container page--container" style="background-color:#f7f7f7;">
        <scroll-view scroll-y="true" style="height:642px;margin-top:88px; margin-bottom:82px;">
          <view is="components/x-scroll-view/x-scroll-view" class="list-container">
            <view class="scroll-view--refresh-block scroll-view--x-common" style="height:0px;">
              <view>
                <image class="scroll-view--refresh sk-image"></image>
              </view>
              <view>
                <text class="sk-transparent sk-text-18-7500-877 sk-text">下拉刷新</text>
              </view>
              <view></view>
            </view>
            <scroll-view class="scroll-view--scroll_container" scroll-top="0" scroll-y="true" style="position:fixed;width:100%;left:0;height:642px;top:88px;bottom:0px;">
              <view style="min-height:100%;position: absolute;width:100%;">
                <view>
                  <view is="components/polymers/polymers">
                    <view class="polymers--polymers-block">
                      <view is="components/polymers/cards/carousel/carousel">
                        <view class="carousel--carousel-card" style="margin-left: 0px;margin-right: 0px">
                          <view is="components/polymers/components/titleBar/titleBar"></view>
                          <view is="components/polymers/cards/banner/banner">
                            <view style="margin-top: 10px;">
                              <swiper autoplay="false" circular="true" class="banner--banner-block" current="0" duration="500" indicator-active-color="rgba(255,255,255,1)" indicator-color="#ccc" interval="4500" next-margin="20px" previous-margin="20px"
                                style="margin: 0;padding: 0;background-color: '#fff';"></swiper>
                              <view class="banner--dots">
                                <view class="banner--dot" data-i="0"></view>
                                <view class="banner--dot" data-i="1"></view>
                                <view class="banner--dot banner--active" data-i="2"></view>
                              </view>
                            </view>
                          </view>
                        </view>
                      </view>
                    </view>
                    <view class="polymers--polymers-block">
                      <view is="components/polymers/cards/content/content">
                        <view is="components/nb-card/nb-card">
                          <view class="nb-card card--nb-card case card--case ">
                            <view class="nb-item card--nb-item ">
                              <view is="components/polymers/components/titleBar/titleBar">
                                <view class="titleBar--float-title-block" style="margin-top: 10px">
                                  <view class="titleBar--title-left-block" style="margin-left:0px;margin-right:0px;">
                                    <text class="titleBar--title-txt sk-transparent sk-text-18-7500-456 sk-text" style="margin-left:0px;">营销资料</text>
                                    <text class="titleBar--subTitle-txt sk-transparent sk-text-18-7500-1000 sk-text">火热销售中</text>
                                  </view>
                                  <view class="titleBar--title-right-block" data-info="[object Object]">
                                    <text class="titleBar--more-txt sk-transparent sk-text-18-7500-635 sk-text">全部热门产品</text>
                                    <view is="miniprogram_npm/vant-weapp/icon/index" class="titleBar--title-next-icon">
                                      <view class="van-icon icon-index--van-icon van-icon-arrow icon-index--van-icon-arrow sk-pseudo sk-pseudo-circle" style="color: #999;font-size: 12px;"></view>
                                    </view>
                                  </view>
                                </view>
                              </view>
                              <view class="content--content-nav" style="margin-top: 0px">
                                <view is="components/scrollable-tabview/scrollable-tabview" class="content--current-item">
                                  <view class="tabview--container tabview--navBar" style="background-color:#fff;">
                                    <scroll-view scroll-with-animation="true" scroll-x="true" scroll-into-view="tabbar0">
                                      <view class="tabview--tab sk-transparent" data-index="0" data-value="0" id="eb7f5309--tabbar0" style="font-weight:500;color: #333;background-color:#fff;padding-top: 10px;">
                                        债券市场
                                        <view class="tabview--indicator" style="background-color:rgba(246, 103, 0, 1);margin-top:5px"></view>
                                      </view>
                                      <view class="tabview--tab sk-transparent" data-index="1" data-value="1" id="eb7f5309--tabbar1" style="font-weight:normal;color: #999;background-color:#fff;padding-top: 10px;">
                                        CNMD
                                        <view class="tabview--indicator" style="background-color:;margin-top:5px"></view>
                                      </view>
                                      <view class="tabview--tab sk-transparent" data-index="2" data-value="2" id="eb7f5309--tabbar2" style="font-weight:normal;color: #999;background-color:#fff;padding-top: 10px;">
                                        市场快评
                                        <view class="tabview--indicator" style="background-color:;margin-top:5px"></view>
                                      </view>
                                      <view class="tabview--tab sk-transparent" data-index="3" data-value="3" id="eb7f5309--tabbar3" style="font-weight:normal;color: #999;background-color:#fff;padding-top: 10px;">
                                        投资策略
                                        <view class="tabview--indicator" style="background-color:;margin-top:5px"></view>
                                      </view>
                                      <view class="tabview--tab sk-transparent" data-index="4" data-value="4" id="eb7f5309--tabbar4" style="font-weight:normal;color: #999;background-color:#fff;padding-top: 10px;">
                                        债券市场
                                      </view>
                                    </scroll-view>
                                    <view class="tabview--border-line"></view>
                                  </view>
                                </view>
                              </view>
                              <view class="content--content-list">
                                <view is="components/polymers/items/news/news">
                                  <view class="news--card" style="border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;border-bottom-width: 1px;border-top-left-radius: 0px;border-top-right-radius: 0px;">
                                    <view class="news--card-box" data-item="[object Object]" style="margin-top:0px;padding-top:0px;border-bottom-color: rgba(238, 238, 238, 0.3)">
                                      <view class="news--card-left">
                                        <view class="news--card-left-title news--text-two-line sk-transparent sk-text-18-7500-759 sk-text">捕捉整体性行情 基金公司密集布局宽基品种</view>
                                        <view class="news--card-row-box">
                                          <image class="news--icon-time sk-image" mode="aspectFill"></image>
                                          <view class="news--card-left-time news--text-two-line sk-transparent sk-text-18-7500-367 sk-text">
                                            2022-03-23 </view>
                                        </view>
                                      </view>
                                      <image class="news--image sk-image" mode="aspectFill"></image>
                                    </view>
                                  </view>
                                </view>
                              </view>
                              <view class="content--content-list">
                                <view is="components/polymers/items/news/news">
                                  <view class="news--card" style="border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;border-bottom-width: 1px;border-top-left-radius: 0px;border-top-right-radius: 0px;">
                                    <view class="news--card-box" data-item="[object Object]" style="margin-top:0px;padding-top:14px;border-bottom-color: rgba(238, 238, 238, 0.3)">
                                      <view class="news--card-left">
                                        <view class="news--card-left-title news--text-two-line sk-transparent sk-text-18-7500-736 sk-text">新冠检测概念股走强，油气板块熄火3股跌超19％</view>
                                        <view class="news--card-row-box">
                                          <image class="news--icon-time sk-image" mode="aspectFill"></image>
                                          <view class="news--card-left-time news--text-two-line sk-transparent sk-text-18-7500-429 sk-text">
                                            2022-03-23 | 21世纪经济报道</view>
                                        </view>
                                      </view>
                                      <image class="news--image sk-image" mode="aspectFill"></image>
                                    </view>
                                  </view>
                                </view>
                              </view>
                              <view class="content--content-list">
                                <view is="components/polymers/items/news/news">
                                  <view class="news--card" style="border-bottom-left-radius: 6px;border-bottom-right-radius: 6px;border-bottom-width: 0px;border-top-left-radius: 0px;border-top-right-radius: 0px;">
                                    <view class="news--card-box" data-item="[object Object]" style="margin-top:0px;padding-top:14px;border-bottom-color: #fff">
                                      <view class="news--card-left">
                                        <view class="news--card-left-title news--text-two-line sk-transparent sk-text-18-7500-89 sk-text">公募REITs品种规模扩容可期 政协委员希冀更多优质项目“落户”</view>
                                        <view class="news--card-row-box">
                                          <image class="news--icon-time sk-image" mode="aspectFill"></image>
                                          <view class="news--card-left-time news--text-two-line sk-transparent sk-text-18-7500-911 sk-text">
                                            2022-03-23 </view>
                                        </view>
                                      </view>
                                      <image class="news--image sk-image" mode="aspectFill"></image>
                                    </view>
                                  </view>
                                </view>
                              </view>
                            </view>
                          </view>
                        </view>
                      </view>
                    </view>
                    <view class="polymers--polymers-block">
                      <view is="components/polymers/cards/content/content">
                        <view is="components/nb-card/nb-card">
                          <view class="nb-card card--nb-card case card--case ">
                            <view class="nb-item card--nb-item ">
                              <view is="components/polymers/components/titleBar/titleBar">
                                <view class="titleBar--title-block" style="margin-left:0px;margin-right:0px;margin-bottom: -12px;margin-top: 10px;">
                                  <view class="titleBar--title-left-block">
                                    <text class="titleBar--title-txt sk-transparent sk-text-18-7500-263 sk-text" style="margin-left:0px;">最新观点</text>
                                    <text class="titleBar--subTitle-txt sk-transparent sk-text-18-7500-411 sk-text">广发基金独家观点</text>
                                  </view>
                                  <view class="titleBar--title-right-block" data-info="[object Object]">
                                    <text class="titleBar--more-txt sk-transparent sk-text-18-7500-288 sk-text">交互区</text>
                                    <view is="miniprogram_npm/vant-weapp/icon/index" class="titleBar--title-next-icon">
                                      <view class="van-icon icon-index--van-icon van-icon-arrow icon-index--van-icon-arrow sk-pseudo sk-pseudo-circle" style="color: #999;font-size: 12px;"></view>
                                    </view>
                                  </view>
                                </view>
                              </view>
                              <view class="content--content-nav" style="margin-top: 0px">
                                <view is="components/scrollable-tabview/scrollable-tabview" class="content--current-item">
                                  <view class="tabview--container tabview--navBar" style="background-color:#fff;">
                                    <scroll-view scroll-with-animation="true" scroll-x="true" scroll-into-view="tabbar0">
                                      <view class="tabview--tab sk-transparent" data-index="0" data-value="0" id="1dd3e192--tabbar0" style="font-weight:500;color: #333;background-color:#fff;padding-top: 10px;">
                                        市场解读
                                        <view class="tabview--indicator" style="background-color:rgba(246, 103, 0, 1);margin-top:5px"></view>
                                      </view>
                                      <view class="tabview--tab sk-transparent" data-index="1" data-value="1" id="1dd3e192--tabbar1" style="font-weight:normal;color: #999;background-color:#fff;padding-top: 10px;">
                                        基金经理说
                                        <view class="tabview--indicator" style="background-color:;margin-top:5px"></view>
                                      </view>
                                      <view class="tabview--tab sk-transparent" data-index="2" data-value="2" id="1dd3e192--tabbar2" style="font-weight:normal;color: #999;background-color:#fff;padding-top: 10px;">
                                        基金推荐
                                        <view class="tabview--indicator" style="background-color:;margin-top:5px"></view>
                                      </view>
                                      <view class="tabview--tab sk-transparent" data-index="3" data-value="3" id="1dd3e192--tabbar3" style="font-weight:normal;color: #999;background-color:#fff;padding-top: 10px;">
                                        营销资料
                                        <view class="tabview--indicator" style="background-color:;margin-top:5px"></view>
                                      </view>
                                    </scroll-view>
                                  </view>
                                </view>
                              </view>
                            </view>
                          </view>
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </scroll-view>
          </view>
          <view is="miniprogram_npm/vant-weapp/toast/index" id="van-toast">
            <view is="miniprogram_npm/vant-weapp/transition/index"></view>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
</template>
import {
  breakIn,
  enums,
  eventName,
  global,
  interaction,
  storage,
  systemtInfo,
  userAction,
  util,
  vLog
} from '../../common/index';

import {
  getAttentionStatus
} from "../../common/nb/home";

import {
  getShortLink,
  getTabBarConfig,
  getTabBarList,
  getUnionID,
  setAppHoldStatus,
  setLoadingMoment,
} from "../../common/utils/userStorage";

const {
  screenHeight,
  footHeight,
  titleHeight
} = systemtInfo

const {
  rpx2px,
  isEmptyObject
} = util;

const {
  DISMISS_TAB_BAR,
  SET_SHOW_RECORD,
  TAB_LISTENER_EVENT,
  FETCH_NET_DATA_ERROR,
  SET_SCROLL_TO_TARGET,
  SET_REFRESH_PAGE,
} = eventName

const {
  SHARE_BLOCK_DEFAULT,
  WECHAT_COLD_START,
} = enums

const app = getApp()
const pageName = 'HOME'

Page({
  /**
   * 页面的初始数据
   */
  data: {
    cardInfos: [],
    pageName,
    pageType: '',
    platform: '',
    url: '',

    refreshing: false,
    nomore: false,
    contentHeight: rpx2px(screenHeight - footHeight - titleHeight),

    loading: true,
    scrollTop: 0,

    showEmptyPage: false,
    showOfficial: false,
    tBarType: 0,
    isMarkDay: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {

    getApp().event.on(TAB_LISTENER_EVENT, (event) => {
      this.onHandleEvent(event)
    })

    let _tConfig = getTabBarConfig()
    for (let [tKey, tValue] of Object.entries(_tConfig)) {
      if (pageName === tValue) {
        this.setData({ tBarType: tKey * 1 })
      }
    }
    return this.init()
  },

  async init() {
    let that = this
    await Promise.allSettled([
      that.getCPInfo(),
      that.initOfficialInfo(),
      userAction({ name: 'initWxInfo', params: {} }),
      // breakIn({name: 'setReportUrlLink', params: {}})
    ]).then((res) => {
      vLog.info(`${pageName} onLoad res >>>`, res).report()
      that.onProbe()
    }).catch((err) => {
      vLog.error(`${pageName} onLoad err >>>`, err).report()
    })
  },

  onShow() {
    if (getApp().globalData?.shortLink || getShortLink()) {
      let shortLink = getApp().globalData?.shortLink || getShortLink()

      return wx.openEmbeddedMiniProgram({
        shortLink: `${shortLink}`,
        extraData: {
          unionId: getUnionID()
        },
        envVersion: 'release',
        complete() {
          getApp().globalData.shortLink = ''
          setAppHoldStatus(true)
          breakIn({ name: 'saveShortLink', params: { shortLink: '' } })
        }
      })
    }
  },

  onHandleEvent(event = {}) {
    vLog.log(`${pageName} onHandleEvent event >>>`, event)
    const { key, tab } = event

    switch (key) {
      case SET_SCROLL_TO_TARGET:
        if (tab === pageName) {
          this.handlePullDownRefresh()
        }
        break

      case FETCH_NET_DATA_ERROR:
        this.doCancelFresh()
        break

      case SET_REFRESH_PAGE:
        this.setRefreshPage()
        break

      case DISMISS_TAB_BAR:
        this.doSetPageHeight()
        break

      case SET_SHOW_RECORD:
        this.onShowRecordModel()
        break

      default:
        break
    }
  },

  async initOfficialInfo() {
    const { success, data = false, msg = '' } = await getAttentionStatus({ unionid: getUnionID() })
    if (!success) {
      return interaction.showToast(msg || '')
    }

    const currScreen = storage.getStorage(global.STORAGE_GLOBAL_SCREEN_CODE) || ''
    if (WECHAT_COLD_START.includes(currScreen) && !data) {
      this.setData({
        showOfficial: true
      })
      getApp().sensors.registerApp({
        is_subscribe: false,// 木棉花公众号关注状态
      });
    } else {
      getApp().sensors.registerApp({
        is_subscribe: true,// 木棉花公众号关注状态
      });
    }
  },

  onShowRecordModel() {
    return userAction({ name: 'showRecordModal', params: { tabName: pageName } })
  },

  setScrollTo(tab, scrollTo = -300) {
    if (tab === pageName) {
      this.setData({
        scrollTop: scrollTo
      }, () => this.setRefreshPage())
    }
  },

  /**
   * 首页优化探针
   */
  onProbe() {
    const { cardInfos } = this.data
    vLog.log(`${pageName} onProbe cardInfos >>>>`, cardInfos)
    if (!cardInfos || !cardInfos.length) {
      this.setData({
        loading: false,
        refreshing: false,
        showEmptyPage: true,
      }, () => this.getCPInfo(true))
    }
  },

  setRefreshPage() {
    return this.doRefreshHomeInfo()
  },

  doSetPageHeight() {
    this.setData({
      contentHeight: rpx2px(screenHeight - titleHeight)
    })
  },

  doCancelFresh() {
    let timer = setTimeout(() => {
      clearTimeout(timer)
      setLoadingMoment(false)
      this.setData({
        refreshing: false,
      })
    }, 1000)
  },

  async getCPInfo(doFresh = true) {
    let tList = (getApp().globalData.tabList)?.length ? getApp().globalData.tabList : getTabBarList() || []
    console.log(`${pageName} getCPInfo cardInfos >>`, cardInfos)
    vLog.log('HOME getCPInfo this.data  >>>', this.data)
    vLog.log('HOME getCPInfo doFresh  >>>', doFresh)
    const { cardInfos } = this.data
    if ((cardInfos && cardInfos.length) && !doFresh) {
      return Promise.resolve(cardInfos)
    }
    if (doFresh) {
      const _cardInfos = await breakIn({ name: 'getTabCardInfo', params: { pageName } })

      this.setData({
        cardInfos: _cardInfos,
        loading: false,
        refreshing: false,
      })
      return Promise.resolve(_cardInfos)
    }

    if (tList.length) {
      tList = tList.find((tItem) => {
        const {
          pageTabBarConf: {
            tabName = ''
          }
        } = tItem || {}

        return pageName === tabName
      })
      if (!isEmptyObject(tList)) {
        const { cardInfos = [] } = tList
        const _cardInfos = []

        cardInfos.forEach((cItem) => {
          const props = {
            ...cItem,
            fromTab: pageName
          }
          _cardInfos.push(props)
        })

        console.log(`${pageName} getCPInfo _cardInfos >>`, _cardInfos)
        this.setData({
          cardInfos: _cardInfos,
          loading: false,
          refreshing: false,
        })

        return Promise.resolve(_cardInfos)
      }
    }
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    setAppHoldStatus(false)
    storage.setStorage(global.STORAGE_GLOBAL_RESET_SHARE_PATH_DATA, {})
  },

  async doRefreshHomeInfo() {
    let that = this
    await Promise.allSettled([
      userAction({ name: 'initWxInfo', params: {} }),
      this.getCPInfo(true)
    ]).then((res) => {
      console.log(`${pageName} doRefreshHomeInfo res >>`, res)

      setLoadingMoment(false)
      that.setData({
        refreshing: false,
      })
    }).catch((err) => {
      vLog.error(`${pageName} doRefreshHomeInfo  err >>>`, err).report()
    })
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  handlePullDownRefresh: function () {
    setLoadingMoment(true)
    setAppHoldStatus(false)
    this.setData({
      nomore: false,
    }, () => {
      return this.doRefreshHomeInfo()
    })
  },

  onShareAppMessage() {
    const shareData = breakIn({ name: 'getPageShareInfo', params: { fromTab: pageName } })
    getApp().sensors.track('MPShare', {
      share_type: "微信好友",
    })
    return {
      ...SHARE_BLOCK_DEFAULT,
      ...shareData
    }
  },

  // onShareTimeline() {
  //   return {
  //     title: '广发木棉花',
  //     query: '/pages/loginAndRegist/startUp/index',
  //     imageUrl: SHARE_IMG_DEFAULT
  //   }
  // }
})

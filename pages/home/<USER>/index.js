import {
  systemtInfo,
  global,
  util,
  enums,
  storage,
  qs,
  interaction,
  wbs,
  breakIn,
  eventName,
  vLog
} from '../../../common/index'

import {
  getNewsList,
  getMulitList,
  getPosterList,
  getRecommendList,
  getHomeMarketPlanList,
  getAdvStaffCardInfo,
  getUnionId,
  checkLogin,
  getArticleClassify,
  getPosterClassify,
  getMulitClassify,
  getMarketPlanClassify,
  getFindById,
  getMarketingPlanInfo
} from "../../../common/nb/home";

import {
  getCategoryListByIds,
  getLiveList,
} from "../../../common/network/api";

import {
  getUnionID,
  getOpenId,
  getToken,
  getWechatInfoId,
  getUser,
  setUser,
  setSaveUserInfo,
  setUserRole,
  getUserRole,
  getCurrRoleType,
  setUserLogin,
  setToken,
  getUserLogin
} from "../../../common/utils/userStorage";

const {
  SEND_EVENT_TO_POLYMERS,
} = eventName

const PRODUCT_PAGE_TYPE = {
  SCROLL_TAB_VIEW: 'advProductDataDetail', // 分页切换
  SCROLL_ANCHOR_VIEW: 'advProductDataNewDetail' // 瀑布流
}

const {
  screenHeight,
  footHeight,
  platform
} = systemtInfo

const {
  rpx2px,
  isEmptyObject,
  analyzeAction,
  doUrlDeCode
} = util;

const {
  RealmType,
} = enums

const app = getApp()
const dayJs = require('dayjs')

// action://share/UnionList?listType=SINGLE&categoryIds=2c95808a7da3b389017da41985a40003,8aaa8e9c7fb0710e017fb4adbbf5000c,8aaa8ec27fba77ab017fc047a6a700ab,2c95808a7da3b389017da41921510002,8aaa8e9c7fb0710e017fb4ac05eb0008,2c95808a7da3b389017db818554e0244,2c95808a7da3b389017de1e150140557&name=终点持营&type=MARKET_PLAN
// action://share/UnionList?listType=UNION&name=聚合列表&unionId=3229790660512133
// action://share/UnionList?path=pages/home/<USER>/index&listType=UNION&name=聚合列表&unionId=3287385853198864

Page({
  data: {
    modePlat: (platform === "ios"),
    contentHeight: rpx2px(screenHeight - footHeight),

    listType: 'SINGLE',
    wInfo: {},
    hasLogin: false,
    userRole: null,
    customerType: '',

    list: [],
    TabCur: 0,  //  一级分类idx
    initNextIdx: 0, // 二级分类idx
    refreshing: false,
    nomore: false,
    loadmoring: false,

    contentInfo: [],
    currList: [],
    page: 0,
    pageSize: 21,
    currTotalPages: 1,
    scrollTop: 0,

    type: '',
    name: "列表",
    unionId: '',
    orgId: '',
    categoryIds: '',
    targetTabId: '',
    nextTabId: '',
    placeholder: '请输入关键词',

    banner: '',
    currNextModels: [],

    refreshTopDistance: 3,
    unionContent: {},

    lView: 'single-line'
  },
  onLoad(options) {
    vLog.log('UNION_LIST options >>>>', qs.parse(options))
    let { ...unionContent } = qs.parse(options) || {}

    for (const item of Object.entries(unionContent)) {
      let [key, value] = item
      key = decodeURIComponent(key)
      value = decodeURIComponent(value + '')
      unionContent[key] = doUrlDeCode(value)
    }

    vLog.log('UNION_LIST unionContent >>>>', unionContent)
    const {
      type = RealmType.ARTICLE,
      name = '',
      categoryIds = '',
      targetTabId = '',
      nextTabId = '',
      unionId = '',
      listType = ''
    } = unionContent || {}

    const customerType = getCurrRoleType()
    const hasLogin = getUserLogin()
    const userRole = getUserRole()
    const wInfo = getUser()

    this.inLineGetSysInfo(userRole)
    this.getWeChatInfo()
    this.setData({
      type,
      name,
      unionId,
      listType,
      targetTabId,
      nextTabId,
      categoryIds,
      wInfo,
      hasLogin,
      customerType,
      unionContent
    }, () => {
      if (listType === 'SINGLE') {
        this.initUnionList()
      } else {
        if (targetTabId || nextTabId) {
          this.initUnionCategory(unionId)
          this.onProbe()
          return;
        }

        this.initUnionInfo(unionId)
      }
    })

    this.onProbe()
    this.scrollProbe()
    this.onSetHide()
  },

  async onSetHide() {
    const { hasLogin, customerType } = this.data
    return setTimeout(() => ((!hasLogin || customerType === 'AGENCY'))
      ? wx.hideShareMenu({
        menus: ['shareAppMessage', 'shareTimeline']
      }) : null
      , 1000)
  },

  async getWeChatInfo() {
    interaction.showLoading('加载中...')
    const { success, data } = await getAdvStaffCardInfo({
      timeStamp: new Date().getTime()
    })
    interaction.hideLoading()
    let _wInfo = getUser()
    if (success) {
      const { orgId = '' } = data || {}
      storage.setStorage('orgId', orgId)

      _wInfo = {
        ..._wInfo,
        ...data
      }
      setUser(_wInfo)
    }

    this.setData({
      wInfo: _wInfo
    })
  },

  async inLineGetSysInfo(uInfo) {
    vLog.log('UNION_LIST inLineGetSysInfo uInfo >>>>', uInfo)
    interaction.showLoading('加载中...')
    const that = this
    if (!uInfo && !(typeof uInfo === 'number' && uInfo === 0)) {
      await wx.login({
        success(res) {
          const { code = '' } = res || {}
          if (code) {
            //发起网络请求
            getUnionId({ code, wechatCode: global.SOURCE_CODE })
              .then(result => {
                vLog.log('UNION_LIST onGetUnionId result >>>', result)
                interaction.hideLoading()
                const { success, msg, param = {} } = result || {}
                if (!success) {
                  interaction.showToast(msg || '')
                } else {
                  setSaveUserInfo(param, false)
                }
                return that._doCLogin(param)
              })
              .catch(err => {
                vLog.error('UNION_LIST catch err >>> ', err).report()
              })
          }
        },
        fail(err) {
          vLog.error('UNION_LIST fail err >>> ', err).report()
        }
      })
    }
    interaction.hideLoading()
  },

  async _doCLogin(param = {}) {
    vLog.info('UNION_LIST _doCLogin param >>', param)
    const {
      unionid = '',
      openid = '',
    } = param || {}

    const _param = {
      unionid,
      openid
    }

    interaction.showLoading('加载中...')
    const { code: wxcode } = await wx.login()
    const { data, code } = await checkLogin({ code: wxcode, customerType: _param.customerType, wechatCode: global.SOURCE_CODE })
      .catch(error => {
        vLog.error('UNION_LIST doCheckLogin catch error >>>', error).report()
      })
    interaction.hideLoading()
    vLog.log('UNION_LIST _doCLogin code  >>>>>', code)
    const { token: _newToken = '' } = data || {}
    const _token = _newToken || getToken()
    if (_token) {
      let _data = {
        ...param,
        ...data,
      }
      setSaveUserInfo(_data)
      setUserLogin(true)
      setToken(_token)
    } else {
      setToken('')
      setUserId('')
    }
    setUserRole(code * 1)
  },

  async onProbe() {
    let _ProbeTimer = setTimeout(() => {
      clearTimeout(_ProbeTimer)

      let userRole = getUserRole()
      if (typeof userRole === 'string') {
        userRole = ''
      }

      this.setData({
        userRole
      })
    }, 1000)
  },

  async initUnionList() {
    interaction.showLoading('加载中...')
    vLog.log('UNION_LIST initUnionList this.data >>>>', this.data)
    const { listType, type, categoryIds, wInfo } = this.data

    let params = {}
    // 单列表
    if (listType === 'SINGLE') {
      switch (type) {
        case RealmType.ARTICLE: {
          params = {
            categoryIds
          }
          const { msg, param, success } = await getArticleClassify(params)
          interaction.hideLoading()
          return this.reInstallSingleCategory(type, param, success, msg)
        }

        case RealmType.LIVE: {
          let ids = categoryIds.split(',')
          const { firstOrgId = '' } = wInfo || {}
          params = {
            ids,
            channelIds: [`${firstOrgId}`]
          }
          if (!firstOrgId) {
            delete params.channelIds
          }

          const { msg, value, code } = await getCategoryListByIds(params)
          interaction.hideLoading()
          return this.reInstallSingleCategory(type, value, code == 0, msg)
        }

        case RealmType.POSTER: {
          params = {
            categoryIds
          }

          const { msg, param, code } = await getPosterClassify(params)
          interaction.hideLoading()
          return this.reInstallSingleCategory(type, param, code == 0, msg)
        }

        case RealmType.MULTI_COURSE: {
          params = {
            categoryId: categoryIds
          }
          const { msg, data, success } = await getMulitClassify(params)
          interaction.hideLoading()
          return this.reInstallSingleCategory(type, data, success, msg)
        }

        case RealmType.MARKET_PLAN: {
          let allData = []
          vLog.log('UNION_LIST getMarketPlanClassify categoryIds ########>>>', categoryIds)
          const { msg, success, param = [], code } = await getMarketPlanClassify({ categoryIds })
          interaction.hideLoading()
          vLog.log('UNION_LIST getMarketPlanClassify msg, success, param, code >>>', msg, success, param, code)
          allData = [].concat(param)

          return this.reInstallSingleCategory(type, allData, success, msg)
        }

        default:
          break
      }
    } else {

    }
  },

  async initUnionCategory(id = '') {
    const { targetTabId, nextTabId, lView, modePlat } = this.data
    let TabCur = 0
    let initNextIdx = 0
    let _cRealm = ''
    let _cModels = []
    let contentInfo = []
    let _lView = lView

    const { msg, data, success } = await getFindById({ id })
    if (!success) {
      return interaction.showToast(msg || '')
    }

    let _agg_dtos = 0
    const { aggCateRelDTOS = [] } = data || {}
    if (aggCateRelDTOS && aggCateRelDTOS.length) {
      aggCateRelDTOS.forEach((item, index) => {
        const { categoryModels = [], realm = '', id: _targetTabId = '' } = item || {}
        if (targetTabId == _targetTabId) {
          _cModels = categoryModels
          _cRealm = realm
          TabCur = index

          if (categoryModels && categoryModels.length) {
            categoryModels.forEach((cItem, cIndex) => {
              const { categoryId: _nextTabId = '', id: _id = '' } = cItem || {}
              let cNextId = _nextTabId || _id || ''
              if (nextTabId == cNextId) {
                initNextIdx = cIndex
              }
            })
          }
        }

        for (let i = 0; i < categoryModels.length; i++) {
          const { status = '', } = categoryModels[i] || {}
          if (status != 'PUBLISHED' && status != '1') {
            categoryModels.splice(i, 1)
          }
        }
        const { tabName = '' } = item || {}
        contentInfo.push({ name: tabName })
        _agg_dtos = index
      })
    }
    vLog.log('UNION_LIST contentInfo,list,type >>> ', contentInfo, data, _cRealm)
    if (_agg_dtos > 1 || contentInfo.length > 1) {
      _lView = 'category-line'
      if (modePlat) {
        _lView = 'single-line'
      }
    }

    if (_cModels.length > 1) {
      _lView = 'subnav-line'
      if (modePlat) {
        _lView = 'category-line'
      }
    }

    this.setData({
      list: data,
      contentInfo,
      lView: _lView,
      currNextModels: _cModels,
      type: _cRealm || '',
      TabCur,
      initNextIdx,
    })

    if (_cModels && _cModels.length > 0) {
      const { categoryId, id } = _cModels[initNextIdx] || {}
      return this.getListById(_cRealm || "", categoryId || id || '')
    }
  },

  async initUnionInfo(id = '') {
    const { TabCur = 0, initNextIdx = 0, lView = '', modePlat } = this.data
    const { msg, data, success } = await getFindById({ id })

    if (!success) {
      return interaction.showToast(msg || '')
    }

    let _lView = lView
    let _cRealm = ''
    let _cModels = []
    let contentInfo = []
    let _targetTabId = ''
    let _nextTabId = ''

    let _agg_dtos = 0
    const { aggCateRelDTOS = [] } = data || {}
    if (aggCateRelDTOS && aggCateRelDTOS.length) {
      aggCateRelDTOS.forEach((item, index) => {
        const { categoryModels = [], realm = '', id: targetTabId = '' } = item || {}
        if (TabCur === index) {
          _cModels = categoryModels
          _cRealm = realm
          _targetTabId = targetTabId
        }

        for (let i = 0; i < categoryModels.length; i++) {
          const { status = '', categoryId: nextTabId = '' } = categoryModels[i] || {}
          if (initNextIdx === i) {
            _nextTabId = nextTabId
          }

          if (status != 'PUBLISHED' && status != '1') {
            categoryModels.splice(i, 1)
          }
        }
        const { tabName = '' } = item || {}
        contentInfo.push({ name: tabName })
        _agg_dtos = index
      })
    }

    vLog.log('UNION_LIST initUnionInfo _agg_dtos,contentInfo >>>', _agg_dtos, contentInfo)
    if (_agg_dtos > 1 || contentInfo.length > 1) {
      _lView = 'category-line'
      if (modePlat) {
        _lView = 'single-line'
      }
    }

    vLog.log('UNION_LIST initUnionInfo _cModels >>>', _cModels)
    if (_cModels.length > 1) {
      _lView = 'subnav-line'
      if (modePlat) {
        _lView = 'category-line'
      }
    }

    this.setData({
      list: data,
      contentInfo,
      lView: _lView,
      currNextModels: _cModels,
      type: _cRealm || '',
      targetTabId: _targetTabId,
      nextTabId: _nextTabId,
    })

    if (_cModels && _cModels.length > 0) {
      const { categoryId, id } = _cModels[initNextIdx] || {}
      return this.getListById(_cRealm || "", categoryId || id || '')
    }
  },

  reInstallSingleCategory(type = '', data = [], success = true, msg = '') {
    vLog.log('UNION_LIST reInstallSingleCategory type, data, success, msg >>>', type, data, success, msg)
    let { targetTabId = '' } = this.data
    if (!success) {
      return wx.showToast({
        title: msg || '',
        icon: "none"
      })
    }
    let dataSource = []
    let TabCur = 0

    if (data && data.length) {
      data.forEach((item, index) => {
        const { status = '', showStatus, categoryId = '', id = '' } = item || {}
        let _targetTabId = categoryId || id || ''
        if (status === 'PUBLISHED' || showStatus == 1) {
          dataSource.push(item)
        }
        if (targetTabId == _targetTabId) {
          TabCur = index;
        }
        if (!targetTabId) {
          targetTabId = _targetTabId
        }
      })
    }

    this.setData({
      TabCur,
      contentInfo: dataSource,
      banner: dataSource[TabCur]?.avatar || '',
      targetTabId,
      type,
    }, () => this.handleScrollTopper())

    if (dataSource && dataSource.length) {
      const { id = '', categoryId = '' } = dataSource[TabCur] || {}
      let cId = categoryId || id
      return this.getListById(type, cId)
    } else {
      this.setData({ currList: [] })
    }
  },

  handleScrollTopper() {
    const that = this
    that.setData({
      scrollTop: 0
    })
  },

  async getListById(type = '', id = '') {
    vLog.log('UNION_LIST getListById type,id >>>', type, id)
    const {
      hasLogin,
      wInfo,
      currNextModels,
      listType,
      page = 0,
      currTotalPages = 1,
      pageSize = 20,
      initNextIdx = 0,
      contentInfo = {},
      TabCur = 0,
    } = this.data

    let params = {
      page,
      pageSize
    }

    vLog.log('UNION_LIST getListById this.data  >>>', this.data)
    if (!page) {
      this.setData({
        currList: []
      });
    }

    switch (type) {
      case RealmType.ARTICLE: {
        params.localCategoryId = id
        interaction.showLoading('加载中...')
        const { msg, param, success } = await getNewsList(params)
        interaction.hideLoading()
        vLog.log('UNION_LIST LIVE msg, rows, code >>>', msg, param, success)
        return this.addToCurrList(msg, param?.content, success, true)
      }

      case RealmType.LIVE: {
        const { firstOrgId = '' } = wInfo
        params = {
          start: page,
          limit: pageSize,
          wbsCategoryId: id,
          channelIds: [`${hasLogin ? firstOrgId : ''}`]
        }
        if (!params.start) {
          params.start = 1
        }
        if (!firstOrgId) {
          delete params.channelIds
        }
        vLog.log('UNION_LIST LIVE getLiveList params >>', params)
        interaction.showLoading('加载中...')
        const { msg, rows, code } = await getLiveList(params)
        interaction.hideLoading()
        vLog.log('UNION_LIST LIVE msg, rows, code >>>', msg, rows, code)
        return this.addToCurrList(msg, rows, code === 0, false)
      }

      case RealmType.POSTER: {
        params.categoryId = id
        interaction.showLoading('加载中...')
        const { msg, param, success } = await getPosterList(params)
        interaction.hideLoading()
        vLog.log('UNION_LIST POSTER msg, data, success >>>', msg, param, success)
        return this.addToCurrList(msg, param?.content, success, true)
      }

      case RealmType.MULTI_COURSE: {
        params.categoryId = id
        interaction.showLoading('加载中...')
        const { msg, data, success } = await getMulitList(params)
        interaction.hideLoading()

        vLog.log('UNION_LIST MULTI_COURSE msg, data, success >>>', msg, data, success)
        return this.addToCurrList(msg, data?.content, success, true)
      }

      case RealmType.MARKET_PLAN: {
        params.categoryId = id
        let _cType = ''
        if (listType === 'UNION' && currNextModels && currNextModels.length) {
          const { type: cType = '' } = currNextModels[initNextIdx] || {}
          _cType = cType
        }

        if ((contentInfo[TabCur]?.type === "MARKET_PLAN_EXCLUSIVE_CATEGORY") || (_cType === "MARKET_PLAN_EXCLUSIVE_CATEGORY") && hasLogin) {
          const { firstOrgId = '' } = wInfo || {}
          params.orgId = firstOrgId
          this.setData({ orgId: firstOrgId })
        }
        const api = hasLogin ? getRecommendList : getHomeMarketPlanList
        if (currTotalPages <= page) {
          this.setData({
            refreshing: false,
            nomore: true,
            loadmoring: false
          })
          return
        }
        interaction.showLoading('加载中...')
        const { msg, param, success } = await api(params)
        interaction.hideLoading()
        vLog.log('UNION_LIST MARKET_PLAN msg, rows, code >>>', msg, param, success)
        return this.addToCurrList(msg, param?.content, success, '', param?.totalPages)
      }

      default:
        break
    }
  },

  addToCurrList(msg = '', _currList = [], success = true, showTime = '', currTotalPages = 1) {
    vLog.log('UNION_LIST addToCurrList msg, _currList, success, showTime, currTotalPages >>>', msg, _currList, success, showTime, currTotalPages)
    let { pageSize, page, currList, contentInfo = [], currNextModels = [], lView, modePlat } = this.data
    this.setData({
      refreshing: false,
      loadmoring: false,
      currTotalPages,
    });

    if (!success) {
      return interaction.showToast(msg || '')
    }

    if (_currList && _currList.length < pageSize) {
      this.setData({
        nomore: true
      });
    }

    if (showTime) {
      _currList = _currList.map(item => ({
        ...item,
        timeCreatedStr: dayJs(item?.timeCreated || new Date()).format('YYYY-MM-DD'),
      }))
    }
    let _lView = lView
    if (contentInfo.length > 1) {
      _lView = 'category-line'
      if (modePlat) {
        _lView = 'single-line'
      }
    }

    if (currNextModels.length > 1) {
      _lView = 'subnav-line'
      if (modePlat) {
        _lView = 'category-line'
      }
    }

    vLog.log('UNION_LIST end this.data >>>', this.data)
    if (page > 0) {
      currList = currList.concat(_currList);
      this.setData({
        currList,
        lView: _lView,
      });
    } else {
      this.setData({
        currList: _currList,
        lView: _lView,
      });
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  handlePullDownRefresh() {
    const {
      type,
      contentInfo,
      TabCur,
      listType,
      currNextModels,
      initNextIdx = 0,
      unionContent
    } = this.data

    if (!contentInfo.length) {
      this.setData({ refreshing: false })
      return;
    }
    this.data.page = 0;

    const { id = '', categoryId = '' } = contentInfo[TabCur] || {}
    let cId = id || categoryId || ''
    this.setData({
      nomore: false,
      currTotalPages: 1,
      currList: []
    }, () => {
      if (listType === 'SINGLE') {
        return this.getListById(type || unionContent.type || '', cId)
      }

      if (listType === 'UNION') {
        const { categoryId, id } = currNextModels[initNextIdx] || {}
        return this.getListById(type || '', categoryId || id || '')
      }
    })
  },

  //加载更多
  handleLoadMore() {
    const {
      contentInfo,
      type,
      TabCur,
      loadmoring,
      listType,
      currNextModels,
      initNextIdx = 0,
    } = this.data
    vLog.log('UNION_LIST handleLoadMore >>', this.data)

    if (contentInfo.length == 0 || loadmoring) {
      this.setData({ refreshing: false })
      return;
    }
    this.data.page++;

    const { id = '', categoryId = '' } = contentInfo[TabCur] || {}
    let cId = id || categoryId || ''
    this.setData({
      nomore: false,
      loadmoring: true
    }, () => {
      if (listType === 'SINGLE') {
        return this.getListById(type, cId)
      }

      if (listType === "UNION") {
        const { categoryId, id } = currNextModels[initNextIdx] || {}
        return this.getListById(type || '', categoryId || id || '')
      }
    });
  },

  // 切换一级 tab
  tabTopSelect(e = {}) {
    let {
      contentInfo,
      type,
      listType,
      initNextIdx,
      list: {
        aggCateRelDTOS = []
      },
      lView,
      modePlat,
      TabCur
    } = this.data

    vLog.log('UNION_LIST tabTopSelect this.data >>>', this.data)
    const { index = 0 } = e.detail
    this.data.page = 0;

    const {
      avatar: banner = '',
      id = '',
      categoryId = ''
    } = contentInfo[index]

    if (index !== TabCur) {
      initNextIdx = 0
    }

    let _lView = lView
    let _cRealm = ''
    let _cModels = []
    let _targetTabId = ''
    let _nextTabId = ''
    let cId = categoryId || id || ''

    let _agg_dtos = 0
    if (listType === 'UNION' && aggCateRelDTOS && aggCateRelDTOS.length) {
      aggCateRelDTOS.forEach((item, aIndex) => {
        const { categoryModels = [], realm = '', id: targetTabId = '' } = item || {}
        if (aIndex === index) {
          _cModels = categoryModels
          _cRealm = realm
          _targetTabId = targetTabId
        }
        _agg_dtos = aIndex
      })
    }

    vLog.log('============ UNION_LIST tabTopSelect _cModels,initNextIdx >>>', _cModels, initNextIdx)
    if (listType === 'UNION' && _cModels && _cModels.length) {
      const { categoryId, id } = _cModels[initNextIdx] || {}
      _nextTabId = categoryId || id || ''
    }

    if (_agg_dtos > 1 || contentInfo.length > 1) {
      _lView = 'category-line'
      if (modePlat) {
        _lView = 'single-line'
      }
    }

    if (_cModels.length > 1) {
      _lView = 'subnav-line'
      if (modePlat) {
        _lView = 'category-line'
      }
    }

    if (listType === 'UNION') {
      this.setData({
        targetTabId: _targetTabId,
        initNextIdx: 0,
        lView: _lView,
        currNextModels: _cModels,
        type: _cRealm || '',
        nextTabId: _nextTabId,
      })
    }

    this.setData({
      TabCur: index,
      targetTabId: cId,
      currList: [],
      nomore: false,
      currTotalPages: 1,
      lView: _lView,
      banner,
    }, () => {
      this.handleScrollTopper()

      if (listType === 'SINGLE') {
        return this.getListById(type, cId)
      }

      if (listType === 'UNION') {
        return this.getListById(_cRealm || '', _nextTabId)
      }
    })
  },

  // 切换二级 tab
  clickCategory(e = {}) {
    const { type, currNextModels } = this.data
    const { index = 0 } = e.detail
    this.data.page = 0;
    let _nextTabId = ''

    if (currNextModels && currNextModels.length) {
      const { categoryId, id } = currNextModels[index] || {}
      _nextTabId = categoryId || id || ''
    }

    this.setData({
      initNextIdx: index,
      nextTabId: _nextTabId,
      currList: [],
      nomore: false,
      currTotalPages: 1,
    }, () => {
      this.handleScrollTopper()
      return this.getListById(type, _nextTabId)
    })
  },

  doSearch() {
    vLog.log('UNION_LIST doSearch data,props >>>>', this.data, this.props)
    const { contentInfo, type, TabCur, orgId, OID, targetTabId = '' } = this.data
    const { type: nType = '', id = '', categoryId = '' } = contentInfo[TabCur]
    let isNeedOrgid = (nType === "MARKET_PLAN_EXCLUSIVE_CATEGORY")
    let cId = targetTabId || categoryId || id || ''

    const params = {
      categoryId: cId,
      type,
      orgId: OID || orgId,
      isNeedOrgid,
      fromPage: 'SHARE_LIST'
    }

    wx.navigateTo({
      url: `/packages-common/pages/common/search/search?${qs.stringify(params)}`,
    })
  },

  async onHandleAction(e = {}) {
    if (!isEmptyObject(e)) {
      const {
        dataset: {
          item = {},
          type = ''
        }
      } = e.currentTarget || {}
      if (type) {
        const passParams = {
          ...item,
          action: `action://share/${type}`
        }
        return app.globalData.emitter.emit(SEND_EVENT_TO_POLYMERS, { detail: passParams })
      }
      return app.globalData.emitter.emit(SEND_EVENT_TO_POLYMERS, { detail: { ...e.detail } })
    }
    return
    vLog.log('UNION_LIST onHandleAction e >>>', e)
    if (!e.hasOwnProperty('detail')) {
      e = { detail: { ...e } }
    }
    const { hasLogin } = this.data
    let event = { ...e }

    if (!('id' in event.detail) && !('courseId' in event.detail)) {
      const {
        dataset: {
          item = {},
          type = ''
        }
      } = e.currentTarget

      event = {
        ...e,
        detail: {
          ...item,
          action: `action://share/${type}`
        }
      }
    }
    vLog.log('UNION_LIST event >>>', event)
    if (!isEmptyObject(event)) {
      const {
        detail: {
          id = '',
          action = '',
          relation = {},
          courseId = '',
          categoryId: _categoryId = '',
          categoryIds: _categoryIds = '',
          uri = '',
          style: pageStyle = 'SCROLL_ANCHOR_VIEW',
          cId = '',
          cName = '',
          mName = '',
        }
      } = event || {}

      let _planId = ''
      let _pageStyle = pageStyle

      const clueParams = {
        fromTab: "HOME",
        cId,
        cName,
        mName
      }

      const _clues = decodeURIComponent(JSON.stringify(app.getCluesInfo(clueParams)))
      const { type, analyzeRes } = analyzeAction(action)

      let params = {
        token: getToken(),
        openid: getOpenId(),
        unionid: getUnionID(),
        wechatInfoId: getWechatInfoId(),
        pageType: type,
        banShare: 1
      }

      if (_categoryId) {
        params.categoryId = _categoryId
      }

      switch (type) {
        // 资讯详情
        case "AppAdvNewsDetail": {
          params.perfix = `${wbs.gfH5}/share/AppAdvNewsDetail`

          if (id) {
            params.value = id
          }
          const { value = '' } = analyzeRes || {}
          if (value) {
            params.value = value
          }
          if (_clues) {
            params.clueParams = _clues
          }

          if (!hasLogin) {
            params.TAG = 'AppAdvNewsDetail'
            params.fromTab = 'HOME'
            params.targetPath = '/pages/common/webview/webPage'
            params.pageFlag = 'SHARE_LIST'
            return breakIn({ name: 'doCheckJumpIn', params })
          }
          return breakIn({ name: 'doRouteWebPage', params })
        }

        // 课程详情
        case "AppAdvInvestorTrainCom": {
          params.perfix = `${wbs.gfH5}/share/AppAdvInvestorTrainCom`
          if (_categoryId || id) {
            params.value = id
            params.shareConfig = true
            params.categoryId = _categoryId || _categoryIds || ''
          }
          if (courseId) {
            const { categoryId, categoryIds = '', shareConfig = true, } = relation
            params.value = courseId
            params.shareConfig = shareConfig
            params.categoryId = categoryId || categoryIds
          }
          const { value = '', shareConfig = true, categoryId = '', categoryIds = '', } = analyzeRes
          if (value) {
            params.value = value
            params.shareConfig = shareConfig
            params.categoryId = categoryId || categoryIds
          }
          if (_clues) {
            params.clueParams = _clues
          }

          if (!hasLogin) {
            params.TAG = 'AppAdvInvestorTrainCom'
            params.fromTab = 'HOME'
            params.targetPath = '/pages/common/webview/webPage'
            params.pageFlag = 'SHARE_LIST'
            return breakIn({ name: 'doCheckJumpIn', params })
          }
          return breakIn({ name: 'doRouteWebPage', params })
        }

        // (营销资料)产品详情
        case 'advProductDataNewDetail':
        case 'advProductDataDetail': {
          const { value = '' } = analyzeRes
          if (value || id) {
            params.value = value || id
          }

          if (_planId) {
            const { success, param = {} } = await getMarketingPlanInfo({ planId: _planId })
            if (success && !isEmptyObject(param)) {
              const { style = '' } = param || {}
              _pageStyle = style
            }
          }

          params.perfix = `${wbs.gfH5}/share/${PRODUCT_PAGE_TYPE[_pageStyle]}`
          params.title = '营销资料'
          if (_clues) {
            params.clueParams = _clues
          }

          if (!hasLogin) {
            params.TAG = 'advProductData'
            params.fromTab = 'HOME'
            params.targetPath = '/pages/common/webview/webPage'
            params.pageFlag = 'SHARE_LIST'
            return breakIn({ name: 'doCheckJumpIn', params })
          }
          return breakIn({ name: 'doRouteWebPage', params })
        }

        // 直播详情
        case "AppAdvLive": {
          params.perfix = `${wbs.gfH5}/share/AppAdvLive`
          if (id) {
            params.id = id
          }
          const { value = '' } = analyzeRes || {}
          if (value) {
            params.value = value
          }
          params.title = '直播详情'
          if (_clues) {
            params.clueParams = _clues
          }

          if (!hasLogin) {
            params.TAG = 'AppAdvLive'
            params.fromTab = 'HOME'
            params.targetPath = '/pages/common/webview/webPage'
            params.pageFlag = 'SHARE_LIST'
            return breakIn({ name: 'doCheckJumpIn', params })
          }
          return breakIn({ name: 'doRouteWebPage', params })
        }

        // 海报详情
        case "AppAdvPosterInfo": {
          if (uri) {
            params.perfix = `${wbs.gfH5}/share/advBoutiquePosterDetail`
            params.value = id
            params.url = uri
            params.title = '海报详情'
            params.pageFlag = 'SHARE_LIST'
          }

          if (_clues) {
            params.clueParams = _clues
          }

          if (!hasLogin) {
            params.TAG = 'AppAdvPosterInfo'
            params.fromTab = 'HOME'
            params.targetPath = '/pages/common/webview/webPage'
            params.pageFlag = 'SHARE_LIST'
            return breakIn({ name: 'doCheckJumpIn', params })
          }
          return breakIn({ name: 'doRouteWebPage', params })
        }
      }
    }
  },

  onShow() {
    vLog.log('UNION_LIST onShow data >>>>', this.data)
    const { hasLogin } = this.data
    let customerType = getCurrRoleType()

    if (!hasLogin || customerType === 'AGENCY') {
      wx.hideShareMenu({
        menus: ['shareAppMessage', 'shareTimeline']
      })
    }
  },

  scrollProbe() {
    let refreshTopDistance = 3
    vLog.log('UNION_LIST scrollProbe data >>>', this.data)
    let sTimer = setTimeout(() => {
      clearTimeout(sTimer)
      const { lView, listType } = this.data
      if (listType === 'SINGLE') {
        if (lView === 'subnav-line') {
          refreshTopDistance = 3
        }
      }

      if (listType === 'UNION') {
        if (lView === 'subnav-line') {
          refreshTopDistance = 4.8
        }
      }

      vLog.log('UNION_LIST scrollProbe refreshTopDistance >>>', refreshTopDistance)
      this.setData({
        refreshTopDistance
      })
    }, 2000)
  }
});

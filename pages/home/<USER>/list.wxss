.list-container{
    padding-bottom: 25vh;
}

.content-box{
    margin: 0 24rpx;
    background-color: #fff;
    border-radius: 8rpx;
}

.content-nav{
    width: 100%;
    z-index: 9999;
    position: fixed;
    height: 100rpx;
    overflow: hidden;
}

.nav{
    color: #999;
    font-size: 32rpx;
    background-color: #fff;
    white-space: nowrap;
    display: flex;
    flex-direction: row;
    border-bottom: 1px solid #eee;
    border-top-left-radius: 10rpx;
    border-top-right-radius: 10rpx;
}

.nav .current-item{
    display: flex;
    flex: 1;
    flex-direction: column;
    height: 100rpx;
    justify-content: center;
    align-items: center;
    position: relative;
}

.nav .current-item .underline{
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 50rpx;
    height: 4rpx;
}

.display-none{
    display: none;
}

.text-black{
    font-size: 16Px;
    color: #333;
    font-weight: 500;
    font-family: PingFangSC-Medium;
}

.subNav{
    background-color: #fff;
    width: 100%;
    padding: 0 24rpx;
    margin-top: 100rpx;
    position: fixed;
    z-index: 9999;
}

.content-list{
    display: flex;
    flex-direction: column;
    background-color: #fff;
    border-radius: 10px;
    margin: 0 14px 20px;
    box-shadow: 0 4px 10px 10px #F7F7F7;
}

.content-poster-list{
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    /*margin: 0 0 0 14px;*/
    flex-wrap: wrap;
}

.content-media-list{
    display: flex;
    flex-direction: column;
    background-color: #fff;
    border-radius: 10px;
    margin: 0 14px 14px;
    overflow: hidden;
}

.search-block{
    display: flex;
    flex-direction: row;
    align-items: center;
    background-color: #fff;
    max-height: 15vw;
    padding: 12rpx 24rpx;
    border-radius: 8vw;
    border: 1px solid #EEEEEE;
    margin: 0 24rpx 14px;
}

.search-content{
    display: flex;
    flex-direction: row;
    align-items: center;
    flex: 1;
}

.search-content-tips{
    font-size: 14Px;
    color: #999999;
    font-weight: 400;
    margin-right: 10rpx;
    font-family: PingFangSC-Regular;
}

.search-icon{
    width: 40rpx;
    height: 40rpx;
    margin: 0 6rpx;
}

.banner-img{
    width: 92%;
    margin: 0 14px 14px;
    border-radius: 10rpx;
}

.empty-class{
    width: 100%;
    height: 90vh;
    padding-top: 200px;
    background-color: #fff;
}

.single-line{
    margin-top: 14px;
}

.category-line{
    margin-top: 64px;
}

.subnav-line{
    margin-top: 115px;
}

.list-container {
  background-color: gray;
}

.content-box {
  margin: 0 24rpx;
  background-color: #fff;
  border-radius: 8rpx;
}

.search {
  display: flex;
  padding: 0rpx 28rpx;
  z-index: 99999;
  position: relative;
  background-color: #fff;
  margin-bottom: -15rpx;
  top: 0;
}

.search-block {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  background-color: #fff;
  min-height: 40Px;
  padding: 12rpx 24rpx;
  /*margin: 28rpx 0 10rpx;*/
  margin: 20rpx 0;
  border-radius: 25Px;
  border: 1px solid #EEEEEE;
}

.search-icon {
  width: 40rpx;
  height: 40rpx;
  margin: 0 6rpx;
}

.search-content-tips {
  font-size: 14Px;
  color: #999999;
  font-weight: 400;
  margin-right: 10rpx;
  font-family: PingFangSC-Regular;
}

.content-nav {
  width: 100%;
  z-index: 9999;
  position: fixed;
  height: 100rpx;
  overflow: hidden;
}

.nav {
  color: #999;
  font-size: 32rpx;
  background-color: #fff;
  white-space: nowrap;
  display: flex;
  flex-direction: row;
  border-bottom: 1px solid #eee;
  border-top-left-radius: 10rpx;
  border-top-right-radius: 10rpx;
}

.current-item {
  height: 80rpx;
}

.underline {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  padding-top: 10px;
  width: 50rpx;
  height: 4rpx;
}

.display-none {
  display: none;
}

.text-black {
  font-size: 16Px;
  color: #333;
  font-weight: 500;
  font-family: PingFangSC-Medium;
}

.subNav {
  background-color: #fff;
  width: 100%;
  position: fixed;
  z-index: 9999;
}

.content-list {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
  margin: 0 14px 20px;
  /* box-shadow: 0 4px 10px 10px #F7F7F7; */
}

.content-poster-list {
  display: flex;
  flex-direction: row;
  /*margin: 0 0 0 14px;*/
  justify-content: flex-start;
  flex-wrap: wrap;
  overflow: hidden;
  border-radius: 10px;
}

.content-media-list {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 10px;
  margin: 0 14px 14px;
  overflow: hidden;
}

.empty-class {
  width: 100%;
  height: 90vh;
  padding-top: 200px;
  background-color: #fff;
}

.single-line {
  margin-top: 14px;
}

.category-line {
  margin-top: 64px;
}

.subnav-line {
  margin-top: 115px;
}

.single-line-qy {
  margin-top: 64px;
}

.category-line-qy {
  margin-top: 114px;
}

.subnav-line-qy {
  margin-top: 165px;
}
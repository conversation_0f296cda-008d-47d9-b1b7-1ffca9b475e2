<wxs src="../../../wxs/common.wxs" module="tools" />
<nb-page showNavBar="{{true}}" navShowBack="{{!contentLibrary}}" showHomeIcon="{{contentLibrary}}" navTitle="{{title||list.listName}}" showTabBar="{{false}}">
  <view wx:if="{{contentLibrary}}" class="search">
    <view class="search-block" bind:tap="doSearch">
      <image src="../../../imgs/empty/<EMAIL>" class="search-icon" mode="aspectFill" />
      <view class="search-content">
        <text class="search-content-tips">{{'请输入关键词'}}</text>
      </view>
    </view>
  </view>
  <view class="content-nav" wx:if="{{contentInfo.length >1}}">
    <scrollable-tabview class=".current-item {{index==TabCur?'text-black':''}}" tabs='{{contentInfo}}' bind:tabNavClick='tabSelect' backgroundColor="#fff" activeColor="#333" color="#999" isFromList="true" barBgColor="{{$state.themeColor}}" currentIndex="{{TabCur}}" />
  </view>

  <view class="subNav" style="margin-top:{{contentInfo.length >1?'48px':0}}" wx:if="{{list.aggCateRelDTOS[TabCur].categoryModels.length >1}}">
    <category tabs="{{list.aggCateRelDTOS[TabCur].categoryModels}}" bind:tabBarClick='clickCategory' color="{{$state.themeColor}}" currentIndex="{{initNextIdx}}" showBorder="{{true}}" />
  </view>

  <x-scroll-view contentLibrary="{{contentLibrary&&list.aggCateRelDTOS[TabCur].categoryModels.length >1}}" enableRefresh="{{true}}" class="list-container" elementHeight="{{list.aggCateRelDTOS[TabCur].categoryModels.length >=1?contentHeight:contentHeight-50}}" elementMarginTop="{{list.aggCateRelDTOS[TabCur].categoryModels.length >=1?0:50}}" refreshing="{{refreshing}}" loadmoring="{{loadmoring}}" nomore="{{nomore}}" enableLoadMore="{{true}}" resetting="{{true}}" scrollTop='{{scrollTop}}' refreshTopDistance="{{contentInfo.length>1 && currNextModels.length >1?4.8:3}}" hasTabBarInfo="{{contentInfo.length>1 || currNextModels.length >1}}" bindpulluploadmore="handleLoadMore" bindpulldownrefresh="handlePullDownRefresh">
    <view wx:if="{{!currList ||!currList.length || showViolations&&(list.aggCateRelDTOS[TabCur].realm === 'LIVE' || list.aggCateRelDTOS[TabCur].realm === 'MULTI_COURSE')}}" class="empty-class">
      <emptyBlock tips="{{isRefreshing?'加载中......':'暂无数据'}}" />
      <vertical-space />
    </view>
    <view wx:else class="{{modePlat?lViewIos:lView}}">
      <view class="content-list" wx:if="{{currList.length>0 && list.aggCateRelDTOS[TabCur].realm === 'ARTICLE'}}">
        <view wx:for="{{currList}}" wx:for-item="item" wx:for-index="id" wx:key="id">
          <news data="{{item}}" bind:onItemClick="onHandleAction" isLast="{{id === currList.length-1}}" elementType="LIST" nomore="{{nomore}}" />
        </view>
      </view>
      <view class="content-media-list" wx:if="{{currList.length>0 && list.aggCateRelDTOS[TabCur].realm === 'LIVE'}}" wx:for="{{currList}}" wx:for-item="item" wx:for-index="id" wx:key="id">
        <live item="{{item}}" data-type="{{'AppAdvLive'}}" data-item="{{item}}" bind:tap="onHandleAction" isLast="{{id === currList.length-1}}" elementType="LIST" nomore="{{nomore}}" />
      </view>
      <view class="content-list" wx:if="{{currList.length>0 && list.aggCateRelDTOS[TabCur].realm === 'MULTI_COURSE'}}">
        <view wx:for="{{currList}}" wx:for-item="item" wx:for-index="id" wx:key="id">
          <multimedia data="{{item}}" bind:onItemClick="onHandleAction" isLast="{{id === currList.length-1}}" elementType="LIST" nomore="{{nomore}}" />
        </view>
      </view>
      <view class="content-poster-list" wx:if="{{currList.length>0 && list.aggCateRelDTOS[TabCur].realm === 'POSTER'}}">
        <view wx:for="{{currList}}" wx:for-item="item" wx:for-index="id" wx:key="id">
          <poster item="{{item}}" data-type="{{'AppAdvPosterInfo'}}" data-item="{{item}}" bind:tap="onHandleAction" isLast="{{id === currList.length-1}}" elementType="LIST" nomore="{{nomore}}" />
        </view>
      </view>
      <view class="content-list" wx:if="{{currList.length>0 && list.aggCateRelDTOS[TabCur].realm === 'MARKET_PLAN'}}">
        <view wx:for="{{currList}}" wx:for-item="item" wx:for-index="id" wx:key="id">
          <marketPlan data="{{item}}" bind:onItemClick="onHandleAction" isLast="{{id === currList.length-1}}" elementType="LIST" nomore="{{nomore}}" />
        </view>
      </view>
    </view>
    <view style="width:100%;height:28px"></view>
  </x-scroll-view>

  <floatBtn wx:if="{{hasLogin && list.aggCateRelDTOS[TabCur].realm !== 'POSTER' && customerType!=='AGENCY' &&!contentLibrary &&shareConfig}}" disabled="{{floatDisabled}}" bind:onFloatClick="onHandleFloat" />

  <netMonitor wx:if="{{!sheetDisabled}}" bind:onSheetCancel="onHandleCancel" bind:onSheetPoster="onHandleSharePoster" />
  <van-toast id="van-toast" />
</nb-page>
<wxs src="../../../wxs/common.wxs" module="tools"/>
<nb-page showNavBar="{{true}}"
         navShowBack="{{true}}"
         navTitle="{{name}}"
         showTabBar="{{false}}">
  <view class="content-nav" wx:if="{{contentInfo.length>1}}">
    <scrollable-tabview
        class=".current-item {{index==TabCur?'text-black':''}}"
        tabs='{{contentInfo}}'
        bind:tabNavClick='tabSelect'
        backgroundColor="#fff"
        activeColor="#333"
        color="#999"
        isFromList="true"
        barBgColor="{{$state.themeColor}}"
        currentIndex="{{TabCur}}"/>
  </view>
  <x-scroll-view
      enableRefresh="{{true}}"
      class="list-container"
      style="margin-bottom: 25vh;"
      elementHeight="{{contentHeight}}"
      refreshing="{{refreshing}}"
      loadmoring="{{loadmoring}}"
      nomore="{{nomore}}"
      enableLoadMore="{{true}}"
      resetting="{{true}}"
      scrollTop='{{scrollTop}}'
      refreshTopDistance="{{contentInfo.length>1?3:1}}"
      hasTabBarInfo="{{contentInfo.length>1}}"
      bindpulluploadmore="handleLoadMore"
      bindpulldownrefresh="handlePullDownRefresh">

    <view class="search-block"
          style="margin-top:{{contentInfo.length>1?modePlat=='ios'?'14px':'64px':'14px'}}"
          wx:if="{{type !== 'POSTER'}}"
          bind:tap="doSearch">
      <image src="../../../imgs/empty/<EMAIL>" class="search-icon" mode="aspectFill"/>
      <view class="search-content">
        <text class="search-content-tips">{{placeholder || '请输入关键词'}}</text>
      </view>
    </view>

    <image class="banner-img"
           wx:if="{{banner}}"
           src="{{banner}}"
           mode="widthFix"/>
    <view wx:if="{{!currList || !currList.length || showViolations}}"
          class="empty-class">
      <emptyBlock wx:if="{{!refreshing}}" tips="暂无数据" showBGColor="false"/>
      <vertical-space/>
    </view>

    <view wx:else>
      <view class="content-list"
            wx:if="{{currList.length>0 && type === 'ARTICLE'}}">
        <view
            wx:for="{{currList}}"
            wx:for-item="item"
            wx:for-index="id"
            wx:key="id">
          <news
              data="{{item}}"
              bind:onItemClick="onHandleAction"
              isLast="{{id === currList.length-1}}"
              elementType="LIST"
              nomore="{{nomore}}"
          />
        </view>
      </view>
      <view class="content-media-list"
            wx:if="{{currList.length>0 && type === 'LIVE'}}"
            wx:for="{{currList}}"
            wx:for-item="item"
            wx:for-index="id"
            wx:key="id">
        <live
            item="{{item}}"
            data-type="{{'AppAdvLive'}}"
            data-item="{{item}}"
            bind:tap="onHandleAction"
            isLast="{{id === currList.length-1}}"
            elementType="LIST"
            nomore="{{nomore}}"
        />
      </view>
      <view class="content-list"
            wx:if="{{currList.length>0 && type === 'MULTI_COURSE'}}">
        <view
            wx:for="{{currList}}"
            wx:for-item="item"
            wx:for-index="id"
            wx:key="id">
          <multimedia
              data="{{item}}"
              data-item="{{item}}"
              bind:onItemClick="onHandleAction"
              isLast="{{id === currList.length-1}}"
              elementType="LIST"
              nomore="{{nomore}}"
          />
        </view>
      </view>
      <view class="content-poster-list"
            wx:if="{{currList.length>0 && type === 'POSTER'}}"
            style="margin-top:{{contentInfo.length>1?modePlat=='ios'?'14px':'64px':'14px'}}">
        <view
            wx:for="{{currList}}"
            wx:for-item="item"
            wx:for-index="id"
            wx:key="id">
          <poster
              item="{{item}}"
              data-type="{{'AppAdvPosterInfo'}}"
              data-item="{{item}}"
              bind:tap="onHandleAction"
              isLast="{{id === currList.length-1}}"
              elementType="LIST"
              nomore="{{nomore}}"
          />
        </view>
      </view>
      <view class="content-list"
            wx:if="{{currList.length>0 && type === 'MARKET_PLAN'}}">
        <view
            wx:for="{{currList}}"
            wx:for-item="item"
            wx:for-index="id"
            wx:key="id">
          <marketPlan
              data="{{item}}"
              bind:onItemClick="onHandleAction"
              isLast="{{id === currList.length-1}}"
              elementType="LIST"
              nomore="{{nomore}}"
          />
        </view>
      </view>
    </view>
    <view style="width:100%;height:28px"></view>
  </x-scroll-view>

  <floatBtn wx:if="{{hasLogin && type !== 'POSTER' && customerType!=='AGENCY' && shareConfig}}" disabled="{{floatDisabled}}" bind:onFloatClick="onHandleFloat" />

  <netMonitor
      wx:if="{{!sheetDisabled}}"
      bind:onSheetCancel="onHandleCancel"
      bind:onSheetPoster="onHandleSharePoster"
  />
  <van-toast id="van-toast"/>
</nb-page>



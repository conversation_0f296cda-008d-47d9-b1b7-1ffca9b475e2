import {
  getCurrRoleType,
  getFirstPath,
  getTab<PERSON>arList,
  getUser,
  getUserLogin,
  getViolationsPhone
} from "../../../common/utils/userStorage";

import {
  systemtInfo,
  global,
  util,
  enums,
  storage,
  eventName,
  qs,
  interaction,
  vLog,
  breakIn
} from '../../../common/index';

import {
  getNewsList,
  getMulitList,
  getArticleClassify,
  getMarketPlanClassify,
  getMulitClassify,
  getPosterClassify,
  getPosterList,
  getRecommendList,
  getHomeMarketPlanList
} from "../../../common/nb/home";

import {
  getLiveList,
  getCategoryListByIds
} from "../../../common/network/api";

const dayJs = require('dayjs')
const { platform } = wx.getSystemInfoSync()

const {
  screenHeight,
  footHeight,
} = systemtInfo

const {
  SEND_EVENT_TO_POLYMERS
} = eventName

const {
  SHARE_BLOCK_DEFAULT,
  RealmType,
  REVIEW_PHONE,
  SHARE_IMG_DEFAULT
} = enums

const {
  rpx2px,
  isEmptyObject
} = util;

const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    modePlat: platform == "ios" ? "ios" : "devtools-android",
    contentHeight: rpx2px(screenHeight - footHeight),
    showViolations: false,

    list: [],
    TabCur: 0,  //  一级分类idx
    refreshing: false,
    nomore: false,
    loadmoring: false,
    hasLogin: false,
    customerType: '',

    contentInfo: [],
    currList: [],
    page: 0,
    pageSize: 21,
    currTotalPages: 1,

    type: RealmType.ARTICLE,
    name: "列表",
    banner: '',
    orgId: '',
    fromTab: '',
    targetTabId: '',
    redirectBack: '',

    floatDisabled: false,
    sheetDisabled: true,
    shareConfig: true,

    currCoverUrl: SHARE_IMG_DEFAULT,
    lView: 'single-line',
    shareTitle: '广发木棉花'
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    vLog.log('NEWS_LIST options >>>>>', options)
    storage.setStorage(global.STORAGE_GLOBAL_PREVIOUS_PARAMS, options)
    const {
      type = '',
      categoryIds = '',
      name = '',
      fromTab = '',
      targetTabId = '',
      redirectBack = ''
    } = options || {}

    const customerType = getCurrRoleType()
    const hasLogin = getUserLogin()

    this.setData({
      type,
      categoryIds,
      targetTabId,
      hasLogin,
      customerType,
      redirectBack,
      name: decodeURIComponent(name),
      fromTab: decodeURIComponent(fromTab),
      showViolations: REVIEW_PHONE.includes(getViolationsPhone()),
    }, () => {
      return this.getList()
    })

    // this.onSetHide()
  },

  // onSetHide() {
  //   const { customerType } = this.data || {}
  //   return setTimeout(() => ((customerType === 'AGENCY'))
  //     ? wx.hideShareMenu({
  //       menus: ['shareAppMessage', 'shareTimeline']
  //     }) : null
  //     , 1000)
  // },

  async doRefreshHomeInfo() {
    let timer = setTimeout(() => {
      clearTimeout(timer)
      this.setData({
        refreshing: false,
      })
    }, 1000)
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  handlePullDownRefresh: function () {
    const { type, contentInfo, TabCur } = this.data
    if (!contentInfo.length) {
      this.setData({ refreshing: false })
      return;
    }
    this.data.page = 0;

    const { id = '', categoryId = '' } = contentInfo[TabCur] || {}
    let cId = id || categoryId || ''
    this.setData({
      nomore: false,
      currTotalPages: 1,
      currList: []
    }, () => this.getDetail(type, cId))
  },

  //加载更多
  handleLoadMore: function () {
    const { contentInfo, type, TabCur, loadmoring } = this.data
    if (contentInfo.length == 0 || loadmoring) {
      this.setData({ refreshing: false })
      return;
    }
    this.data.page++;
    const { id = '', categoryId = '' } = contentInfo[TabCur] || {}
    let cId = id || categoryId || ''
    this.setData({
      nomore: false,
      loadmoring: true
    }, () => this.getDetail(type, cId));
  },

  /**
   * 获取分类数据
   */
  async getList() {
    const { type, categoryIds } = this.data
    const wInfo = getUser()

    //资讯
    if (type === RealmType.ARTICLE) {
      const params = {
        categoryIds
      }
      interaction.showLoading('加载中...')
      const { msg, param, success } = await getArticleClassify(params)
      interaction.hideLoading()
      this.checkClassify(type, msg, param, success)
    }
    //直播
    else if (type === RealmType.LIVE) {
      let ids = categoryIds.split(',')
      const { firstOrgId = '' } = wInfo || {}
      const params = {
        ids,
        channelIds: [`${firstOrgId}`]
      }
      if (!firstOrgId) {
        delete params.channelIds
      }
      interaction.showLoading('加载中...')
      const { value } = await getCategoryListByIds(params)
      interaction.hideLoading()
      this.checkClassify(type, '', value, true)
    }

    //海报
    else if (type === RealmType.POSTER) {
      const params = {
        categoryIds
      }
      interaction.showLoading('加载中...')
      const { msg, param, code } = await getPosterClassify(params)
      interaction.hideLoading()
      this.checkClassify(type, msg, param, code === 0)
    }

    //音视频
    else if (type === RealmType.MULTI_COURSE) {
      const params = {
        categoryId: categoryIds
      }
      interaction.showLoading('加载中...')
      const { msg, data, success } = await getMulitClassify(params)
      interaction.hideLoading()
      this.checkClassify(type, msg, data, success)
    }

    //营销方案
    else if (type === RealmType.MARKET_PLAN) {
      let allData = []
      interaction.showLoading('加载中...')
      const { msg, success, param = [] } = await getMarketPlanClassify({ categoryIds })
      interaction.hideLoading()
      allData = [].concat(param)
      vLog.log('NEWS_LIST getMarketPlanClassify allData >>>', allData)
      this.checkClassify(type, msg, allData, success)
    }
  },

  checkClassify(type, msg, data = [], success) {
    let { hasLogin, targetTabId = '' } = this.data
    if (!success) {
      return wx.showToast({
        title: msg || '',
        icon: "none"
      })
    }

    let dataSource = []
    let TabCur = 0
    if (data && data.length) {
      data.forEach((item, index) => {
        const { status = '', showStatus, categoryId = '', id = '' } = item || {}
        let _targetTabId = categoryId || id || ''
        if (status === 'PUBLISHED' || showStatus == 1) {
          dataSource.push(item)
        }
        if (targetTabId == _targetTabId) {
          TabCur = index;
        }
        if (!targetTabId) {
          targetTabId = _targetTabId
        }
      })
    }

    let banner = ''
    let currCoverUrl = ''
    let shareTitle = ''
    let _shareConfig = true
    if (!isEmptyObject(dataSource)) {
      const {
        avatar = '',
        shareCoverUrl = '',
        name = '',
        sharePictureUrl = '',
        shareConfig = true
      } = dataSource[TabCur] || {}
      banner = avatar
      currCoverUrl = shareCoverUrl || sharePictureUrl || SHARE_IMG_DEFAULT
      shareTitle = name || '广发木棉花'
      _shareConfig = shareConfig
    }
    let _floatDisabled = !_shareConfig

    this.setData({
      TabCur,
      contentInfo: dataSource,
      shareConfig: _shareConfig,
      floatDisabled: _floatDisabled,
      banner,
      currCoverUrl,
      shareTitle,
      targetTabId
    }, () => {
      if (!_shareConfig) {
        wx.hideShareMenu({
          menus: ['shareAppMessage', 'shareTimeline']
        })
      } else {
        // if (hasLogin) {
          wx.showShareMenu({
            menus: ['shareAppMessage']
          })
        // }
      }

      if (type === 'POSTER') {
        wx.hideShareMenu({
          menus: ['shareAppMessage', 'shareTimeline']
        })
      }
      this.handleScrollToupper()
    })

    if (dataSource && dataSource.length) {
      const { id = '', categoryId = '' } = dataSource[TabCur] || {}
      let cId = categoryId || id
      return this.getDetail(type, cId)
    } else {
      this.setData({ currList: [] })
    }
  },

  getData(msg, _currList, success, showTime = '', currTotalPages = 1) {
    let { pageSize, page, currList } = this.data
    this.setData({
      refreshing: false,
      loadmoring: false,
      currTotalPages,
    });

    if (!success) {
      return wx.showToast({
        title: msg || '',
        icon: "none"
      })
    }

    if (_currList && _currList.length < pageSize) {
      this.setData({
        nomore: true
      });
    }

    if (showTime) {
      _currList = _currList.map(item => ({
        ...item,
        timeCreatedStr: dayJs(item?.timeCreated || new Date()).format('YYYY-MM-DD'),
      }))
    }

    if (page > 0) {
      currList = currList.concat(_currList);
      this.setData({
        currList
      });
    } else {
      this.setData({
        currList: _currList
      });
    }
  },

  async getDetail(type = '', id = '') {
    const { TabCur, hasLogin, page = 0, currTotalPages = 1, pageSize = 20, contentInfo = {} } = this.data
    const wInfo = getUser()

    let params = {
      page,
      pageSize
    }

    if (!page) {
      this.setData({
        currList: []
      });
    }

    //资讯
    if (type === RealmType.ARTICLE) {
      params.localCategoryId = id
      const { msg, param, success } = await getNewsList(params)
      this.getData(msg, param?.content, success, true)
    }

    //直播
    else if (type === RealmType.LIVE) {
      const { firstOrgId = '' } = wInfo || {}
      params = {
        start: page,
        limit: pageSize,
        wbsCategoryId: id,
        channelIds: [`${hasLogin ? firstOrgId : ''}`]
      }
      if (!params.start) {
        params.start = 1
      }
      if (!firstOrgId) {
        delete params.channelIds
      }
      const { msg, rows, code } = await getLiveList(params)
      this.getData(msg, rows, code === 0, false)
    }

    //海报
    else if (type === RealmType.POSTER) {
      params.categoryId = id
      const { msg, param, success } = await getPosterList(params)
      this.getData(msg, param?.content, success, true)
    }

    //音视频 课程
    else if (type === RealmType.MULTI_COURSE) {
      params.categoryId = id
      const { msg, data, success } = await getMulitList(params)
      this.getData(msg, data?.content, success, true)
    }

    //营销方案
    else if (type === RealmType.MARKET_PLAN) {
      params.categoryId = id
      if (contentInfo[TabCur].type === "MARKET_PLAN_EXCLUSIVE_CATEGORY" && hasLogin) {
        const { firstOrgId = '' } = wInfo || {}
        params.orgId = firstOrgId
        this.setData({ orgId: firstOrgId })
      }
      const api = hasLogin ? getRecommendList : getHomeMarketPlanList
      if (currTotalPages <= page) {
        this.setData({
          refreshing: false,
          nomore: true,
          loadmoring: false
        })
        return
      }

      const { msg, param, success } = await api(params)
      this.getData(msg, param?.content, success, '', param?.totalPages)
    }
  },

  handleScrollToupper() {
    const that = this
    that.setData({
      scrollTop: 0
    })
  },

  // 切换一级 tab
  tabSelect(e = {}) {
    const {
      contentInfo,
      type,
      currCoverUrl,
      shareTitle = '',
      shareConfig = true,
      hasLogin
    } = this.data

    const { index = 0 } = e.detail || {}
    this.data.page = 0;
    let _currCoverUrl = currCoverUrl
    let _sTitle = shareTitle
    let _shareConfig = shareConfig

    const {
      avatar: banner = '',
      id = '',
      shareCoverUrl = '',
      sharePictureUrl = '',
      categoryId = '',
      name: _shareTitle = '',
      shareConfig: sConfig = true
    } = contentInfo[index] || {}
    _currCoverUrl = shareCoverUrl || sharePictureUrl || SHARE_IMG_DEFAULT
    _sTitle = _shareTitle
    _shareConfig = sConfig
    let cId = categoryId || id || ''
    let _floatDisabled = !_shareConfig

    this.setData({
      TabCur: index,
      targetTabId: cId,
      shareConfig: _shareConfig,
      floatDisabled: _floatDisabled,
      banner,
      currList: [],
      nomore: false,
      currTotalPages: 1,
      currCoverUrl: _currCoverUrl,
      shareTitle: _sTitle
    }, () => {
      this.handleScrollToupper()
      if (!_shareConfig) {
        wx.hideShareMenu({
          menus: ['shareAppMessage', 'shareTimeline']
        })
      } else {
        // if (hasLogin){
        wx.showShareMenu({
          menus: ['shareAppMessage']
        })
        // }
      }

      if (type === 'POSTER') {
        wx.hideShareMenu({
          menus: ['shareAppMessage', 'shareTimeline']
        })
      }
      return this.getDetail(type, cId)
    })
  },

  onHandleAction(e = {}) {
    const { shareConfig, targetTabId } = this.data

    let banShare = 0
    if (!isEmptyObject(e)) {
      const {
        dataset: {
          item = {},
          type = ''
        }
      } = e.currentTarget || {}
      if (shareConfig === false) { banShare = 1 }
      if (type) {
        const passParams = {
          ...item,
          banShare,
          targetTabId,
          action: `action://share/${type}`
        }

        return app.globalData.emitter.emit(SEND_EVENT_TO_POLYMERS, { detail: passParams })
      }
      return app.globalData.emitter.emit(SEND_EVENT_TO_POLYMERS, { detail: { ...e.detail, banShare, }, targetTabId })
    }
  },

  onHandleFloat(e = {}) {
    const { shareConfig } = this.data
    const { disabled } = e.detail || {}

    if (!shareConfig) {
      return interaction.showToast('该频道不支持分享')
    }

    this.setData({
      floatDisabled: disabled,
      sheetDisabled: !disabled,
    })
  },

  onHandleCancel() {
    this.setData({
      floatDisabled: false,
      sheetDisabled: true,
    })
  },

  onHandleSharePoster() {
    const {
      fromTab,
      type,
      categoryIds,
      targetTabId,
      TabCur = 0,
      contentInfo,
      banner = '',
      currCoverUrl,
      shareTitle
    } = this.data
    const { userId = '', orgId = '', orgType = '' } = getUser()

    let params = {
      type,
      categoryIds,
      name: shareTitle,
      targetTabId,
      content: contentInfo[TabCur],
      avatar: banner,
      listPath: '/pages/common/list/index',
      fromTab,
      userId,
      orgId,
      orgType,
      listType: 'SINGLE',
      currCoverUrl: currCoverUrl || SHARE_IMG_DEFAULT
    }

    this.onHandleCancel()
    return wx.navigateTo({
      url: `/packages-common/pages/common/palette/index?${qs.stringify(params)}`
    })
  },

  onShareAppMessage() {
    const {
      fromTab,
      type,
      categoryIds,
      targetTabId,
      TabCur = 0,
      contentInfo,
      banner = '',
      currCoverUrl,
      shareTitle
    } = this.data
    const { userId = '', orgId = '', orgType = '' } = getUser()
    getApp().sensors.track('MPShare', {
      share_type: "微信好友",
    })
    let params = {
      type,
      categoryIds,
      userId,
      orgId,
      orgType,
      targetTabId,
      name: shareTitle,
      content: contentInfo[TabCur],
      avatar: banner,
      listType: 'SINGLE',
      currCoverUrl: currCoverUrl || SHARE_IMG_DEFAULT
    }
    params = qs.stringify(params)
    const pathInfo = {
      fromTab,
      listPath: '/pages/common/list/index',
      params
    }

    const shareData = breakIn({ name: "getFloatShareInfo", shareParams: { floatShare: pathInfo } })
    if (currCoverUrl) {
      shareData.imageUrl = currCoverUrl
    }
    shareData.title = shareTitle
    this.onHandleCancel()

    return {
      ...SHARE_BLOCK_DEFAULT,
      ...shareData
    }
  },

  doSearch() {
    const { contentInfo, type, TabCur, orgId } = this.data
    const { type: nType = '', id = '', categoryId = '' } = contentInfo[TabCur] || {}
    let isNeedOrgid = (nType === "MARKET_PLAN_EXCLUSIVE_CATEGORY")
    let cId = categoryId || id || ''

    wx.navigateTo({
      url: `/packages-common/pages/common/search/search?categoryId=${cId}&type=${type}&orgId=${orgId}&isNeedOrgid=${isNeedOrgid}`,
    })
  },

  onUnload() {
    const { fromTab, redirectBack } = this.data
    let backPath = getFirstPath()
    const tabList = (getApp().globalData.tabList)?.length ? getApp().globalData.tabList : getTabBarList() || []
    if (tabList && tabList.length) {
      let bTarget = tabList.find(item => item && item.tabName == fromTab)
      if (bTarget && !isEmptyObject(bTarget)) {
        backPath = bTarget?.tabPath
      }
    }
    if (!`${backPath}`.startsWith('/')) {
      backPath = `/${backPath}`
    }

    if (redirectBack) {
      return wx.switchTab({
        url: `${backPath}`
      })
    }
  },
})

import {
  getCurrRoleType,
  getFirst<PERSON><PERSON>,
  getTab<PERSON><PERSON><PERSON>ist,
  getUser,
  getUserLogin,
  getViolationsPhone
} from "../../../common/utils/userStorage";

import { getLiveList } from "../../../common/network/api";
import {
  systemtInfo,
  util,
  enums,
  storage,
  eventName,
  global,
  qs,
  interaction,
  vLog,
  breakIn
} from '../../../common/index';

import {
  getFindById,
  getContentLibrary,
  getNewsList,
  getPosterList,
  getMulitList,
  getRecommendList,
  getHomeMarketPlanList,
} from "../../../common/nb/home";

const { platform } = wx.getSystemInfoSync()
const dayJs = require('dayjs')

const {
  screenHeight,
  footHeight,
} = systemtInfo

const {
  SEND_EVENT_TO_POLYMERS,
  SET_SCROLL_NAV_BAR_SIZE
} = eventName

const {
  SHARE_BLOCK_DEFAULT,
  REVIEW_PHONE,
  RealmType,
  SHARE_IMG_DEFAULT
} = enums

const {
  rpx2px,
  isEmptyObject
} = util;

const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    modePlat: (platform === "ios"),
    contentHeight: rpx2px(screenHeight - footHeight),
    showViolations: false,

    list: [],
    TabCur: 0,  //  一级分类idx
    initNextIdx: 0, // 二级分类idx
    refreshing: false,
    isRefreshing: true,
    nomore: false,
    loadmoring: false,
    hasLogin: false,

    contentInfo: [],
    currList: [],
    page: 0,
    pageSize: 21,
    currTotalPages: 1,
    customerType: '',

    title: '广发基金木棉花',
    id: '',
    fromTab: '',

    targetTabId: '',
    nextTabId: '',
    currNextModels: [],
    redirectBack: '',

    floatDisabled: false,
    sheetDisabled: true,
    shareConfig: true,

    currCoverUrl: SHARE_IMG_DEFAULT,
    lView: 'single-line',
    lViewIos: 'single-line',
    shareTitle: '广发木棉花',
    contentLibrary: false
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    vLog.log('LATEST_VIEW options >>>', options)
    storage.setStorage(global.STORAGE_GLOBAL_PREVIOUS_PARAMS, options)
    const {
      name = '',
      fromTab = '',
      id = '',
      targetTabId = '',
      nextTabId = '',
      redirectBack = '',
      contentLibrary = false,
    } = options || {}

    const hasLogin = getUserLogin()
    const customerType = getCurrRoleType()

    this.setData({
      title: decodeURIComponent(name),
      fromTab: decodeURIComponent(fromTab),
      id: decodeURIComponent(id),
      showViolations: REVIEW_PHONE.includes(getViolationsPhone()),
      hasLogin,
      customerType,
      targetTabId,
      nextTabId,
      redirectBack,
      contentLibrary
    }, () => {
      if (targetTabId || nextTabId) {
        return this.getListInstall()
      }
      return this.getList()
    })

    // this.onSetHide()
  },

  doSearch() {
    const type = this.channelList[0]?.type
    return wx.navigateTo({
      url: `/packages-common/pages/common/search/search?type=${type}&channelList=${JSON.stringify(this.channelList)}&placeholder=${'搜索'}&fromTab=${'contentLibrary'}`,
    })
  },

  // async onSetHide() {
  //   const { hasLogin, customerType, contentLibrary } = this.data

  //   return setTimeout(() => ((!hasLogin || customerType === 'AGENCY' || contentLibrary))
  //     ? wx.hideShareMenu({
  //       menus: ['shareAppMessage', 'shareTimeline']
  //     }) : null
  //     , 1000)
  // },

  async doRefreshHomeInfo() {
    let timer = setTimeout(() => {
      clearTimeout(timer)
      this.setData({
        refreshing: false,
        isRefreshing: false,
      })
    }, 1000)
  },

  handleScrollToupper() {
    const that = this
    that.setData({
      scrollTop: 0
    })
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  handlePullDownRefresh: function () {
    const { type, currNextModels, initNextIdx = 0 } = this.data
    this.data.page = 0;

    this.setData({
      nomore: false,
      refreshing: true,
      currTotalPages: 1,
      currList: []
    }, () => {
      const { categoryId, id } = currNextModels[initNextIdx] || {}
      return this.getDetail(
        type || '',
        categoryId || id || '',
      )
    })
  },

  /**
   * 上拉加载
   */
  handleLoadMore: function () {
    const { type, currNextModels, initNextIdx = 0, loadmoring } = this.data

    if (loadmoring) {
      this.setData({ refreshing: false, isRefreshing: false })
      return
    }

    this.data.page++;
    this.setData({
      nomore: false,
      loadmoring: true
    }, () => {
      const { categoryId, id } = currNextModels[initNextIdx] || {}
      return this.getDetail(
        type || '',
        categoryId || id || '',
      )
    });
  },

  /**
   * 分享打开后的数据重组
   */
  async getListInstall() {
    vLog.log('LATEST_VIEW getListInstall ')
    const {
      id,
      targetTabId,
      nextTabId,
      currCoverUrl,
      modePlat,
      shareConfig,
      shareTitle = '',
      hasLogin,
      contentLibrary
    } = this.data
    let TabCur = 0
    let initNextIdx = 0
    let _cRealm = ''
    let _cModels = []
    let contentInfo = []

    const { msg, data, success } = contentLibrary ? await getContentLibrary() : await getFindById({ id })
    vLog.log('LATEST_VIEW getListInstall getFindById >>> ', msg, data, success)
    if (!success) {
      return interaction.showToast(msg || "")
    }

    let lView = contentLibrary ? 'single-line-qy' : 'single-line'
    let _agg_dtos = 0
    const { aggCateRelDTOS = [] } = data || {}
    if (aggCateRelDTOS && aggCateRelDTOS.length) {
      aggCateRelDTOS.forEach((item, index) => {
        const { categoryModels = [], realm = '', id: _targetTabId = '' } = item || {}
        for (let i = 0; i < categoryModels.length; i++) {
          const { status = '' } = categoryModels[i] || {}
          if (status !== 'PUBLISHED' && status != '1') {
            categoryModels.splice(i, 1)
          }
        }

        if (targetTabId == _targetTabId) {
          _cModels = categoryModels
          _cRealm = realm
          TabCur = index
        }
        const { tabName = '' } = item || {}
        contentInfo.push({ name: tabName })
        _agg_dtos = index
      })
    }

    let _nextTabId = ''
    let _sConfig = shareConfig
    let _shareTitle = shareTitle
    let _currCoverUrl = currCoverUrl
    if (_cModels && _cModels.length) {
      _cModels.forEach((cItem, cIndex) => {
        const {
          name: sTitle = '',
          categoryId: _nTabId = '',
          id: _id = '',
          shareConfig = true,
          shareCoverUrl: sCoverUrl = '',
        } = cItem || {}
        let cNextId = _nTabId || _id || ''

        if (nextTabId == cNextId) {
          _nextTabId = nextTabId
          _sConfig = shareConfig
          _shareTitle = sTitle
          initNextIdx = cIndex
          _currCoverUrl = sCoverUrl || SHARE_IMG_DEFAULT
        }
      })
    }

    if (_agg_dtos > 1) {
      lView = contentLibrary ? 'category-line-qy' : 'category-line'
    }
    if (contentInfo?.length > 1) {
      lView = contentLibrary ? 'category-line-qy' : 'category-line'
    }
    if (_cModels.length > 1) {
      lView = contentLibrary ? 'subnav-line-qy' : 'subnav-line'
      if (contentInfo && contentInfo.length <= 1) {
        lView = contentLibrary ? 'category-line-qy' : 'category-line'
      }
    }
    if (modePlat) {
      lView = contentLibrary ? 'single-line-qy' : 'single-line'
    }

    this.setData({
      list: data,
      contentInfo,
      lView,
      currNextModels: _cModels,
      currCoverUrl: _currCoverUrl,
      shareTitle: _shareTitle,
      shareConfig: _sConfig,
      type: _cRealm || '',
      TabCur,
      isRefreshing: aggCateRelDTOS?.length === 0 ? false : true,
      initNextIdx,
    }, () => {
      if (!_sConfig || contentLibrary) {
        wx.hideShareMenu({
          menus: ['shareAppMessage', 'shareTimeline']
        })
      } else {
        // if (hasLogin) {
        wx.showShareMenu({
          menus: ['shareAppMessage']
        })
        // }
      }

      if (_cRealm === 'POSTER' || contentLibrary) {
        wx.hideShareMenu({
          menus: ['shareAppMessage', 'shareTimeline']
        })
      }
    })

    if (_cModels && _cModels.length > 0) {
      const { categoryId, id } = _cModels[initNextIdx] || {}
      return this.getDetail(
        _cRealm || "",
        categoryId || id || '',
      )
    }
  },

  /**
   * 获取列表数据
   */
  async getList() {
    const {
      TabCur = 0,
      initNextIdx = 0,
      id = '',
      currCoverUrl,
      shareConfig = true,
      modePlat,
      shareTitle = '',
      hasLogin,
      contentLibrary
    } = this.data

    const { msg, data, success } = contentLibrary ? await getContentLibrary() : await getFindById({ id })
    vLog.log('LATEST_VIEW getFindById msg, data, success >>>', msg, data, success)
    if (!success) {
      return wx.showToast({
        title: msg || '',
        icon: "none"
      })
    }

    let _cRealm = ''
    let _cModels = []
    let contentInfo = []
    let _targetTabId = ''
    let lView = contentLibrary ? 'single-line-qy' : 'single-line'

    let _agg_dtos = 0
    const { aggCateRelDTOS = [] } = data || {}
    if (aggCateRelDTOS && aggCateRelDTOS.length) {
      this.channelList = []
      aggCateRelDTOS.forEach((item, index) => {
        this.channelList.push({ label: item.tabName, type: item.realm, categoryId: '', shortLink: '' })
        let {
          categoryModels = [],
          realm = '',
          id: targetTabId = '',
        } = item || {}

        for (let i = 0; i < categoryModels.length; i++) {
          const { status = '' } = categoryModels[i] || {}
          if (status != 'PUBLISHED' && status != '1') {
            categoryModels.splice(i, 1)
          }
        }

        if (TabCur === index) {
          _cModels = categoryModels
          _cRealm = realm
          _targetTabId = targetTabId
        }

        const { tabName = '' } = item || {}
        contentInfo.push({ name: tabName })
        _agg_dtos = index
      })
    }

    let _nextTabId = ''
    let _sConfig = shareConfig
    let _shareTitle = shareTitle
    let _currCoverUrl = currCoverUrl
    if (_cModels && _cModels.length) {
      _cModels.forEach((cItem, cIndex) => {
        const {
          name: sTitle = '',
          categoryId: nextTabId = '',
          shareConfig = true,
          shareCoverUrl: sCoverUrl = '',
        } = cItem || {}
        if (cIndex === initNextIdx) {
          _nextTabId = nextTabId
          _sConfig = shareConfig
          _shareTitle = sTitle
          _currCoverUrl = sCoverUrl || SHARE_IMG_DEFAULT
        }
      })
    }

    vLog.log('LATEST_VIEW _agg_dtos  >>>', _agg_dtos)
    vLog.log('LATEST_VIEW _cModels  >>>', _cModels)
    vLog.log('LATEST_VIEW contentInfo  >>>', contentInfo)

    if (_agg_dtos > 1) {
      lView = contentLibrary ? 'category-line-qy' : 'category-line'
    }
    if (contentInfo?.length > 1) {
      lView = contentLibrary ? 'category-line-qy' : 'category-line'
    }
    if (_cModels.length > 1) {
      lView = contentLibrary ? 'subnav-line-qy' : 'subnav-line'
      if (contentInfo && contentInfo.length === 1) {
        lView = contentLibrary ? 'category-line-qy' : 'category-line'
      }
    }
    if (!_cModels || !_cModels.length) {
      lView = contentLibrary ? 'single-line-qy' : 'single-line'
    }
    if (modePlat) {
      lView = contentLibrary ? 'single-line-qy' : 'single-line'
    }

    this.setData({
      list: data,
      contentInfo,
      lView,
      currNextModels: _cModels,
      currCoverUrl: _currCoverUrl,
      shareConfig: _sConfig,
      type: _cRealm || '',
      targetTabId: _targetTabId,
      nextTabId: _nextTabId,
      shareTitle: _shareTitle,
      isRefreshing: aggCateRelDTOS?.length === 0 ? false : true,
    }, () => {
      if (!_sConfig || contentLibrary) {
        wx.hideShareMenu({
          menus: ['shareAppMessage', 'shareTimeline']
        })
      } else {
        // if (hasLogin) {
        wx.showShareMenu({
          menus: ['shareAppMessage']
        })
        // }
      }

      if (_cRealm === 'POSTER' || contentLibrary) {
        wx.hideShareMenu({
          menus: ['shareAppMessage', 'shareTimeline']
        })
      }
    })

    if (_cModels && _cModels.length > 0) {
      const { categoryId, id } = _cModels[initNextIdx] || {}
      return this.getDetail(
        _cRealm || "",
        categoryId || id || '',
      )
    } else {
      return this.getDetail(_cRealm || "", '')
    }
  },

  getData(msg, dataContent = [], success, showTime, currTotalPages = 1, live = false) {
    vLog.log('LATEST_VIEW getData >>>>>', msg, dataContent, success, showTime)
    let { pageSize, page, currList = [] } = this.data || {}

    this.setData({
      loadmoring: false,
      currTotalPages
    });

    if (!success) {
      return wx.showToast({
        title: msg || '',
        icon: "none"
      })
    }

    if (dataContent && dataContent.length < pageSize) {
      this.setData({
        nomore: true
      });
    }
    if (showTime) {
      dataContent = dataContent.map(item => ({
        ...item,
        timeCreatedStr: dayJs(item?.timeCreated || new Date()).format('YYYY-MM-DD'),
      }))
    }
    if (page > 0) {
      currList = currList.concat(dataContent);
      this.setData({
        currList,
        refreshing: false,
        isRefreshing: false,
      });
    } else {
      this.setData({
        currList: dataContent,
        refreshing: false,
        isRefreshing: false,
      });
    }
  },

  /**
   * 获取聚合列表分类
   */
  async getDetail(type = '', id = '') {
    vLog.log('LATEST_VIEW getDetail >>>', type, id)
    const {
      page = 0,
      pageSize = 20,
      initNextIdx = 0,
      currNextModels,
      hasLogin,
      currTotalPages = 1,
    } = this.data

    const wInfo = getUser()
    if (page === 0) {
      this.setData({
        currList: [],
      });
    }

    let params = {
      page,
      pageSize
    }

    //资讯
    if (type === RealmType.ARTICLE) {
      params.localCategoryId = id
      const { msg, param, success } = await getNewsList(params)
      vLog.log('LATEST_VIEW getDetail getNewsList msg, param, success >>>', msg, param, success)
      this.getData(msg, param?.content, success, true)
    }

    //直播
    else if (type === RealmType.LIVE) {
      const { firstOrgId = '' } = wInfo || {}

      params = {
        start: page + 1,
        limit: pageSize,
        wbsCategoryId: id,
        channelIds: [`${firstOrgId}`]
      }
      // if (!params.start) {
      //   params.start = 1
      // }
      if (!firstOrgId) {
        delete params.channelIds
      }
      const { msg, rows, code } = await getLiveList(params)
      vLog.log('LATEST_VIEW getDetail getLiveList msg, rows, code >>>', msg, rows, code)
      this.getData(msg, rows, code === 0, false, 1, true)
    }

    //海报
    else if (type === RealmType.POSTER) {
      params.categoryId = id
      const { msg, param, success } = await getPosterList(params)
      vLog.log('LATEST_VIEW getDetail getPosterList msg, param, success >>>', msg, param, success)
      this.getData(msg, param?.content, success, true)
    }

    //音视频
    else if (type === RealmType.MULTI_COURSE) {
      params.categoryId = id
      const { msg, data, success } = await getMulitList(params)
      vLog.log('LATEST_VIEW getDetail getMulitList msg, data, success >>>', msg, data, success)
      this.getData(msg, data?.content, success, true)
    }

    //营销方案
    else if (type === RealmType.MARKET_PLAN) {
      params.categoryId = id

      let _cType = ''
      if (currNextModels && currNextModels.length) {
        const { type = '' } = currNextModels[initNextIdx] || {}
        _cType = type
      }

      if (_cType === "MARKET_PLAN_EXCLUSIVE_CATEGORY" && hasLogin) {
        const { firstOrgId = '' } = wInfo || {}
        params.orgId = firstOrgId
      }

      const api = hasLogin ? getRecommendList : getHomeMarketPlanList
      if (currTotalPages <= page) {
        this.setData({
          nomore: true,
          loadmoring: false
        })
        return
      }

      const { msg, param, success } = await api(params)
      vLog.log('LATEST_VIEW getDetail MARKET_PLAN msg, param, success >>>', msg, param, success)
      this.getData(msg, param?.content, success, '', param?.totalPages)
    }
  },

  // 切换一级 tab
  tabSelect(e = {}) {
    vLog.log('LATEST_VIEW e,this.data >>', e, this.data)
    this.data.initNextIdx = 0
    let {
      initNextIdx = 0,
      list: {
        aggCateRelDTOS = []
      },
      lView,
      currCoverUrl,
      shareTitle,
      shareConfig,
      modePlat,
      hasLogin,
      contentInfo,
      contentLibrary
    } = this.data

    const { index = 0 } = e.detail

    let _lView = lView
    let _cRealm = ''
    let _cModels = []
    let _targetTabId = ''
    let _nextTabId = ''
    let _currCoverUrl = currCoverUrl
    let _shareTitle = shareTitle
    let _sConfig = shareConfig

    let _agg_dtos = 0
    if (aggCateRelDTOS && aggCateRelDTOS.length) {
      aggCateRelDTOS.forEach((item, aIndex) => {
        const { categoryModels = [], realm = '', id: targetTabId = '', } = item || {}
        if (aIndex === index) {
          _cModels = categoryModels
          _cRealm = realm
          _targetTabId = targetTabId
        }

        _agg_dtos = aIndex
      })
    }

    vLog.log('LATEST_VIEW tabSelect _cModels >>>', _cModels)
    vLog.log('LATEST_VIEW tabSelect initNextIdx >>>', initNextIdx)
    if (_cModels && _cModels.length) {
      const { categoryId, id, name = '', shareCoverUrl = '', shareConfig = true } = _cModels[initNextIdx] || {}
      _nextTabId = categoryId || id || ''
      _shareTitle = name
      _currCoverUrl = shareCoverUrl || SHARE_IMG_DEFAULT
      _sConfig = shareConfig
    }

    if (_agg_dtos > 1) {
      _lView = contentLibrary ? 'category-line-qy' : 'category-line'
    }
    if (contentInfo?.length > 1) {
      _lView = contentLibrary ? 'category-line-qy' : 'category-line'
    }
    if (_cModels.length > 1) {
      _lView = contentLibrary ? 'subnav-line-qy' : 'subnav-line'
      if (contentInfo && contentInfo.length === 1) {
        _lView = contentLibrary ? 'category-line-qy' : 'category-line'
      }
    }

    if (!_cModels || !_cModels.length) {
      _lView = contentLibrary ? 'single-line-qy' : 'single-line'
    }

    if (modePlat) {
      _lView = contentLibrary ? 'single-line-qy' : 'single-line'
    }

    let showMargin = aggCateRelDTOS[index]?.categoryModels?.length > 1
    vLog.log('LATEST_VIEW tabSelect showMargin,_lView >>>', showMargin, _lView)

    this.data.page = 0;
    this.setData({
      TabCur: index,
      lView: _lView,
      initNextIdx: 0,
      currList: [],
      nomore: false,
      currTotalPages: 1,
      currNextModels: _cModels,
      currCoverUrl: _currCoverUrl,
      shareTitle: _shareTitle,
      type: _cRealm || '',
      targetTabId: _targetTabId,
      nextTabId: _nextTabId,
      shareConfig: _sConfig,
      refreshing: false,
      isRefreshing: true,
    }, () => {
      this.handleScrollToupper()
      if (!_sConfig || contentLibrary) {
        wx.hideShareMenu({
          menus: ['shareAppMessage', 'shareTimeline']
        })
      } else {
        // if (hasLogin) {
        wx.showShareMenu({
          menus: ['shareAppMessage']
        })
        // }
      }

      if (_cRealm === 'POSTER' || contentLibrary) {
        wx.hideShareMenu({
          menus: ['shareAppMessage', 'shareTimeline']
        })
      }

      return this.getDetail(
        _cRealm || '',
        _nextTabId,
      )
    })
  },

  // 切换二级 tab
  clickCategory(e = {}) {
    const {
      type,
      currNextModels,
      currCoverUrl,
      shareTitle,
      shareConfig,
      hasLogin,
      contentLibrary
    } = this.data
    const { index = 0 } = e.detail || {}
    this.data.page = 0;
    let _nextTabId = ''
    let _currCoverUrl = currCoverUrl
    let _shareTitle = shareTitle
    let _sConfig = shareConfig

    if (currNextModels && currNextModels.length) {
      vLog.log('LATEST_VIEW clickCategory currNextModels[index] >>>', currNextModels[index])
      const { categoryId, id, shareCoverUrl = '', name = '', shareConfig = true } = currNextModels[index] || {}
      _nextTabId = categoryId || id || ''
      _currCoverUrl = shareCoverUrl || SHARE_IMG_DEFAULT
      _shareTitle = name
      _sConfig = shareConfig
    }

    this.setData({
      initNextIdx: index,
      nextTabId: _nextTabId,
      currCoverUrl: _currCoverUrl,
      shareTitle: _shareTitle,
      currList: [],
      nomore: false,
      currTotalPages: 1,
      refreshing: false,
      isRefreshing: true,
      shareConfig: _sConfig
    }, () => {
      this.handleScrollToupper()
      if (!_sConfig || contentLibrary) {
        wx.hideShareMenu({
          menus: ['shareAppMessage', 'shareTimeline']
        })
      } else {
        // if (hasLogin) {
        wx.showShareMenu({
          menus: ['shareAppMessage']
        })
        // }
      }

      if (type === 'POSTER' || contentLibrary) {
        wx.hideShareMenu({
          menus: ['shareAppMessage', 'shareTimeline']
        })
      }

      return this.getDetail(
        type,
        _nextTabId,
      )
    })
  },

  onHandleAction(e = {}) {
    vLog.log('LATEST_VIEW onHandleAction e >>>', e)
    const { shareConfig } = this.data
    let banShare = 0
    if (!isEmptyObject(e)) {
      const {
        dataset: {
          item = {},
          type = ''
        }
      } = e.currentTarget || {}
      if (shareConfig === false) { banShare = 1 }
      if (type) {
        const passParams = {
          ...item,
          banShare,
          action: `action://share/${type}`
        }
        return app.globalData.emitter.emit(SEND_EVENT_TO_POLYMERS, { detail: passParams })
      }
      return app.globalData.emitter.emit(SEND_EVENT_TO_POLYMERS, { detail: { ...e.detail, banShare, } })
    }
  },

  onHandleFloat(e = {}) {
    vLog.log('LATEST_VIEW onHandleFloat e,this.data >>', e, this.data)
    const { shareConfig } = this.data
    const { disabled } = e.detail || {}

    if (!shareConfig) {
      return interaction.showToast('该频道不支持分享')
    }

    this.setData({
      floatDisabled: disabled,
      sheetDisabled: !disabled,
    })
  },

  onHandleCancel(e = {}) {
    vLog.log('LATEST_VIEW onHandleCancel e >>', e)
    this.setData({
      floatDisabled: false,
      sheetDisabled: true,
    })
  },

  onHandleSharePoster(e = {}) {
    vLog.log('LATEST_VIEW onHandleSharePoster e,this.data >>>>', e, this.data)
    const {
      fromTab,
      id,
      targetTabId,
      nextTabId,
      type,
      initNextIdx = 0,
      currNextModels = [],
      currCoverUrl,
      shareTitle
    } = this.data
    const { userId = '', orgId = '', orgType = '', } = getUser()

    let params = {
      name: shareTitle,
      id,
      targetTabId,
      nextTabId,
      type,
      content: { aggregationPage: true, ...currNextModels[initNextIdx] },
      fromTab,
      userId,
      orgId,
      orgType,
      listPath: '/pages/common/list/index',
      currCoverUrl: currCoverUrl || SHARE_IMG_DEFAULT,
    }
    vLog.log('LATEST_VIEW onHandleSharePoster params >>>', params)

    this.onHandleCancel({})
    return wx.navigateTo({
      url: `/packages-common/pages/common/palette/index?${qs.stringify(params)}`
    })
  },

  onShareAppMessage() {
    vLog.log('LATEST_VIEW this.data >>>', this.data)
    const {
      fromTab,
      id,
      targetTabId,
      nextTabId,
      type,
      initNextIdx = 0,
      currNextModels = [],
      currCoverUrl = '',
      shareTitle = ''
    } = this.data
    const { userId = '', orgId = '', orgType = '', } = getUser()
    getApp().sensors.track('MPShare', {
      share_type: "微信好友",
    })
    let params = {
      name: shareTitle,
      id,
      targetTabId,
      nextTabId,
      type,
      userId,
      orgId,
      orgType,
      listType: 'UNION',
      content: { aggregationPage: true, ...currNextModels[initNextIdx] },
      currCoverUrl: currCoverUrl || SHARE_IMG_DEFAULT
    }
    params = qs.stringify(params)
    const pathInfo = {
      fromTab,
      listPath: '/pages/common/list/index',
      params
    }

    const shareData = breakIn({ name: "getFloatShareInfo", shareParams: { floatShare: pathInfo } })
    vLog.log('LATEST_VIEW shareData>>>', shareData)
    if (currCoverUrl) {
      shareData.imageUrl = currCoverUrl
    }
    shareData.title = shareTitle || '广发木棉花'
    this.onHandleCancel()

    return {
      ...SHARE_BLOCK_DEFAULT,
      ...shareData
    }
  },

  onUnload() {
    app.globalData.emitter.emit(SET_SCROLL_NAV_BAR_SIZE, 0)
    const { fromTab, redirectBack } = this.data || {}
    let backPath = getFirstPath()
    const tabList = (getApp().globalData.tabList)?.length ? getApp().globalData.tabList : getTabBarList() || []
    if (tabList && tabList.length) {
      let bTarget = tabList.find(item => item && item.tabName == fromTab)
      if (bTarget && !isEmptyObject(bTarget)) {
        backPath = bTarget?.tabPath
      }
    }

    if (redirectBack) {
      return wx.switchTab({
        url: `${backPath}`
      })
    }
  },
})

<wxs src="../../../wxs/common.wxs" module="tools"/>
<nb-page
    showNavBar="{{true}}"
    navShowBack="{{false}}"
    showHomeIcon="{{true}}"
    navTitle="{{name||'列表'}}"
    showTabBar="{{false}}">
  <view class="content-nav" wx:if="{{contentInfo.length>1}}">
    <scrollable-tabview
        class=".current-item {{index==TabCur?'text-black':''}}"
        tabs='{{contentInfo}}'
        bind:tabNavClick='tabTopSelect'
        backgroundColor="#fff"
        activeColor="#333"
        color="#999"
        isFromList="true"
        barBgColor="{{$state.themeColor}}"
        currentIndex="{{TabCur}}"/>
  </view>
  <view class="subNav"
        style="margin-top:{{contentInfo.length >1?'50px':0}}"
        wx:if="{{list.aggCateRelDTOS[TabCur].categoryModels.length >1}}">
    <category
        tabs="{{list.aggCateRelDTOS[TabCur].categoryModels}}"
        bind:tabBarClick='clickCategory'
        color="{{$state.themeColor}}"
        currentIndex="{{initNextIdx}}"
        showBorder="{{true}}"
    />
  </view>

  <x-scroll-view
      enableRefresh="{{true}}"
      class="list-container"
      style="margin-bottom: 25vh;"
      elementHeight="{{contentHeight}}"
      refreshing="{{refreshing}}"
      loadmoring="{{loadmoring}}"
      nomore="{{nomore}}"
      enableLoadMore="{{true}}"
      resetting="{{true}}"
      scrollTop='{{scrollTop}}'
      refreshTopDistance="{{refreshTopDistance}}"
      hasTabBarInfo="{{contentInfo.length>1 || list.aggCateRelDTOS[TabCur].categoryModels.length > 1}}"
      bindpulluploadmore="handleLoadMore"
      bindpulldownrefresh="handlePullDownRefresh">

    <view
        wx:if="{{type !== 'POSTER' && listType==='SINGLE'}}"
        class="search-block {{lView}}"
        bind:tap="doSearch">
      <image src="../../../imgs/empty/<EMAIL>" class="search-icon" mode="aspectFill"/>
      <view class="search-content">
        <text class="search-content-tips">{{placeholder || '请输入关键词'}}</text>
      </view>
    </view>
    <image
        wx:if="{{banner}}"
        class="banner-img"
        src="{{banner}}"
        mode="widthFix"
    />

    <view
        wx:if="{{!currList || !currList.length}}"
        class="empty-class">
      <emptyBlock wx:if="{{!refreshing}}" tips="暂无数据" showBGColor="false"/>
      <vertical-space/>
    </view>

    <view wx:if="{{listType==='UNION'}}" class="{{lView}}">
      <view
          class="content-list"
          wx:if="{{currList.length>0 && list.aggCateRelDTOS[TabCur].realm === 'ARTICLE'}}">
        <view
            wx:for="{{currList}}"
            wx:for-item="item"
            wx:for-index="id"
            wx:key="id">
          <news
              data="{{item}}"
              bind:onItemClick="onHandleAction"
              isLast="{{id === currList.length-1}}"
              elementType="LIST"
              nomore="{{nomore}}"
          />
        </view>
      </view>
      <view
          class="content-media-list"
          wx:if="{{currList.length>0 && list.aggCateRelDTOS[TabCur].realm === 'LIVE'}}"
          wx:for="{{currList}}"
          wx:for-item="item"
          wx:for-index="id"
          wx:key="id">
        <live
            item="{{item}}"
            data-type="{{'AppAdvLive'}}"
            data-item="{{item}}"
            bind:tap="onHandleAction"
            isLast="{{id === currList.length-1}}"
            elementType="LIST"
            nomore="{{nomore}}"
        />
      </view>
      <view
          class="content-list"
          wx:if="{{currList.length>0 && list.aggCateRelDTOS[TabCur].realm === 'MULTI_COURSE'}}">
        <view
            wx:for="{{currList}}"
            wx:for-item="item"
            wx:for-index="id"
            wx:key="id">
          <multimedia
              data="{{item}}"
              bind:onItemClick="onHandleAction"
              isLast="{{id === currList.length-1}}"
              elementType="LIST"
              nomore="{{nomore}}"
          />
        </view>
      </view>
      <view
          class="content-poster-list"
          style="margin-top:14px"
          wx:if="{{currList.length>0 && list.aggCateRelDTOS[TabCur].realm === 'POSTER'}}">
        <view
            wx:for="{{currList}}"
            wx:for-item="item"
            wx:for-index="id"
            wx:key="id">
          <poster
              item="{{item}}"
              data-type="{{'AppAdvPosterInfo'}}"
              data-item="{{item}}"
              bind:tap="onHandleAction"
              isLast="{{id === currList.length-1}}"
              elementType="LIST"
              nomore="{{nomore}}"
          />
        </view>
      </view>
      <view
          class="content-list"
          wx:if="{{currList.length>0 && list.aggCateRelDTOS[TabCur].realm === 'MARKET_PLAN'}}">
        <view
            wx:for="{{currList}}"
            wx:for-item="item"
            wx:for-index="id"
            wx:key="id">
          <marketPlan
              data="{{item}}"
              bind:onItemClick="onHandleAction"
              isLast="{{id === currList.length-1}}"
              elementType="LIST"
              nomore="{{nomore}}"
          />
        </view>
      </view>
    </view>

    <view wx:if="{{listType==='SINGLE'}}" class="list-view">
      <view
          class="content-list"
          wx:if="{{currList.length>0 && type === 'ARTICLE'}}">
        <view
            wx:for="{{currList}}"
            wx:for-item="item"
            wx:for-index="id"
            wx:key="id">
          <news
              data="{{item}}"
              bind:onItemClick="onHandleAction"
              isLast="{{id === currList.length-1}}"
              elementType="LIST"
              nomore="{{nomore}}"
          />
        </view>
      </view>
      <view
          class="content-media-list"
          wx:if="{{currList.length>0 && type === 'LIVE'}}"
          wx:for="{{currList}}"
          wx:for-item="item"
          wx:for-index="id"
          wx:key="id">
        <live
            item="{{item}}"
            data-type="{{'AppAdvLive'}}"
            data-item="{{item}}"
            bind:tap="onHandleAction"
            isLast="{{id === currList.length-1}}"
            elementType="LIST"
            nomore="{{nomore}}"
        />
      </view>
      <view
          class="content-list"
          wx:if="{{currList.length>0 && type === 'MULTI_COURSE'}}">
        <view
            wx:for="{{currList}}"
            wx:for-item="item"
            wx:for-index="id"
            wx:key="id">
          <multimedia
              data="{{item}}"
              bind:onItemClick="onHandleAction"
              isLast="{{id === currList.length-1}}"
              elementType="LIST"
              nomore="{{nomore}}"
          />
        </view>
      </view>
      <view
          class="content-poster-list"
          style="margin-top:14px"
          wx:if="{{currList.length>0 && type === 'POSTER'}}">
        <view
            wx:for="{{currList}}"
            wx:for-item="item"
            wx:for-index="id"
            wx:key="id">
          <poster
              item="{{item}}"
              data-type="{{'AppAdvPosterInfo'}}"
              data-item="{{item}}"
              bind:tap="onHandleAction"
              isLast="{{id === currList.length-1}}"
              elementType="LIST"
              nomore="{{nomore}}"
          />
        </view>
      </view>
      <view
          class="content-list"
          wx:if="{{currList.length>0 && type === 'MARKET_PLAN'}}">
        <view
            wx:for="{{currList}}"
            wx:for-item="item"
            wx:for-index="id"
            wx:key="id">
          <marketPlan
              data="{{item}}"
              bind:onItemClick="onHandleAction"
              isLast="{{id === currList.length-1}}"
              elementType="LIST"
              nomore="{{nomore}}"
          />
        </view>
      </view>
    </view>

    <view style="width:100%;height:28px"></view>
  </x-scroll-view>

  <polymers></polymers>
  <van-toast id="van-toast"/>
</nb-page>


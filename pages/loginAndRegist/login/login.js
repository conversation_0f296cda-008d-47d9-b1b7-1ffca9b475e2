import {GLOBAL_START_PATH} from "../../../common/const/enum";
import {BREAK_IN_INIT_SUCCESS} from "../../../common/event/name";

Page({
  onLoad: function(options) {
    console.log('START_LOGIN options >>>>', options)

    return wx.reLaunch({
      url: `/${GLOBAL_START_PATH}`,
      complete: () => {
        getApp().globalData.refersMark = true
        getApp().event.emit(BREAK_IN_INIT_SUCCESS)
      }
    })
  }
});

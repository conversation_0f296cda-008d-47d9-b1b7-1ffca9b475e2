<view class="start-page">
  <block wx:if="{{!$state.hasChannelCover && !hasChannelCover}}">
    <image src="./images/pic_start_up.jpeg" mode="aspectFill" class="channelImg" />
  </block>
  <block wx:else>
    <image src="{{$state.channelCoverUrl || channelCover}}" mode="aspectFill" class="channelImg" />
  </block>

  <view class="start-block">
    <view class='dot-wrapper'>
      <view class='dot {{loadingIdx == 0 ? "highlight" : ""}}'></view>
      <view class='dot {{loadingIdx == 1 ? "highlight" : ""}}'></view>
      <view class='dot {{loadingIdx == 2 ? "highlight" : ""}}'></view>
    </view>
  </view>
  <!-- 保证没有卡片加载的时候相关的页面跳转逻辑也能用 -->
  <polymers />
</view>
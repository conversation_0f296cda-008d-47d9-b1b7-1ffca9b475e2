.start-page{
    display: flex;
    flex-direction: column;
    flex: 1;
    width: 100vw;
    height: 100vh;
    justify-content: space-between;
}

.background{
    width: 100vw;
}

.channelImg{
    width: 100%;
    height: 100%;
    z-index: 99;
}

.dot-wrapper {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 80rpx;
    height: 14rpx;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-top: 20Px;
    padding-bottom: 300rpx;
    margin-bottom: 100rpx;
    margin-left: calc((100% - 80rpx)/2)
}

.dot {
    width: 14rpx;
    height: 14rpx;
    border-radius: 7rpx;
    background: rgba(255, 255, 255, 0.5)
}

.highlight {
    background: rgba(255, 255, 255, 1);
}

.start-logo{
    width: 70vw;
    margin-top: 220Px;
}

.start-tips{
    font-size: 20Px;
    font-weight: 500;
    color: #fff;
    margin-top: 70Px;
    font-family: PingFangSC-Regular;
}

.start-block{
    display: flex;
    flex-direction: column;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 99;
    width: 100vw;
    height: 100vh;
    align-items: center;
    /*justify-content: center;*/
}

import {
  global,
  interaction,
  storage,
  util,
  enums,
  wbs,
  qs,
  breakIn,
  vLog,
  convert,
  shapeShift,
  eventName,
} from "../../../common/index.js";

import {
  checkLogin,
  getSceneParam,
  qyCode2session,
  getAdvStaffCardInfo
} from "../../../common/nb/home";

import {
  getUnionID,
  getOpenId,
  getToken,
  getWechatInfoId,
  setAppHoldStatus,
  getSFV,
  setSFV,
  setStartUpRoutePageType,
  getStartUpRoutePageType,
  getUserRole,
  getFirstPath,
  setRouterInRegister,
  getCustomerTypeInt,
  setCustomerTypeInt,
  getCurrRoleType,
  getUserLogin,
  getStartUpHasCover,
  getStartUpCover,
  getOUInfo,
  getLoadRawStatus,
  getUserId,
  getUser
} from "../../../common/utils/userStorage";

const {
  BREAK_IN_INIT_SUCCESS
} = eventName
const ERROR_STATUS = ['null', 'undefined', '[object Object]']
const PASS_CODE = [0, 200]

const {
  LoginState,
  EnterSource,
  FILTER_KEYS_SHARE_LIST,
  FROM_CHANNEL,
  ROLE_TYPE,
  REGISTER_TYPE,
  APP_START_WAY_IDS
} = enums || {}

const {
  isEmptyObject,
  doUrlDeCode,
  formatUrlObject,
  panTransformUrl2Params,
  panDClone,
  repairLaunchPath
} = util

const FILTER_TEMP_KEY = ['fromChannel', 'formChannel', 'targetPath', 'targetRole', 'perfix']
const FILTER_QR_KEY = ['routerPage']
const FILTER_TARGET_KEY = ['targetPath']
const staffTypeStr = { CHANNEL: '渠道客户', AGENCY: '机构客户', CUSTOMER: '普通客户' }

Page({
  data: {
    loadingIdx: 1,
    hasChannelCover: false,
    channelCoverUrl: '',
  },
  _data: {
    loadingTimer: '',
    type: '',
    inviterStaffId: '',
    inviterName: '',
    inviterRole: 'CHANNEL',
    inviterStatus: '',

    fromChannel: '',
    options: {},
    hasChannel: false,
    isHangUp: false
  },

  onLoad(options) {
    this.canSendChatMessage()
    vLog.info('START_UP ORIGIN qs.parse(options) >>>>', qs.parse(options)).report()
    setAppHoldStatus(false)
    if (options?.contentLibrary === 'true') {
      breakIn({ name: 'initUnionId' })
    }
    if (options?.type === "INVITE") {
      getApp().sensors.track('qrcode_invite', {
        type: '分享邀请注册',
        invitor: options.inviterStaffId
      })
      breakIn({ name: 'initUnionId' })
    }
    getApp().event.on(BREAK_IN_INIT_SUCCESS, () => {
      vLog.info('START_UP BREAK_IN 监听').report()
      return this.startInit()
    })

    let that = this
    options = qs.parse(options)
    for (let [key, value] of Object.entries(options)) {
      const keyNew = this.decodeUrl(key)
      const valueNew = this.decodeUrl(value + '')
      options[keyNew] = valueNew
    }
    let hasChannelCover = getStartUpHasCover()
    let channelCover = getStartUpCover()

    vLog.log('START_UP', channelCover)
    this.setData({
      hasChannelCover,
      channelCover
    })

    that._data = {
      ...that._data,
      options: Object.assign({}, options)
    }
  },

  onShow() {
    const { isHangUp } = this._data
    if (!isHangUp) {
      this.onHangUp(0)
    }
    let timeElapsed = 0
    this._data.loadingTimer = setInterval(() => {
      const { loadingIdx } = this.data || {}
      this.setData({
        loadingIdx: (loadingIdx + 1) % 3
      })

      timeElapsed += 550
    }, 500)
  },

  onReady() {
    vLog.log('START_UP onReady data >>', this.data, this._data)
    breakIn({ name: 'getCheckAppUpdate' })
    const { isHangUp } = this._data
    if (!isHangUp) {
      return this.onHangUp(2500)
    }
  },

  onHangUp(ms = 0) {
    const { options } = this._data || {}
    let that = this

    let timer = setTimeout(() => {
      clearTimeout(timer)
      const {
        shareFrom = '',
        fromChannel = '',
        calculatorType = '',
        sceneData = '',
        scene = '',
        isFromOut = ''
      } = options || {}

      that._data = {
        ...that._data,
        isHangUp: true
      }
      let pagePath = getCurrentPages().pop();
      vLog.info(`START_UP onHangUp pagePath:${pagePath?.route}`).report()
      if (pagePath.route !== 'pages/loginAndRegist/startUp/index') {
        return
      }

      if (shareFrom && typeof shareFrom == 'string'
        || fromChannel
        || calculatorType
        || sceneData
        || scene
        || isFromOut) {
        vLog.log('START_UP onHangUp options >>>>', options)
        return that.startInit()
      }
      vLog.info('START_UP onHangUp !options || isEmptyObject(options) >>>>>>>', !options || isEmptyObject(options)).report()
      if (!options || isEmptyObject(options)) {
        return that.startInit()
      }
    }, ms)
  },

  /**
   * 启动页初始化
   */
  startInit() {
    const { options } = this._data || {}
    const hasLogin = getUserLogin()
    vLog.log('START_UP START_INIT qs.parse(options) >>>>', qs.parse(options))
    let {
      type = '',
      inviterStaffId = '',
      inviterRole = '',
      inviterName = '',
      scene = "",
      shareFrom = '',
      fromChannel = '',
      calculatorType = '',
      isFromOut = '',
      isFormOut = '',
      path = '',
      targetPath = '',
      targetRole = '',
      perfix = '',
      routerPage = '',
      pageType = '',
      url = '',
      params = '',
      fromTab = '',
      routerStatus = '',
      customerType = '',
      shouldLogin = ''
    } = qs.parse(options) || {}

    // const rawStatus = getLoadRawStatus()
    // vLog.warn('====== START_UP rawStatus >>', rawStatus)
    // if (rawStatus) {
    //   return
    // }

    // 木槿智投登录跳转
    let _fOut = isFromOut || isFormOut || ''
    if (_fOut && _fOut == 'true' && !getApp().globalData.refersMark) {
      let rpt = {}
      rpt = {
        path: `${path}`,
        type: LoginState.JUMP_OTHERS,
        way: "navigateToMiniProgram",
      }
      vLog.log('START_UP isFromOut rpt >>>', rpt)
      setStartUpRoutePageType(rpt)
      return this.initConfig()
    }

    // 消息推送
    vLog.info('START_UP 消息推送 fromChannel,refersMark >>>>', fromChannel, getApp().globalData.refersMark).report()
    if (fromChannel && !getApp().globalData.refersMark) {
      let rpt = {}
      switch (fromChannel) {
        // 旧版-内容 => 兼容
        case FROM_CHANNEL.OLD_TEMP: {
          targetPath = '/pages/common/webview/webPage'
        }
        // 内容
        case FROM_CHANNEL.CONTENT: {
          let opts = {}
          for (let [oKey, oValue] of Object.entries(panDClone(options))) {
            if (!FILTER_TEMP_KEY.includes(oKey)) {
              opts[oKey] = doUrlDeCode(oValue)
            }
          }

          let _checkUrl = `${perfix}`.split('//')
          vLog.log('START_UP _checkUrl >>>>', _checkUrl)
          if (_checkUrl && _checkUrl.length >= 3) {
            perfix = `${_checkUrl[0]}//${_checkUrl[1]}/${pageType}`
          }
          const reRouteInfo = {
            pageType,
            pageFlag: 'TEMP_MSG',
            url: `${perfix}`,
            customerType: targetRole,
            banShare: 1,
            ...opts
          }

          rpt = {
            path: `${targetPath}?${qs.stringify(reRouteInfo)}`,
            type: LoginState.JUMP_TARGET,
            way: "reLaunch",
            targetRole,
          }
          break
        }

        // 单列表
        case FROM_CHANNEL.SINGLE: {
          let opts = {}
          for (let [oKey, oValue] of Object.entries(panDClone(options))) {
            if (!FILTER_TEMP_KEY.includes(oKey)) {
              opts[oKey] = doUrlDeCode(oValue)
            }
          }

          const reRouteInfo = {
            listType: 'SINGLE',
            customerType: targetRole,
            banShare: 1,
            ...opts
          }
          rpt = {
            path: `${targetPath}?${qs.stringify(reRouteInfo)}`,
            type: LoginState.JUMP_TARGET,
            way: "reLaunch",
            targetRole,
          }
          break
        }

        // 聚合列表
        case FROM_CHANNEL.UNION: {
          let opts = {}
          for (let [oKey, oValue] of Object.entries(panDClone(options))) {
            if (!FILTER_TEMP_KEY.includes(oKey)) {
              opts[oKey] = doUrlDeCode(oValue)
            }
          }

          const reRouteInfo = {
            listType: 'UNION',
            customerType: targetRole,
            banShare: 1,
            ...opts
          }
          rpt = {
            path: `${targetPath}?${qs.stringify(reRouteInfo)}`,
            type: LoginState.JUMP_TARGET,
            way: "reLaunch",
            targetRole,
          }
          break
        }

        default:
          break
      }

      vLog.log('START_UP 消息推送 fromChannel rpt >>>', rpt)
      // this.setData({fromChannel})
      this._data.fromChannel = fromChannel
      setStartUpRoutePageType(rpt)
      return this.initConfig()
    }

    // 定投相关
    vLog.info('START_UP 定投相关 calculatorType >>>>', calculatorType).report()
    if (calculatorType && calculatorType === "Calculator" && !getApp().globalData.refersMark) {
      vLog.log('START_UP 定投相关 url.includes(hasLogin=2) >>>', url.includes('hasLogin=2'))
      // 游客跳转登录
      if (!hasLogin && url.includes('hasLogin=2')) {
        const { url, routerPage } = options || {}

        let cInstall = {
          perfix: url,
          hasLogin: 2,
          targetPath: routerPage || '/pages/common/webview/webPage',
          token: getToken(),
          openid: getOpenId(),
          unionid: getUnionID(),
          wechatInfoId: getWechatInfoId()
        }
        vLog.info('START_UP calculatorType >>>', JSON.stringify(cInstall)).report()

        let rpt = {
          path: `/pages/user/login/index`,
          params: cInstall,
          type: LoginState.JUMP_TARGET,
          way: 'reLaunch'
        }
        setStartUpRoutePageType(rpt)
        return this.initConfig()
      }
    }

    vLog.info('START_UP 分享解析 shareFrom >>>', JSON.stringify(shareFrom), typeof shareFrom)
    // shareFrom 解析
    if (shareFrom && typeof shareFrom == 'string' && !getApp().globalData.refersMark) {
      vLog.log('START_UP 分享解析 options >>>', options)
      let rpt = {}
      switch (shareFrom) {
        // 详情页面
        case EnterSource.WEBVIEW: {
          const reRouteParams = {
            pageType,
            pageFlag: 'WEBVIEW',
            url,
            customerType
          }
          rpt = {
            path: `${routerPage}?${qs.stringify(reRouteParams)}`,
            type: LoginState.JUMP_TARGET,
            way: "reLaunch"
          }
          break
        }

        // 定投罗盘
        case EnterSource.COMPASS: {
          rpt = {
            path: `${routerPage}`,
            type: LoginState.JUMP_TARGET,
            way: "reLaunch"
          }
          break
        }

        // tab首页
        case EnterSource.HOME: {
          rpt = {
            path: routerPage || getFirstPath(),
            type: LoginState.START_UP,
            customerType: getCurrRoleType()
          }
          // 非普客站点访问 需要登录态
          if (shouldLogin && shouldLogin === 'true' && getCurrRoleType() !== customerType) {
            rpt['targetRole'] = customerType
            rpt['shouldLogin'] = true
            rpt['way'] = 'reSetSite'
          } else {
            rpt['shouldLogin'] = ''
            rpt['way'] = 'reLaunch'
          }
          break
        }

        // 列表
        case EnterSource.LIST: {
          rpt = {
            path: `${routerPage}?${params}&fromTab=${fromTab}`,
            type: LoginState.FLOAT_SHARE,
            way: 'reLaunch'
          }
          break
        }

        // 活动
        case EnterSource.ACTIVITY: {
          rpt = {
            path: `${routerPage}?fromTab=${fromTab}&routerStatus=${routerStatus}&${qs.stringify(qs.parse(options))}`,
            type: LoginState.JUMP_ACTIVITY,
            way: 'reLaunch'
          }
          break
        }

        // 三重好礼
        case EnterSource.WELCOME: {
          rpt = {
            path: `${routerPage}?fromTab=${fromTab}&routerStatus=${routerStatus}&${qs.stringify(qs.parse(options))}`,
            type: LoginState.WELCOME,
            way: 'reLaunch'
          }
          break
        }

        // 抽奖
        case EnterSource.H5: {
          rpt = {
            path: `${routerPage}?${params}&${qs.stringify(qs.parse(options))}`,
            type: LoginState.H5,
            way: 'reLaunch'
          }
          break
        }

        // 专题
        case EnterSource.TOPIC: {
          let tParams = {}
          for (let [tKey, tValue] of Object.entries(options)) {
            if (!FILTER_QR_KEY.includes(tKey)) {
              tParams[tKey] = tValue
            }
          }
          tParams = formatUrlObject(tParams)
          const { shouldLogin } = tParams || {}
          vLog.log('START_UP tParams >>', tParams)
          if (shouldLogin && !hasLogin) {
            return this.verifyToLogin(tParams)
          }

          rpt = {
            path: `${routerPage}?${qs.stringify(tParams)}`,
            type: LoginState.TOPIC,
            way: 'reLaunch'
          }
          break
        }
      }

      vLog.info('START_UP shareFrom rpt >>>', JSON.stringify(rpt)).report()
      setStartUpRoutePageType(rpt)
      return this.initConfig()
    }

    let sceneData = decodeURIComponent(scene)
    if (ERROR_STATUS.includes(sceneData)) {
      sceneData = ''
    }

    // 邀请相关
    vLog.info('START_UP 邀请相关 type,inviterStaffId,sceneData >>>>', type, inviterStaffId, sceneData).report()
    if (type && (inviterStaffId || (sceneData && sceneData.length < 32))) {
      this._data = {
        ...this._data,
        type: "INVITE",
        inviterStaffId: inviterStaffId || sceneData,
        inviterRole,
        inviterName
      }
      this.doCheckRole()
    }

    // 小程序码解析
    vLog.info('START_UP 小程序码解析 sceneData >>>>', sceneData).report()
    if (sceneData && sceneData.length === 32 && !getApp().globalData.refersMark) {
      setSFV(global.STORAGE_GLOBAL_SCREEN_QUERY, sceneData + '')
      return this.initQueryInfo(sceneData)
    }

    return this.initConfig()
  },

  /**
   * 判断是否登录
   */
  verifyToLogin(params) {
    const hasLogin = getUserLogin()
    vLog.info('START_UP verifyToLogin hasLogin, >>>>', hasLogin).report()
    vLog.info('START_UP verifyToLogin params >>>>', JSON.stringify(params)).report()
    // 跳转登录
    if (!hasLogin) {
      let cInstall = {
        hasLogin: 2,
        ...params
      }
      vLog.info('START_UP verifyToLogin cInstall >>>', JSON.stringify(cInstall)).report()
      let rpt = {
        path: `/pages/user/login/index`,
        params: cInstall,
        type: LoginState.JUMP_TARGET,
        way: 'reLaunch'
      }
      setStartUpRoutePageType(rpt)
      return this.initConfig()
    }
    let tParams = {}
    let { routerPage, targetPath, fromType } = params || {}
    let _FILTER = []
    let LoginType = LoginState.JUMP_TARGET

    // 过滤组装
    switch (fromType) {
      case 'TOPIC':
        _FILTER = [].concat(FILTER_QR_KEY)
        LoginType = LoginState.TOPIC
        break

      case "CALCULATOR":
        routerPage = targetPath
        _FILTER = [].concat(FILTER_TARGET_KEY)
        break

      default:
        break
    }

    for (let [tKey, tValue] of Object.entries(params)) {
      if (!_FILTER.includes(tKey)) {
        tParams[tKey] = tValue
      }
    }
    tParams = formatUrlObject(tParams)

    let rpt = {
      path: `${routerPage}?${qs.stringify(tParams)}`,
      type: LoginType,
      way: 'reLaunch'
    }
    vLog.info('START_UP verifyToLogin rpt >>>', JSON.stringify(rpt)).report()
    setStartUpRoutePageType(rpt)
    return this.initConfig()
  },

  //检查登录
  async doCheckLogin(param = {}) {
    vLog.info('START_UP doCheckLogin checkLogin param >>>>>', JSON.stringify(param)).report()
    let executed = 'FAILED'
    const hasLogin = getUserLogin()
    const {
      type = '',
      inviterRole = '',
      inviterStatus = '',
      inviterStaffId = '',
      inviterName = ''
    } = this._data || {}

    // 邀请注册
    if (type && type === 'INVITE') {
      vLog.info('START_UP type,!PASS_CODE.includes(code),inviterStatus >>>>', type, !PASS_CODE.includes(code), inviterStatus).report()
      if (!PASS_CODE.includes(code) || inviterStatus === LoginState.ABSENCE || inviterStatus === LoginState.REGISTER) {
        let rpt = {
          path: `/pages/user/login/index?inviterStaffId=${inviterStaffId}&inviterRole=${inviterRole}&inviterName=${inviterName}`,
          type: LoginState.INVITE,
          way: 'reLaunch'
        }

        if (!hasLogin) {
          setStartUpRoutePageType(rpt)
        }
      }
    }

    const baseParams = getOUInfo() || {}
    if (baseParams && !isEmptyObject(baseParams) && baseParams.openid) {
      return { executed: "DONE" }
    }

    const customerType = getCustomerTypeInt()
    vLog.info('START_UP doCheckLogin (!param || isEmptyObject(param) || !param?.openid || !param.unionid) >>>', !param || isEmptyObject(param) || !param?.openid || !param.unionid)
    if (!param || isEmptyObject(param) || !param?.openid || !param.unionid) {
      return { executed }
    }

    const { executed: _executed, code } = await breakIn({ name: 'doCheckLogin', params: { ...param, customerType } })
    vLog.log('START_UP doCheckLogin _executed,code >>>', _executed, code)
    executed = _executed

    return { executed }
  },

  async doCheckRole() {
    const { inviterRole } = this._data || {}
    const param = {
      openid: getOpenId() || '',
      unionid: getUnionID() || '',
      customerType: ROLE_TYPE[inviterRole] || 0,
    }
    vLog.info('START_UP doCheckRole param>>>', JSON.stringify(param)).report()
    if (!param.openid || !param.unionid) {
      return
    }

    const { code: wxcode } = await wx.login()
    const { success, data, msg, code } = await checkLogin({ code: wxcode, customerType: param.customerType, wechatCode: global.SOURCE_CODE })
      .catch(error => {
        vLog.error('START_UP checkLogin catch error >>>', error).report()
      })
    vLog.info('START_UP doCheckRole success, msg, code >>>', success, msg, code).report()
    if (!success) {
      this._data.inviterStatus = code * 1
    }
  },

  sensors(param) {
    const { shareFrom, inviterStaffId } = qs.parse(param)
    if (shareFrom === EnterSource.INVITE) {
      getApp().sensors.track('qrcode_invite', {
        type: '图片保存',
        invitor: inviterStaffId
      })
    }
  },

  /**
   * 海报小程序码解析
   */
  async initQueryInfo(paramKey) {
    const { success, param, msg } = await getSceneParam({ paramKey })
    vLog.info('START_UP initQueryInfo getSceneParam success, param, msg >>>', success, JSON.stringify(param), msg).report()
    const hasLogin = getUserLogin()
    const userCode = getUserRole()
    this.sensors(param)
    vLog.log('START_UP initQueryInfo hasLogin,userCode >>>', hasLogin, userCode)
    // 指定站点首页
    if (success) {
      if (`${param}`.includes('REFERS')) {
        const { rolePage: rPage = '' } = qs.parse(param)
        getApp().globalData.switchRole = rPage
      }
    }

    return setTimeout(async () => {
      if (!success) {
        interaction.showToast(msg || "")

        return setTimeout(() => {
          this._data.type = 'START_UP'
          let rpt = {
            path: getFirstPath(),
            type: LoginState.START_UP,
            way: 'switchTab'
          }
          setStartUpRoutePageType(rpt)
          return this.initConfig()
        }, 1200)
      }

      /**
       * 指定首页
       */
      if (`${param}`.includes('REFERS')) {
        const { rolePage: rPage = '' } = qs.parse(param)
        vLog.log('START_UP REFERS  qs.parse(param) >>>', qs.parse(param))
        let _currRole = getCurrRoleType()
        if (!hasLogin) { // 游客
          let cInstall = {
            hasLogin: 2,
            rolePage: rPage
          }
          vLog.log('START_UP REFERS >>>', cInstall)
          vLog.info('START_UP REFERS cInstall >>>', JSON.stringify(cInstall)).report()
          let rpt = {
            path: `/pages/user/login/index`,
            params: cInstall,
            type: LoginState.JUMP_TARGET,
            way: 'reLaunch'
          }
          setStartUpRoutePageType(rpt)
          return this.initConfig()
        } else {
          if (_currRole && _currRole == rPage) { // 相同站点，直接跳转
            this._data.type = 'START_UP'
            let rpt = {
              path: getFirstPath(),
              type: LoginState.START_UP,
              way: 'switchTab'
            }
            setStartUpRoutePageType(rpt)
            return this.initConfig()
          } else { // 切换站点
            let cParams = {
              unionid: getUnionID() || '',
              openid: getOpenId() || '',
              customerType: ROLE_TYPE[`${rPage}`] || 0,
            }
            // 查询目标站点是否存在
            const { code: wxcode } = await wx.login()
            const { success, data, msg, code } = await checkLogin({ code: wxcode, customerType: cParams.customerType, wechatCode: global.SOURCE_CODE })
              .catch(error => {
                vLog.error('START_UP checkLogin catch error >>>', error).report()
              })
            vLog.log('START_UP REFERS success,data,code,msg >>>>', success, data, code, msg)
            if (success && !isEmptyObject(data)) { // 存在目标站点身份 可以直接切
              this._data.type = 'REFERS'
              let rpt = {
                path: getFirstPath(),
                type: LoginState.QR_INVITE,
                switchRole: `${rPage}`
              }
              setStartUpRoutePageType(rpt)
              return this.initConfig()
            } else {  // 不存在目标站点身份 需要跳转对应身份的切换身份注册页
              let staffType = getCustomerTypeInt()
              let rParams = {
                registerType: REGISTER_TYPE.CHANGE,
                pageType: 'CHANGE',
                currRole: staffType,
                regFrom: 'START_UP',
                rPage,
                singlePage: true
              }

              return wx.reLaunch({
                url: `/packages-user/pages/role/index?${qs.stringify(rParams)}`
              })
            }
          }
        }
      }

      /**
       * 邀请注册
       */
      if (`${param}`.includes('QR_INVITE')) {
        const vRes = qs.parse(`${param}`)
        vLog.log('START_UP QR_INVITE vRes >>>', vRes)
        this._data.type = 'QR_INVITE'
        const {
          routerPage,
          inviterStaffId,
          fromTab,
          inviterName = ''
        } = vRes || {}

        const params = {
          inviterStaffId,
          fromTab,
          inviterName
        }
        let rpt = {
          path: getFirstPath(),
          type: LoginState.START_UP,
          way: 'switchTab'
        }
        vLog.log('START_UP QR_INVITE !PASS_CODE.includes(userCode * 1),!hasLogin >>>', !PASS_CODE.includes(userCode * 1), !hasLogin)
        if (!PASS_CODE.includes(userCode * 1) || !hasLogin) {
          rpt = {
            path: `${routerPage}?${qs.stringify(params)}`,
            type: LoginState.QR_INVITE,
            way: 'reLaunch'
          }
        }
        setStartUpRoutePageType(rpt)
        return this.initConfig()
      }

      /**
       * 列表海报
       */
      if (`${param}`.includes('LIST_POSTER')) {
        const lpRes = qs.parse(`${param}`)
        vLog.log('START_UP LIST_POSTER lpRes >>>', lpRes)
        this._data.type = 'LIST_POSTER'
        const {
          fromTab,
          params,
          routerPage,
        } = lpRes || {}

        let rpt = {
          path: `${routerPage}?${qs.stringify(params)}&fromTab=${fromTab}`,
          type: LoginState.LIST_POSTER,
          way: 'reLaunch'
        }
        setStartUpRoutePageType(rpt)
        return this.initConfig()
      }

      /**
       * 活动引导页
       */
      if (`${param}`.includes('WELCOME')) {
        const welRes = qs.parse(`${param}`)
        vLog.log('START_UP WELCOME lpRes >>>', welRes)
        this._data.type = 'WELCOME'
        const {
          fromTab,
          routerPage,
        } = welRes || {}

        let rpt = {
          path: `${routerPage}?fromTab=${fromTab}&routerStatus=MiniCode`,
          type: LoginState.WELCOME,
          way: 'reLaunch'
        }
        setStartUpRoutePageType(rpt)
        return this.initConfig()
      }

      /**
       * 详情
       */
      if (`${param}`.includes('WEBVIEW')) {
        this._data.type = 'WELCOME'
        let _url = `${wbs.gfH5}`
        if (param && param !== 'null') {
          _url = `${wbs.gfH5}/${param}`
        }
        let [headerUrl, ...rest] = `${_url}`.split('?')
        let _headerURL = `${headerUrl}`.split('/')
        let pageType = _headerURL.pop()

        vLog.log('START_UP WEBVIEW rest >>>', rest)
        const _params = panTransformUrl2Params(decodeURIComponent(_url))
        const { hasLogin: showLogin = '', targetPath = '' } = _params || {}

        // 强制登录 && 游客
        if (showLogin == 2 && !hasLogin) {
          let cInstall = {}
          for (let [cKey, cValue] of Object.entries(_params)) {
            const cKeyNew = doUrlDeCode(cKey)
            const cValueNew = cValue === 'undefined' ? '' : cValue

            cInstall[cKeyNew] = cValueNew
          }
          cInstall = {
            ...cInstall,
            perfix: headerUrl,
            targetPath: '/pages/common/webview/webPage',
            token: getToken(),
            openid: getOpenId(),
            unionid: getUnionID(),
            wechatInfoId: getWechatInfoId()
          }
          vLog.info('START_UP WEBVIEW cInstall >>>', JSON.stringify(cInstall)).report()
          let rpt = {
            path: `/pages/user/login/index`,
            params: cInstall,
            type: LoginState.JUMP_TARGET,
            way: 'reLaunch'
          }
          setStartUpRoutePageType(rpt)
          return this.initConfig()
        }

        let targetRes = {
          token: getToken(),
          openid: getOpenId(),
          unionid: getUnionID(),
          wechatInfoId: getWechatInfoId(),
          pageFlag: 'REGISTER',
          pageType
        }
        for (let [tKey, tValue] of Object.entries(panDClone(_params))) {
          if (!FILTER_KEYS_SHARE_LIST.includes(`${tKey}`)) {
            targetRes[`${tKey}`] = tValue
          }
        }

        let _targetRes = {}
        vLog.log('START_UP pageType && pageType === "productDataDetailReport" >>>', pageType && pageType === "productDataDetailReport")
        // 基金解读
        if (pageType && pageType === "productDataDetailReport") {
          for (let [tKey, tValue] of Object.entries(targetRes)) {
            if (!(typeof tValue === 'string' && tValue === '')) {
              _targetRes[tKey] = tValue
            }
          }

          if (!_targetRes.hasOwnProperty('customType')) {
            _targetRes['customType'] = getCurrRoleType()
          }
          vLog.log('START_UP _targetRes >>>', _targetRes)
          targetRes = { ..._targetRes }
        }

        vLog.log('START_UP targetRes >>>', targetRes)
        let rpt = {
          path: `/pages/common/webview/webPage?url=${targetPath}&${qs.stringify(targetRes)}`,
          type: LoginState.QR_DETAIL,
          way: 'reLaunch'
        }
        setStartUpRoutePageType(rpt)
        return this.initConfig()
      }

      /**
       * 抽奖
       */
      if (`${param}`.includes('H5')) {
        this._data.type = 'H5'
        let rpt = {
          path: `/pages/common/h5/index?pageType=advLotteryActivityPageOut`,
          type: LoginState.H5,
          way: 'reLaunch'
        }
        setStartUpRoutePageType(rpt)
        return this.initConfig()
      }

      /**
       * 活动
       */
      if (`${param}`.includes('ACTIVITY')) {
        const acRes = qs.parse(`${param}`)
        vLog.log('START_UP ACTIVITY acRes >>>', acRes)
        this._data.type = 'ACTIVITY'
        const {
          fromTab,
          routerStatus,
          routerPage,
        } = acRes || {}

        let rpt = {
          path: `${routerPage}?fromTab=${fromTab}&routerStatus=${routerStatus}&${qs.stringify(acRes)}`,
          type: LoginState.JUMP_ACTIVITY,
          way: 'reLaunch'
        }
        setStartUpRoutePageType(rpt)
        return this.initConfig()
      }

      /**
       * 定投罗盘
       */
      if (`${param}`.includes('COMPASS')) {
        const comRes = qs.parse(`${param}`)
        vLog.log('START_UP COMPASS comRes >>>', comRes)
        this._data.type = 'COMPASS'

        let rpt = {
          path: `/pages/mine/compass/index?shareFrom=${EnterSource.COMPASS}`,
          type: LoginState.JUMP_TARGET,
          way: 'reLaunch'
        }
        setStartUpRoutePageType(rpt)
        return this.initConfig()
      }

      /**
       * 专题页
       */
      if (`${param}`.includes('TOPIC')) {
        let topicRes = qs.parse(`${param}`)
        topicRes = formatUrlObject(topicRes)
        const {
          routerPage,
          shouldLogin
        } = topicRes || {}

        // 强制登录
        if (!hasLogin && shouldLogin) {
          let cInstall = {
            hasLogin: 2,
            rolePage: routerPage,
            ...topicRes
          }
          vLog.log('START_UP TOPIC >>>', cInstall)
          let rpt = {
            path: `/pages/user/login/index`,
            params: cInstall,
            type: LoginState.JUMP_TARGET,
            way: 'reLaunch'
          }
          setStartUpRoutePageType(rpt)
          return this.initConfig()
        }

        vLog.log('START_UP TOPIC topicRes >>>', topicRes)
        this._data.type = 'TOPIC'
        let topicParams = {}
        for (let [tKey, tValue] of Object.entries(topicRes)) {
          if (!FILTER_QR_KEY.includes(tKey)) {
            topicParams[tKey] = tValue
          }
        }

        let rpt = {
          path: `${routerPage}?${qs.stringify(topicParams)}`,
          type: LoginState.TOPIC,
          way: 'reLaunch'
        }
        setStartUpRoutePageType(rpt)
        return this.initConfig()
      }

      /**
       * 启动页
       */
      if (`${param}`.includes('START_UP')) {
        this._data.type = 'START_UP'
        let rpt = {
          path: getFirstPath(),
          type: LoginState.START_UP,
          way: 'switchTab'
        }
        setStartUpRoutePageType(rpt)
        return this.initConfig()
      }
    }, 0)
  },

  async initConfig() {
    const roleType = getCurrRoleType()
    const hasLogin = getUserLogin()
    const loginParams = {
      openid: getOpenId(),
      unionid: getUnionID(),
      customerType: getCustomerTypeInt()
    }

    vLog.info('START_UP onShow roleType >>>', roleType).report()
    vLog.info('START_UP onShow loginParams >>>', JSON.stringify(loginParams)).report()
    const _switchRole = getApp().globalData.switchRole
    // 切换站点
    if (_switchRole && _switchRole !== roleType && hasLogin) {
      vLog.log('START_UP WARNING _switchRole && _switchRole !== roleType >>>', _switchRole && _switchRole !== roleType)
      const holdParams = getStartUpRoutePageType() || {}
      vLog.log('START_UP holdParams >>>>', holdParams)
      const roleInfo = {
        ...holdParams,
        targetRole: _switchRole,
        customerType: roleType
      }

      return wx.reLaunch({
        url: `/pages/user/holdUp/index?${qs.stringify(roleInfo)}`,
        complete: () => {
          setStartUpRoutePageType({})
          getApp().globalData.switchRole = ''
        }
      })
    }

    new Promise((resolve) => {
      const loginRes = this.doCheckLogin(loginParams)
      resolve(loginRes)
    }).then((res) => {
      vLog.info('START_UP initConfig Promise res >>>', res).report()
      return this.reRoutePage()
    }).catch((err) => {
      vLog.error('START_UP initConfig Promise catch err >>>', err).report()
    })
  },

  async reRoutePage() {
    const { options } = this._data
    vLog.log('START_UP reRoutePage this.data >>>>', this._data)
    setAppHoldStatus(false)
    const customerType = getCurrRoleType()
    const hasLogin = getUserLogin()
    const routeParams = getStartUpRoutePageType() || {}
    const userCode = getUserRole()
    vLog.info('START_UP reRoutePage customerType >>>>', customerType).report()
    vLog.info('START_UP reRoutePage hasLogin >>>>', hasLogin).report()
    vLog.info('START_UP reRoutePage routeParams >>>>', JSON.stringify(routeParams)).report()
    if (routeParams && !isEmptyObject(routeParams)) {
      let {
        path = '',
        type = '',
        way = '',
        params = {},
        targetRole = '',
        switchRole = '',
        shouldLogin = '',
      } = routeParams || {}

      vLog.log('START_UP reRoutePage 切换身份访问 >>>>>', routeParams.hasOwnProperty('targetRole') && targetRole !== customerType)
      /**
       * 消息推送-判断打开身份-是否是当前站点身份
       */
      if (hasLogin && routeParams.hasOwnProperty('targetRole') && (targetRole !== customerType) && type !== LoginState.START_UP) {
        const roleInfo = {
          ...routeParams,
          targetRole: targetRole,
          customerType
        }

        return wx.reLaunch({
          url: `/pages/user/holdUp/index?${qs.stringify(roleInfo)}`,
          complete: () => {
            setStartUpRoutePageType({})
          }
        })
      }

      vLog.log('START_UP reRoutePage 选择身份 >>>>>', routeParams.hasOwnProperty('switchRole') && switchRole)
      if (hasLogin && routeParams.hasOwnProperty('switchRole') && switchRole) {
        setStartUpRoutePageType({})
        setCustomerTypeInt(ROLE_TYPE[`${switchRole}`])
        getApp().globalData.switchRole = ''
        return shapeShift(`${switchRole}`)
      }

      vLog.log('START_UP reRoutePage 跳转目标页 type >>>>>', type, type == LoginState.JUMP_TARGET)
      if (type === LoginState.JUMP_TARGET) {
        let _panRes = panTransformUrl2Params(decodeURIComponent(path))
        vLog.log('START_UP reRoutePage JUMP LOGIN _panRes #### >>>', _panRes)
        if (params && !isEmptyObject(params)) {
          _panRes = { ...params }
        }
        vLog.info('START_UP reRoutePage JUMP LOGIN _panRes *****>>>', JSON.stringify(_panRes)).report()
        const { hasLogin: shouldLogin = '', rolePage = '' } = _panRes || {}
        vLog.info('START_UP reRoutePage 游客强制登录 shouldLogin,hasLogin >>>>>', shouldLogin, hasLogin).report()
        if ((shouldLogin && shouldLogin == 3) && !hasLogin) {
          let _url = `/pages/user/login/index?shouldLogin=${2}`
          if (rolePage) {
            _url = `${_url}&rolePage=${rolePage}`
          }
          if (_panRes.hasOwnProperty('qrType') && `${_panRes.qrType}`.toUpperCase() === 'TOPIC') {
            _panRes['fromTab'] = 'TOPIC'
          }

          setRouterInRegister(_panRes)
          _url = repairLaunchPath(_url)
          return wx.reLaunch({
            url: `${_url}`,
            complete: () => {
              setStartUpRoutePageType({})
            }
          })
        }
      }

      /**
       * 站点首页分享跳转
       */
      if (type === LoginState.START_UP && shouldLogin) {
        vLog.log('START_UP 切换到首页逻辑（登录态） hasLogin >>>>', hasLogin)
        const _resetSite = {
          fromTab: 'HOME',
          reSetSite: true,
          type,
          way,
          path,
          targetRole,
          targetRoleInt: ROLE_TYPE[`${targetRole}`]
        }
        setRouterInRegister(_resetSite)
        if (!hasLogin) {
          let _url = `/pages/user/login/index?shouldLogin=${2}`
          _url = repairLaunchPath(_url)
          return wx.reLaunch({
            url: `${_url}`,
            complete: () => {
              setStartUpRoutePageType({})
            }
          })
        }

        // 跳转切换身份访问
        vLog.log('START_UP HOLD_UP >>>>', `/pages/user/holdUp/index?${qs.stringify(routeParams)}&holdType=RESET_SITE`)
        return wx.reLaunch({
          url: `/pages/user/holdUp/index?${qs.stringify(routeParams)}&holdType=RESET_SITE`,
          complete: () => {
            setStartUpRoutePageType({})
          }
        })
      }

      // shortLink 跳转
      vLog.info('START_UP reRoutePage type 是否shortLink跳转 >>>>>', type, `${path}`.includes('shortLink')).report()
      if (type === LoginState.JUMP_OTHERS && `${path}`.includes('shortLink')) {
        let _shortLink = `${path}`.split('shortLink:')
        vLog.info('START_UP reRoutePage _shortLink,hasLogin >>>>', _shortLink, hasLogin).report()
        if (hasLogin) {
          if (_shortLink && _shortLink.length > 1 && !_shortLink[0]) {
            _shortLink = `${_shortLink[1]}`.trim()
            breakIn({ name: 'saveShortLink', params: { shortLink: _shortLink } })
          }
        } else {
          _shortLink = `${_shortLink[1]}`.trim()
          vLog.info('START_UP reRoutePage _shortLink ### >>>>', _shortLink).report()
          return wx.reLaunch({
            url: `/pages/user/login/index?shouldLogin=${2}`,
            complete: () => {
              breakIn({ name: 'saveShortLink', params: { shortLink: _shortLink } })
              setStartUpRoutePageType({})
            }
          })
        }
      }

      // 分享打开专题页
      vLog.info('START_UP reRoutePage type 是否跳转专题 >>>>>', type, `${path}`.toLowerCase().includes('topic')).report()
      if (type === LoginState.TOPIC && `${path}`.toLowerCase().includes('topic')) {
        const [_path, _params] = `${path}`.split('?')
        let reRouteParams = {
          targetPath: _path,
          path: _path,
          ...qs.parse(_params)
        }

        let _filterParams = {
          customerType,
          pageFlag: "REGISTER",
          routerStatus: 'SHARE',
          ...reRouteParams,
        }

        vLog.log('START_UP TOPIC reRouteParams >>>>', reRouteParams)
        // 目标站点和当前站点不同
        if (hasLogin && reRouteParams.hasOwnProperty('customerType') && reRouteParams.customerType !== customerType) {
          let currRole = ROLE_TYPE[customerType] || 0
          vLog.log('START_UP reRoutePage currRole >>>>>', currRole)
          vLog.log('START_UP reRoutePage _filterParams >>>>>', _filterParams)
          delete _filterParams.targetPath
          setRouterInRegister(reRouteParams)
          const roleInfo = {
            ..._filterParams,
            targetRole: reRouteParams.customerType,
            customerType: customerType
          }
          return wx.reLaunch({
            url: `/pages/user/holdUp/index?${qs.stringify(roleInfo)}`,
            complete: () => {
              setStartUpRoutePageType({})
            }
          })
        }
      }

      // 补充跳转格式
      path = repairLaunchPath(path)

      // 游客点开推送 补丁【不存在情况】
      vLog.info('START_UP reRoutePage !hasLogin 游客点开推送 >>>>>', !hasLogin, `${path}`.includes('unionList')).report()
      if (!hasLogin && `${path}`.includes('unionList')) {
        let page = getCurrentPages().pop();
        let currPage = page?.route || ''
        vLog.log('START_UP reRoutePage currPage >>>', currPage)

        if (currPage === 'pages/loginAndRegist/startUp/index') {
          const firstPath = getFirstPath()
          vLog.log('START_UP reRoutePage firstPath >>>', firstPath)
          clearTimeout(fTimer)
          return wx.switchTab({
            url: `${firstPath}`
          })
        }
      }

      vLog.info('START_UP path >>>>>', path).report()
      switch (way) {
        case 'reLaunch': {
          setTimeout(() => {
            return wx.reLaunch({
              url: `${path}`,
              complete: () => {
                setStartUpRoutePageType({})
              }
            })
          }, 500);
        }

        case 'switchTab': {
          return wx.switchTab({
            url: `${path}`,
            complete: () => {
              setStartUpRoutePageType({})
            }
          })
        }

        default:
          break
      }
    } else if (options?.contentLibrary === 'true' && userCode !== '30035') {
      return wx.reLaunch({
        url: '/pages/home/<USER>/list?contentLibrary=true',
        complete: () => {
          setStartUpRoutePageType({})
        }
      })
    } else {
      const currScreen = storage.getStorage(global.STORAGE_GLOBAL_SCREEN_CODE) || ''
      vLog.log('START_UP reRoutePage refersMark >>>', getApp().globalData.refersMark)
      vLog.info(`START_UP reRoutePage refersMark:${getApp().globalData.refersMark}, currScreen:${currScreen}`).report()
      if (getApp().globalData.refersMark || APP_START_WAY_IDS.includes(`${currScreen}`)) {
        let page = getCurrentPages().pop();
        let currPage = page?.route || ''
        vLog.info(`START_UP reRoutePage currPage:${currPage}`).report()
        if (!getApp().globalData.tabList.length) {
          const _barList = await convert({ name: 'tabBar' }) || []
          vLog.log('START_UP reRoutePage _barList >>>>>', _barList)
          if (_barList && _barList.length) {
            getApp().globalData.routerHome = true
          }
        }

        if (currPage === 'pages/loginAndRegist/startUp/index') {
          if (!getApp().globalData.tabList.length) {
            let timer = setTimeout(() => {
              clearTimeout(timer)
              const firstPath = getFirstPath()
              vLog.log('START_UP reRoutePage firstPath >>>>>', firstPath)

              getApp().globalData.refersMark = ""
              return wx.switchTab({
                url: `${firstPath}`
              })
            }, 10)
          }
        }
      }
    }

    let fTimer = setTimeout(async () => {
      let firstPath = getFirstPath()
      vLog.info(`START_UP reRoutePage fTimer ftPath:${firstPath} `).report()
      vLog.log('START_UP reRoutePage fTimer tabList  >>>>>', !getApp().globalData.tabList.length)
      if (!getApp().globalData.tabList.length) {
        const _barList = await convert({ name: 'tabBar' }) || []
        vLog.log('START_UP reRoutePage fTimer _barList >>>>>', _barList)
        firstPath = getFirstPath()
        vLog.log('START_UP reRoutePage fTimer firstPath ### >>>>>', firstPath)
        if (_barList && _barList.length) {
          getApp().globalData.routerHome = true
        }
      }

      let page = getCurrentPages().pop();
      let currPage = page?.route || ''
      vLog.info(`START_UP reRoutePage currPage:${currPage} `).report()

      if (currPage === 'pages/loginAndRegist/startUp/index') {
        clearTimeout(fTimer)
        getApp().globalData.refersMark = ""
        return wx.switchTab({
          url: `${firstPath}`
        })
      }
    }, 600)
  },

  decodeUrl(url = '') {
    let _url = decodeURIComponent(url)
    if (`${_url}`.startsWith('%') || `${_url}`.startsWith('http%') || `${_url}`.startsWith('https%')) {
      _url = this.decodeUrl(_url)
    }
    return _url
  },

  canSendChatMessage() {
    const that = this
    wx.qy?.checkSession({
      success: function (e) {
        that.setEntryQY()
      },
      fail: function (e) {
        that.login()
      }
    })
  },

  login() {
    const that = this
    wx.qy.login({
      success: async function (res) {
        if (res.code) {
          const { code } = await qyCode2session({ code: res.code })
          if (code === 0) { that.setEntryQY() }
        } else {
          console.log('企业微信登录失败！' + res.errMsg)
        }
      }
    });
  },

  setEntryQY() {
    wx.qy.getContext({
      success: function (res) {
        global.entryQY = res?.entry || "normal" //返回进入小程序的入口类型
      }, fail: function (res) {
        // wx.showModal({ content: JSON.stringify(res) })
      },
    })
  },

  reSetData() {
    vLog.log('===== START_UP reSetData ===')
    this._data = {
      loadingTimer: '',
      type: '',
      inviterStaffId: '',
      inviterName: '',
      inviterRole: 'CHANNEL',
      inviterStatus: '',

      isHangUp: false,
      hasChannel: false,
      options: {},
    }
  },

  onUnload() {
    // 来源：sourceType;
    // ADMIN_CREATE("后台录入"),
    // SELF_REGISTER("主动注册"),
    // DATA_SYNC("数据同步"),
    // SSO_SYNC("SSO同步")
    breakIn({ name: 'registerApp' })
    vLog.info('>>> START_UP onUnload <<<').report()
    const { loadingTimer } = this._data || {}
    const token = getToken()
    let hasChannel = false
    const _channelImgUrl = getSFV(global.STORAGE_GLOBAL_CURR_CHANNEL_IMG_URL) || ''
    if (_channelImgUrl) {
      hasChannel = true
    }

    // 获取启动页封面
    vLog.log('START_UP 启动页封面 _channelImgUrl,_defaultUrl,hasChannel >>>>', _channelImgUrl, hasChannel)
    if (hasChannel) {
      breakIn({
        name: 'doSetMiniCoverUrl',
        params: {
          hasChannel,
          channelImgUrl: _channelImgUrl
        }
      })
    }

    this.reSetData()
    clearInterval(loadingTimer)
    if (!getApp().globalData.routerHome && !getApp().globalData.tabList.length) {
      convert({ name: 'tabBar' })
    }

    let unTimer = setTimeout(() => {
      clearTimeout(unTimer)
      vLog.info('START_UP onUnload unTimer').report()
      getApp().globalData.refersMark = ""
      getApp().globalData.switchRole = ''
      getApp().globalData.routerHome = false
      storage.setStorage(global.STORAGE_GLOBAL_REROUTE_BY_CODE, false)
      storage.setStorage(global.STORAGE_GLOBAL_SCREEN_QUERY, '')
      if (token) {
        breakIn({ name: 'doRefreshToken' })
      }

      breakIn({ name: 'initSys' })
      breakIn({ name: 'initCurrentMarkDay' })
      breakIn({ name: 'initVersionInfo' })
      breakIn({ name: 'doGetChannelInfos', doSave: true })
      breakIn({ name: 'doClearSaveFiles' })
      breakIn({ name: 'doCheckAttention' })
    }, 2000)
  }
});

<wxs src="../../../wxs/common.wxs" module="tools" />

<cover-view wx:if="{{!modePlat}}" class="activity-android activity-header-bar" style='height:{{navHeight}}px'>
  <cover-image wx:if="{{rStatus}}" bindtap="_onPressBack" src="../../../imgs/nav_icon_white.png" mode="aspectFill" class="left-icon icon-android" />
  <cover-image wx:else bindtap="_onPressBack" src="../../../imgs/nav_icon_home_white.png" mode="aspectFill" class="home-icon icon-android" />
</cover-view>

<view wx:else class="activity-ios activity-header-bar" style='height:{{navHeight}}px;'>
  <view class="normal-nav-back" style='height: {{model.height}}px!important; width: {{model.width}}px!important;' bindtap="_onPressBack">

    <van-icon wx:if="{{rStatus}}" size="24px" color="#fff" class="nav-icon" name="arrow-left" />
    <cover-image wx:else src="../../../imgs/nav_icon_home_white.png" mode="aspectFill" class="home-icon" />
  </view>
</view>

<scroll-view id="scroll-view" style="position:fixed;width:100%;left:0;height:{{contentHeight}}px;;" class="list-container" scroll-y="true" scroll-with-animation enable-back-to-top>
  <view class="content-block">
    <image src="{{contentInfo.header}}" mode="widthFix" class="background" />

    <view class="activity-content-view">
      <view class="activity-date-block">
        <view class="date-header">
          <view>{{'活动开始时间'}}</view>
          <view>{{'活动截止时间'}}</view>
        </view>
        <image src="{{contentInfo.line}}" mode="widthFix" class="line-view" />
        <view class="date-header">
          <view>{{startDate}}</view>
          <view>{{endDate}}</view>
        </view>
      </view>
    </view>
  </view>

  <view class="content-block">
    <image src="{{contentInfo.rule}}" mode="widthFix" class="background" />
  </view>

  <view class="content-block">
    <image src="{{contentInfo.register_content}}" mode="widthFix" class="background" />

    <view class="activity-content-view" style="height: {{register_contentSize}}rpx;margin-top: -{{register_contentSize}}rpx">
      <view class="activity-content-title-bar">
        <view class="title-text">{{'邀请礼'}}</view>
        <view class="activity-title-tips">
          <view class="tips-text">{{'已邀人数'}}</view>
          <view class="count-text">{{inviteNum + '人'}}</view>
        </view>
      </view>

      <image src="{{btnInviteTips}}" data-type="INVITE" class="content-btn" bind:tap="onRewardAction" mode="aspectFit" />
    </view>
  </view>

  <view class="content-block">
    <image src="{{contentInfo.share_content}}" mode="widthFix" class="background" />

    <view class="activity-content-view" style="height: {{share_contentSize}}rpx;margin-top: -{{share_contentSize}}rpx">
      <view class="activity-content-title-bar title-bar-share">
        <view class="title-text">{{'分享礼'}}</view>
        <view class="activity-title-tips">
          <view class="tips-text">{{'分享后浏览人数'}}</view>
          <view class="count-text">{{shareNum + '人'}}</view>
        </view>
      </view>
      <image src="{{btnShareTips}}" data-type="SHARE" class="content-btn share-btn" bind:tap="onRewardAction" mode="aspectFit" />
    </view>
  </view>

  <view class="content-block">
    <image src="{{contentInfo.rewards_register}}" mode="widthFix" class="background" />

    <view class="activity-content-view" style="height: {{rewards_registerSize}}rpx;margin-top: -{{rewards_registerSize}}rpx">
      <view class="float-tips-bar">
        <view class="float-tips-top">
          {{floatTips}}
        </view>

        <view class="float-tips-top">
          {{'剩余名额'}}
          <view class="float-box">{{rQuota}}</view>
        </view>
      </view>
    </view>
  </view>

  <view class="content-block">
    <image src="{{contentInfo.rewards_content}}" mode="widthFix" class="background" />
    <view class="activity-content-view" style="height: {{rewards_contentSize}}rpx;margin-top: -{{rewards_contentSize}}rpx">
      <view class="float-tips-bar">
        <view>
          {{''}}
        </view>
      </view>
    </view>
  </view>

  <view class="content-block">
    <image src="{{contentInfo.footer}}" mode="widthFix" class="background" />

    <view class="activity-content-view" style="height: {{footerSize}}rpx;margin-top: -{{footerSize}}rpx;padding-top: 160rpx;">
      <view class="explain-box" wx:if="{{explain && explain.length}}" wx:for="{{explain}}" wx:for-item="item" wx:for-index="tips" wx:key="tips" data-item="{{item}}">
        <view class="explain-tips">
          {{item || ''}}
        </view>
      </view>
    </view>
  </view>

</scroll-view>
import {
  global,
  storage,
  util,
  systemtInfo,
  interaction,
  eventName,
  enums,
  qs,
  vLog
} from "../../../common/index.js";

import {
  getPromotionalData,
  getInvitationData,
  getUCSystemApiConfig,
  checkDisqualified,
} from "../../../common/nb/home";

import {
  getUnionID,
  getOpenId,
  getToken,
  getWechatInfoId,
  saveVersionInfo,
  setAppHoldStatus,
  getFirstPath,
  getUserLogin,
  getSystemInfo,
  getUser,
  setPreviousPath,
  getUserPhone,
  getCurrRoleType,
  setSystemInfo,
  getCustomerTypeInt,
  getUserId
} from "../../../common/utils/userStorage";

const {
  REFRESH_PAGE_DATA,
  SEND_REGISTER_OPTIONS,
} = eventName

const {
  EnterSource,
  GLOBAL_START_PATH
} = enums

const dayJs = require('dayjs')

const app = getApp()
const {
  screenHeight,
  platform,
  titleHeightPx,
} = systemtInfo
const { rpx2px, isEmptyObject, doJSON_PARSE } = util

const actImgUrl = 'https://aim-pic-dev.gffunds.com.cn/image/course/2022-08-12/8e001b6c-f8ce-4725-ad13-1e11453e0178.png'

Page({
  data: {
    modePlat: (platform === "ios"),
    contentHeight: rpx2px(screenHeight),
    navHeight: titleHeightPx,
    fromTab: '',
    rStatus: false,
    loading: true,
    customerType: 0,

    contentSize: 777,
    footerSize: 700,
    rewards_contentSize: 1535,
    rewards_registerSize: 885,
    register_contentSize: 345,
    share_contentSize: 485,
    floatTips: '完成注册礼的前100名理财经理',
    hQuota: false,
    rQuota: 100,

    activityInfo: {},
    rewardRules: [],
    contentInfo: {},
    popImg: [],

    startDate: '',
    endDate: '',
    explain: [],
    model: wx.getMenuButtonBoundingClientRect(),

    inviteNum: 0,
    shareNum: 0,
    btnInviteTips: '',
    btnShareTips: '',
    INVITE_CURR_STATUS: 'REGISTER',
    SHARE_CURR_STATUS: 'SHARE',
    rewardPool: [],
    inLive: 'BEFORE',

    openByInline: false,
    options: {},
    sysInfo: {},
    wInfo: {},
    showModal: false,
  },

  onLoad(options) {
    vLog.log('ACTIVITY options >>>', options)
    const { fromTab = 'MINE', routerStatus = 'PAGE' } = options || {}

    let customerType = getCustomerTypeInt()
    const sysInfo = getSystemInfo()
    const hasLogin = getUserLogin()
    let model = wx.getMenuButtonBoundingClientRect()
    let {
      startDate = '',
      endDate = '',
      activityRules,
      popImg,
      contentInfo = {},
    } = sysInfo?.staff?.clientParams?.activityConfig || {}

    this.setData({
      loading: false,
      activityRules,
      popImg,
      contentInfo,
      startDate,
      endDate,
    })
    this.setData({
      options,
      customerType,
      wInfo: getUser(),
      sysInfo,
      hasLogin,
      openByInline: storage.getStorage(global.STORAGE_GLOBAL_INLINE_PAGE) || '',
      model,
      fromTab,
      rStatus: routerStatus === 'PAGE',
      activityInfo: sysInfo?.staff?.clientParams?.activityConfig || {}
    }, () => this.initActivity())

    this.initSysInfo()
  },

  onProbe() {
    let _ProbeTimer = setTimeout(() => {
      clearTimeout(_ProbeTimer)
      this.setData({
        loading: false,
      })
    }, 1500)
  },

  initSysInfo() {
    const {
      appId = '',
      envVersion = '',
      version = ''
    } = wx.getAccountInfoSync().miniProgram || {}

    let vTime = dayJs(new Date()).format('YYYY-MM-DD HH:mm')
    saveVersionInfo({ appId, envVersion, version: version ? version : '1.0.0', vTime })
  },

  /**
   * 初始化活动
   * @returns {Promise<void>}
   */
  async initActivity() {
    vLog.log('ACTIVITY initActivity this.data >>>', this.data)
    let { activityInfo, customerType, wInfo, hasLogin } = this.data
    if (hasLogin) {
      const { data = false } = await checkDisqualified()
      this.data.showModal = data
    }
    if (!activityInfo || isEmptyObject(activityInfo)) {
      let customerType = getCurrRoleType()
      interaction.showLoading('加载中...')
      const { success, data, code, msg } = await getUCSystemApiConfig({ customerType })
      interaction.hideLoading()
      vLog.log('ACTIVITY initActivity getUCSystemApiConfig >>>', success, data, code, msg)

      if (success && code == 0) {
        let sysInfo = { ...data }
        for (const [key, value] of Object.entries(sysInfo)) {


          if (Array.isArray(value)) {
            sysInfo[key] = [].concat(value)
          } else if (value instanceof Object) {
            sysInfo[key] = { ...value }
          } else {
            sysInfo[key] = doJSON_PARSE(value || '{}' + '')
          }
        }
        setSystemInfo(sysInfo)
        activityInfo = sysInfo?.staff?.clientParams?.activityConfig || {}
      }
    }

    let { phone: phoneNumber = '' } = wInfo || {}
    let {
      startDate = '',
      endDate = '',
      activityRules,
      popImg,
      rewardRules = [],
      contentInfo = {},
      contentClue = {},
      floatTips = {},
      mock = {}
    } = activityInfo || {}

    this.setData({
      loading: false,
      activityRules,
      popImg,
      contentInfo,
      startDate,
      endDate,
    })

    let hQuota = false
    let rQuota = 100
    let inviteNum = 0
    let shareNum = 0;
    endDate = dayJs(endDate).format('YYYY-MM-DD HH:mm:ss')
    startDate = dayJs(startDate).format('YYYY-MM-DD HH:mm:ss')
    interaction.showLoading('加载中...')

    let promotionalRes = {
      success: false,
      param: {
        inviterStaffNo: 0,
        shareViewTimes: 0
      },
      msg: ''
    }

    let invitationRes = {
      success: false,
      param: {
        hasQuota: false,
        remainingQuota: 100
      },
      msg: ''
    }
    if (!hasLogin) {
      promotionalRes = { success: false, param: {}, msg: '' }
    } else {
      const queryParams = {
        startDate,
        endDate,
        phone: phoneNumber || getUserPhone(),
        customerType
      }
      promotionalRes = await getPromotionalData(queryParams)
      invitationRes = await getInvitationData(queryParams)

      vLog.log('ACTIVITY initActivity invitationRes,promotionalRes >>>', invitationRes, promotionalRes)
      if (!promotionalRes.success) {
        interaction.showToast(promotionalRes.msg || '')
      }

      if (!invitationRes.success) {
        interaction.showToast(invitationRes.msg || '')
      }
    }
    interaction.hideLoading()

    const urlPool = {}
    let heightPool = {}
    let explain = []

    vLog.log('ACTIVITY initActivity urlPool,heightPool >>>', urlPool, heightPool)
    const {
      inviterStaffNo = 0,
      shareViewTimes = 0
    } = promotionalRes.param || {}
    inviteNum = inviterStaffNo
    shareNum = shareViewTimes

    const {
      hasQuota = false, // 是否在100以内
      remainingQuota = 100 //剩余名额
    } = invitationRes.param || {}
    hQuota = hasQuota
    rQuota = remainingQuota

    if (Number(rQuota) <= 0) {
      rQuota = 0
    }

    vLog.log('ACTIVITY mock >>>', mock, !isEmptyObject(mock))
    /**
     * 使用后台配置的测试数据
     */
    if (!isEmptyObject(mock)) {
      const {
        invNum = 0,
        sNum = 0
      } = mock || {}

      if (invNum) {
        inviteNum = invNum
      }

      if (sNum) {
        shareNum = sNum
      }
    }

    const {
      invite = '',
      share = '',
    } = contentClue || {}

    const {
      rulesTips
    } = activityRules || {}
    if (Array.isArray(rulesTips)) {
      explain = rulesTips
    } else if (typeof rulesTips === 'string') {
      explain = [`${rulesTips}`]
    }

    let _fTips = this.data.floatTips
    const {
      register: fTips = ''
    } = floatTips || {}
    if (fTips) {
      _fTips = fTips
    }

    let INVITE_CURR_STATUS = 'NULL'
    let SHARE_CURR_STATUS = 'NULL'
    let btnInviteTips = invite
    let btnShareTips = share
    let inLive = this.getCurrentStatus(startDate, endDate)
    vLog.log('ACTIVITY initActivity inLive  >>>', inLive)

    let shareProps = rewardRules.filter((item) => item && item.type === 'SHARE_LEVEL')
    let inviteProps = rewardRules.filter((item) => item && item.type === 'INVITE_LEVEL')

    let shareLevel = false
    let inviteLevel = false
    let rewardPool = []
    switch (inLive) {
      case "BEFORE":
        break;

      case 'LIVE': {
        const { standard: shareStandard = [] } = shareProps && shareProps[0] || {}
        const { standard: inviteStandard = [] } = inviteProps && inviteProps[0] || {}

        shareStandard.forEach((item) => {
          const { ruleFrom = 0, rewardKey = [] } = item || {}
          if (shareNum >= ruleFrom) {
            shareLevel = true
            rewardPool = rewardPool.concat(rewardKey)
          }
        })
        inviteStandard.forEach((item) => {
          const { ruleFrom = 0, rewardKey = [] } = item || {}
          if (inviteNum >= ruleFrom) {
            inviteLevel = true
            rewardPool = rewardPool.concat(rewardKey)
          }
        })

        /**
         * 获取活动状态
         */
        const { inviteStatus, inviteTips } = this.getLiveStatus(shareLevel, inviteLevel, hQuota, rQuota, 'LIVE', 'INVITE')
        const { shareStatus, shareTips } = this.getLiveStatus(shareLevel, inviteLevel, hQuota, rQuota, 'LIVE', 'SHARE')

        INVITE_CURR_STATUS = inviteStatus
        btnInviteTips = inviteTips

        SHARE_CURR_STATUS = shareStatus
        btnShareTips = shareTips
        break
      }

      case 'AFTER': {
        const { standard: shareStandard = [] } = shareProps && shareProps[0] || {}
        const { standard: inviteStandard = [] } = inviteProps && inviteProps[0] || {}
        shareStandard.forEach((item) => {
          const { ruleFrom = 0, rewardKey = [] } = item || {}
          if (shareNum >= ruleFrom) {
            shareLevel = true
            rewardPool = rewardPool.concat(rewardKey)
          }
        })
        inviteStandard.forEach((item) => {
          const { ruleFrom = 0, rewardKey = [] } = item || {}
          if (inviteNum >= ruleFrom) {
            inviteLevel = true
            rewardPool = rewardPool.concat(rewardKey)
          }
        })

        const { inviteStatus, inviteTips } = this.getLiveStatus(shareLevel, inviteLevel, hQuota, rQuota, 'AFTER', 'INVITE')
        const { shareStatus, shareTips } = this.getLiveStatus(shareLevel, inviteLevel, hQuota, rQuota, 'AFTER', 'SHARE')

        INVITE_CURR_STATUS = inviteStatus
        btnInviteTips = inviteTips

        SHARE_CURR_STATUS = shareStatus
        btnShareTips = shareTips
        break
      }

      default:
        break
    }

    startDate = dayJs(startDate).format('YYYY年MM月DD日')
    endDate = dayJs(endDate).format('YYYY年MM月DD日')

    this.setData({
      loading: false,
      activityRules,
      popImg,
      inviteNum,
      shareNum,
      contentInfo,
      startDate,
      endDate,
      explain,
      hQuota,
      rQuota,

      inLive,
      INVITE_CURR_STATUS,
      SHARE_CURR_STATUS,
      btnInviteTips,
      btnShareTips,
      rewardPool: [...new Set(rewardPool)],
      floatTips: _fTips,
      ...heightPool,
    })
  },

  /**
   * 获取活动状态类型
   * @param shareLevel 分享等级
   * @param inviteLevel 邀请注册等级
   * @param hQuota 是否在名额内
   * @param rQuota 剩余名额
   * @param currStatus 当前活动状态
   * @returns {{bTips: *, sTips: *, cStatus: string}}
   */
  getLiveStatus(shareLevel, inviteLevel, hQuota, rQuota, currStatus = '', clueType = '') {
    vLog.log('ACTIVITY getLiveStatus shareLevel, inviteLevel, rQuota, currStatus,clueType >>>>', shareLevel, inviteLevel, rQuota, currStatus, clueType)
    const { activityInfo } = this.data || {}
    let {
      contentClue = {}
    } = activityInfo || {}

    const {
      invite = '',
      share = '',
      receive = '',
      full = '',
      over = ''
    } = contentClue || {}

    let inviteStatus = 'REGISTER'
    let shareStatus = 'HOME_SHARE'
    let inviteTips = invite
    let shareTips = share


    switch (clueType) {
      case 'INVITE': {
        // 注册礼 ❎ 活动结束 => 活动结束
        if (!inviteLevel && currStatus === 'AFTER') {
          inviteStatus = 'OVER'
          inviteTips = over
          return { inviteStatus, inviteTips }
        }

        // 剩余名额0 ❎ => 名额已满
        if (!hQuota && rQuota == 0) {
          inviteStatus = 'FULL'
          inviteTips = full
          return { inviteStatus, inviteTips }
        }

        // 注册礼 ✅ => 领取礼品
        if (inviteLevel) {
          inviteStatus = 'PRE_IMAGE'
          inviteTips = receive
          return { inviteStatus, inviteTips }
        }

        // 注册礼 ❎ 活动未结束 => 继续邀请
        return { inviteStatus, inviteTips }
      }

      case 'SHARE': {
        // 分享礼 ❎ 活动未结束 => 继续分享
        if (!shareLevel && currStatus === 'LIVE') {
          shareTips = share
          return { shareStatus, shareTips }
        }

        // 分享礼 ✅ 活动结束 => 领取奖励
        if (shareLevel && currStatus === 'AFTER') {
          shareStatus = 'PRE_IMAGE'
          shareTips = receive
          return { shareStatus, shareTips }
        }

        // 分享礼 ❎ 活动结束 => 活动结束
        if (!shareLevel && currStatus === 'AFTER') {
          shareStatus = 'OVER'
          shareTips = over
          return { shareStatus, shareTips }
        }

        return { shareStatus, shareTips }
      }

      default:
        break;
    }
  },

  /**
   * 获取当前活动状态
   * @param startDate
   * @param endDate
   * @returns {string}
   */
  getCurrentStatus(startDate = '', endDate = '') {
    let inLive = 'BEFORE'
    const currDay = dayJs(new Date()).format('YYYY-MM-DD HH:mm:ss')
    let _diffBefore = dayJs(currDay).diff(dayJs(startDate), 'seconds')
    let _diffAfter = dayJs(currDay).diff(dayJs(endDate), 'seconds')

    if (_diffAfter > 0) {
      inLive = 'AFTER'
      return inLive
    }

    if (_diffBefore >= 0 && _diffAfter <= 0) {
      inLive = 'LIVE'
      return inLive
    }

    if (_diffBefore < 0) {
      return inLive
    }

    return inLive
  },

  /**
   * 点击响应
   * @param event
   * @returns {undefined|void}
   */
  async onRewardAction(event = {}) {
    vLog.log('ACTIVITY onRewardAction event, this.data >>>', event, this.data)
    const {
      dataset: {
        type = ''
      }
    } = event.target || {}

    const {
      INVITE_CURR_STATUS,
      SHARE_CURR_STATUS,
      rewardPool,
      hasLogin,
      showModal
    } = this.data

    let currType = 0
    let rHeightLevel = Math.max(...rewardPool)
    let rLowLevel = Math.min(...rewardPool)
    currType = this.doJudgeRewardLevel(rHeightLevel, rLowLevel)
    vLog.log('ACTIVITY doPrevImage currType >>>>', currType)

    if (type === 'INVITE') {
      // 游客登录
      if (!hasLogin && INVITE_CURR_STATUS !== 'OVER') {
        return this.showLoginModal()
      }
      if (!showModal && INVITE_CURR_STATUS !== 'OVER') {
        return wx.showModal({
          title: '仅限理财经理参与',
          showCancel: false,
          confirmText: "确定",
        })
      }
      switch (INVITE_CURR_STATUS) {
        // 活动未开始
        case 'NULL': {
          return wx.showModal({
            title: '活动未开始',
            showCancel: false,
            confirmText: "确定"
          })
        }

        // 名额未满足，去邀请
        case 'REGISTER': {
          return wx.navigateTo({
            url: `/packages-user/pages/invite/index`,
          })
        }

        // 名额已满
        case 'FULL': {
          return interaction.showToast('名额已满')
        }

        // 活动已结束
        case 'OVER': {
          return interaction.showToast('活动已结束')
        }

        // 领取奖励
        case 'PRE_IMAGE': {
          return this.doPrevImage('INVITE')
        }

        default:
          break;
      }
    }

    if (type === 'SHARE') {
      // 游客登录
      if (!hasLogin && SHARE_CURR_STATUS !== 'OVER') {
        return this.showLoginModal()
      }
      if (!showModal && SHARE_CURR_STATUS !== 'OVER') {
        return wx.showModal({
          title: '仅限理财经理参与',
          showCancel: false,
          confirmText: "确定",
        })
      }
      switch (SHARE_CURR_STATUS) {
        case 'NULL': {
          return wx.showModal({
            title: '活动未开始',
            showCancel: false,
            confirmText: "确定"
          })
        }

        // 活动已结束
        case 'OVER': {
          return interaction.showToast('活动已结束')
        }

        // 跳转首页去分享
        case 'HOME_SHARE': {
          let currTabPath = getFirstPath()
          currTabPath = `${currTabPath}`.startsWith('/') ? currTabPath : `/${currTabPath}`
          return wx.switchTab({
            url: `${currTabPath}`
          })
        }

        // 领取奖励
        case 'PRE_IMAGE': {
          return this.doPrevImage('SHARE')
        }

        default:
          break
      }
    }
  },

  /**
   * 登录弹窗
   */
  showLoginModal() {
    const { fromTab, options } = this.data || {}
    let params = {
      token: getToken(),
      openid: getOpenId(),
      unionid: getUnionID(),
      wechatInfoId: getWechatInfoId(),
      pageType: 'AppAdvActivity',
    }

    params = {
      ...options,
      ...params,
      TAG: 'AppAdvActivity',
      fromTab,
      routerStatus: 'SHARE',
      shareFrom: EnterSource.ACTIVITY,
      targetPath: '/pages/loginAndRegist/activity/activity',
    }

    return wx.showModal({
      title: '请先登录,再参加活动',
      content: '',
      confirmText: '立即登录',
      success: res => {
        if (res.confirm) {
          let pagePath = getCurrentPages().pop();
          vLog.log('ACTIVITY  pagePath >>>', pagePath?.route)
          setPreviousPath(pagePath?.route)

          return wx.navigateTo({
            url: '/pages/user/login/index',
            success(res) {
              vLog.log('ACTIVITY go login event >>>', params)
              res.eventChannel.emit(SEND_REGISTER_OPTIONS, params)
            }
          })
        }
      }
    })
  },

  /**
   * 展示表单
   * @param awardType
   */
  doPrevImage(awardType = '') {
    vLog.log('ACTIVITY doPrevImage awardType >>>>', awardType)
    let currType = 0
    let floatImg = ''
    const { popImg, rewardPool } = this.data || {}
    vLog.log('ACTIVITY doPrevImage rewardPool >>>>', rewardPool)
    if (!popImg || !popImg.length) {
      return
    }

    let rHeightLevel = Math.max(...rewardPool)
    let rLowLevel = Math.min(...rewardPool)
    currType = this.doJudgeRewardLevel(rHeightLevel, rLowLevel, awardType)
    vLog.log('ACTIVITY doPrevImage currType >>>>', currType)


    popImg.forEach((item) => {
      const { level, imgUrl } = item || {}
      if (level == currType) {
        floatImg = imgUrl
      }
    })

    return wx.previewImage({
      current: floatImg,
      urls: [floatImg]
    })
  },

  /**
   * 获取奖品等级
   * @param rHeightLevel
   * @param rLowLevel
   * @param type
   * @returns {number}
   */
  doJudgeRewardLevel(rHeightLevel, rLowLevel, type = '') {
    let level = 0

    // 分享礼
    if (type === 'SHARE') {
      switch (rHeightLevel) {
        case 6:
          level = 3
          break

        case 8:
          level = 1
          break

        case 10:
          level = 2
          break

        default:
          break
      }
    }

    return level
  },

  onUnload() {
    const { rStatus = '' } = this.data || {}
    setAppHoldStatus(false)
    storage.setStorage(global.STORAGE_GLOBAL_INLINE_PAGE, false)

    if (!rStatus) {
      app.globalData.emitter.emit(REFRESH_PAGE_DATA, {
        isRefresh: true,
        pageType: 'AppAdvProfileInfo',
        registerRouter: true
      })
    }
  },

  _onPressBack() {
    vLog.log('ACTIVITY _onPressBack this.data >>>', this.data)
    const { rStatus } = this.data || {}
    if (rStatus) {
      return wx.navigateBack({})
    } else {
      return wx.switchTab({
        url: '/pages/home/<USER>'
      })
    }
  },

  onShareAppMessage() {
    const { sysInfo, fromTab } = this.data || {}
    let imageUrl = sysInfo?.staff?.invitationShareImg || actImgUrl
    getApp().sensors.track('MPShare', {
      share_type: "微信好友",
    })
    const sParams = {
      routerPage: `/pages/loginAndRegist/activity/activity`,
      shareFrom: EnterSource.ACTIVITY,
      routerStatus: 'SHARE',
      fromTab,
      faId: getUserId()
    }
    let path = `${GLOBAL_START_PATH}?${qs.stringify(sParams)}`

    return {
      title: '邀请有礼，分享有礼，礼品多多，等你来拿',
      path,
      imageUrl
    }
  }

});

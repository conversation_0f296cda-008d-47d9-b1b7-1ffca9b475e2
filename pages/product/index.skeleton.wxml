<!--
此文件为开发者工具生成，生成时间: 2022/4/18 下午2:35:41
使用方法：
在 /Users/<USER>/Desktop/work/wbs-wechat-investor/pages/product/index.wxml 引入模板

```
<import src="index.skeleton.wxml"/>
<template is="skeleton" wx:if="{{loading}}" />
```

在 /Users/<USER>/Desktop/work/wbs-wechat-investor/pages/product/index.wxss 中引入样式
```
@import "./index.skeleton.wxss";
```

更多详细信息可以参考文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/skeleton.html
-->
<template name="skeleton">
  <view class="sk-container">
    <view is="components/nb-page/nb-page">
      <view class="container page--container" style="background-color:#f7f7f7;">
        <scroll-view scroll-y="true" style="height:642px;margin-top:88px; margin-bottom:82px;">
          <view is="components/x-scroll-view/x-scroll-view" class="list-container">
            <view class="scroll-view--refresh-block scroll-view--x-common" style="height:0px;">
              <view>
                <image class="scroll-view--refresh sk-image"></image>
              </view>
              <view>
                <text class="sk-transparent sk-text-18-7500-234 sk-text">下拉刷新</text>
              </view>
              <view></view>
            </view>
            <scroll-view class="scroll-view--scroll_container" scroll-top="0" scroll-y="true" style="position:fixed;width:100%;left:0;height:642px;top:88px;bottom:0px;">
              <view style="min-height:100%;position: absolute;width:100%;">
                <view>
                  <view is="components/polymers/polymers">
                    <view class="polymers--polymers-block">
                      <view is="components/polymers/cards/search/search">
                        <view style="margin: 15px 14px">
                          <view class="search--search-block" data-item="[object Object]">
                            <image class="search--search-icon sk-image" mode="aspectFill"></image>
                            <view class="search--search-content">
                              <text class="search--search-content-tips sk-transparent sk-text-18-7500-45 sk-text">搜素材、找资料</text>
                            </view>
                          </view>
                        </view>
                      </view>
                    </view>
                    <view class="polymers--polymers-block">
                      <view is="components/polymers/cards/content/content">
                        <view is="components/nb-card/nb-card">
                          <view class="nb-card card--nb-card case card--case ">
                            <view class="nb-item card--nb-item ">
                              <view is="components/polymers/components/titleBar/titleBar">
                                <view class="titleBar--float-title-block" style="margin-top: 10px">
                                  <view class="titleBar--title-left-block" style="margin-left:0px;margin-right:0px;">
                                    <text class="titleBar--title-txt sk-transparent sk-text-18-7500-665 sk-text" style="margin-left:0px;">热门产品</text>
                                    <text class="titleBar--subTitle-txt sk-transparent sk-text-18-7500-935 sk-text">火热销售中</text>
                                  </view>
                                  <view class="titleBar--title-right-block" data-info="[object Object]">
                                    <text class="titleBar--more-txt sk-transparent sk-text-18-7500-607 sk-text">全部热门产品</text>
                                    <view is="miniprogram_npm/vant-weapp/icon/index" class="titleBar--title-next-icon">
                                      <view class="van-icon icon-index--van-icon van-icon-arrow icon-index--van-icon-arrow sk-pseudo sk-pseudo-circle" style="color: #999;font-size: 12px;"></view>
                                    </view>
                                  </view>
                                </view>
                              </view>
                              <view class="content--content-nav" style="margin-top: 0px">
                                <view is="components/scrollable-tabview/scrollable-tabview" class="content--current-item">
                                  <view class="tabview--container tabview--navBar" style="background-color:#fff;">
                                    <scroll-view scroll-with-animation="true" scroll-x="true" scroll-into-view="tabbar0">
                                      <view class="tabview--tab sk-transparent" data-index="0" data-value="0" id="4f557deb--tabbar0" style="font-weight:500;color: #333;background-color:#fff;padding-top: 10px;">
                                        权益
                                        <view class="tabview--indicator" style="background-color:rgba(242, 77, 40, 1);margin-top:5px"></view>
                                      </view>
                                      <view class="tabview--tab sk-transparent" data-index="1" data-value="1" id="4f557deb--tabbar1" style="font-weight:normal;color: #999;background-color:#fff;padding-top: 10px;">
                                        固收
                                        <view class="tabview--indicator" style="background-color:;margin-top:5px"></view>
                                      </view>
                                      <view class="tabview--tab sk-transparent" data-index="2" data-value="2" id="4f557deb--tabbar2" style="font-weight:normal;color: #999;background-color:#fff;padding-top: 10px;">
                                        海外
                                        <view class="tabview--indicator" style="background-color:;margin-top:5px"></view>
                                      </view>
                                      <view class="tabview--tab sk-transparent" data-index="3" data-value="3" id="4f557deb--tabbar3" style="font-weight:normal;color: #999;background-color:#fff;padding-top: 10px;">
                                        指数
                                        <view class="tabview--indicator" style="background-color:;margin-top:5px"></view>
                                      </view>
                                      <view class="tabview--tab sk-transparent" data-index="4" data-value="4" id="4f557deb--tabbar4" style="font-weight:normal;color: #999;background-color:#fff;padding-top: 10px;">
                                        FOF
                                        <view class="tabview--indicator" style="background-color:;margin-top:5px"></view>
                                      </view>
                                    </scroll-view>
                                    <view class="tabview--border-line"></view>
                                  </view>
                                </view>
                              </view>
                              <view class="content--content-list">
                                <view is="components/polymers/items/marketPlan/marketPlan">
                                  <view class="marketPlan--card" style="border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;border-bottom-width: 1px;border-top-left-radius: 0px;border-top-right-radius: 0px;">
                                    <view class="marketPlan--card-box" data-item="[object Object]" style="margin-top:0px;padding-top:0px;border-bottom-color: rgba(238, 238, 238, 0.3)">
                                      <image class="marketPlan--image sk-image" mode="aspectFill"></image>
                                      <view class="marketPlan--card-right">
                                        <text class="marketPlan--market-title-txt marketPlan--text-one-line sk-transparent sk-text-18-7500-599 sk-text">广发鑫享</text>
                                        <view class="marketPlan--card-left-subtitle marketPlan--text-one-line sk-transparent sk-text-18-7500-409 sk-text">002132</view>
                                        <view class="marketPlan--card-bottom-info">
                                          <view class="marketPlan--card-left-time marketPlan--text-one-line sk-transparent sk-text-18-7500-71 sk-text">郑澄然</view>
                                        </view>
                                      </view>
                                    </view>
                                  </view>
                                </view>
                              </view>
                              <view class="content--content-list">
                                <view is="components/polymers/items/marketPlan/marketPlan">
                                  <view class="marketPlan--card" style="border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;border-bottom-width: 1px;border-top-left-radius: 0px;border-top-right-radius: 0px;">
                                    <view class="marketPlan--card-box" data-item="[object Object]" style="margin-top:0px;padding-top:14px;border-bottom-color: rgba(238, 238, 238, 0.3)">
                                      <image class="marketPlan--image sk-image" mode="aspectFill"></image>
                                      <view class="marketPlan--card-right">
                                        <text class="marketPlan--market-title-txt marketPlan--text-one-line sk-transparent sk-text-18-7500-679 sk-text">广发多因子</text>
                                        <view class="marketPlan--card-left-subtitle marketPlan--text-one-line sk-transparent sk-text-18-7500-99 sk-text">002943</view>
                                        <view class="marketPlan--card-bottom-info">
                                          <view class="marketPlan--card-left-time marketPlan--text-one-line sk-transparent sk-text-18-7500-933 sk-text">唐晓斌、杨冬</view>
                                        </view>
                                      </view>
                                    </view>
                                  </view>
                                </view>
                              </view>
                              <view class="content--content-list">
                                <view is="components/polymers/items/marketPlan/marketPlan">
                                  <view class="marketPlan--card" style="border-bottom-left-radius: 6px;border-bottom-right-radius: 6px;border-bottom-width: 0px;border-top-left-radius: 0px;border-top-right-radius: 0px;">
                                    <view class="marketPlan--card-box" data-item="[object Object]" style="margin-top:0px;padding-top:14px;border-bottom-color: #fff">
                                      <image class="marketPlan--image sk-image" mode="aspectFill"></image>
                                      <view class="marketPlan--card-right">
                                        <text class="marketPlan--market-title-txt marketPlan--text-one-line sk-transparent sk-text-18-7500-27 sk-text">广发稳健增长</text>
                                        <view class="marketPlan--card-left-subtitle marketPlan--text-one-line sk-transparent sk-text-18-7500-16 sk-text">270002</view>
                                        <view class="marketPlan--card-bottom-info">
                                          <view class="marketPlan--card-left-time marketPlan--text-one-line sk-transparent sk-text-18-7500-817 sk-text">傅友兴</view>
                                        </view>
                                      </view>
                                    </view>
                                  </view>
                                </view>
                              </view>
                            </view>
                          </view>
                        </view>
                      </view>
                    </view>
                    <view class="polymers--polymers-block">
                      <view is="components/polymers/cards/content/content">
                        <view is="components/nb-card/nb-card">
                          <view class="nb-card card--nb-card case card--case ">
                            <view class="nb-item card--nb-item ">
                              <view is="components/polymers/components/titleBar/titleBar">
                                <view class="titleBar--float-title-block" style="margin-bottom: 14px;margin-top: 10px">
                                  <view class="titleBar--title-left-block" style="margin-left:0px;margin-right:0px;">
                                    <text class="titleBar--title-txt sk-transparent sk-text-18-7500-424 sk-text" style="margin-left:0px;">季度推荐</text>
                                    <text class="titleBar--subTitle-txt sk-transparent sk-text-18-7500-682 sk-text">广发基金独家资料</text>
                                  </view>
                                  <view class="titleBar--title-right-block" data-info="[object Object]">
                                    <text class="titleBar--more-txt sk-transparent sk-text-18-7500-237 sk-text">更多</text>
                                    <view is="miniprogram_npm/vant-weapp/icon/index" class="titleBar--title-next-icon">
                                      <view class="van-icon icon-index--van-icon van-icon-arrow icon-index--van-icon-arrow sk-pseudo sk-pseudo-circle" style="color: #999;font-size: 12px;"></view>
                                    </view>
                                  </view>
                                </view>
                              </view>
                              <view class="content--content-nav" style="margin-top: 0px"></view>
                              <view class="content--content-list">
                                <view is="components/polymers/items/marketPlan/marketPlan">
                                  <view class="marketPlan--card" style="border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;border-bottom-width: 1px;border-top-left-radius: 6px;border-top-right-radius: 6px;">
                                    <view class="marketPlan--card-box" data-item="[object Object]" style="margin-top:-6px;padding-top:12px;border-bottom-color: rgba(238, 238, 238, 0.3)">
                                      <image class="marketPlan--image sk-image" mode="aspectFill"></image>
                                      <view class="marketPlan--card-right">
                                        <text class="marketPlan--market-title-txt marketPlan--text-one-line sk-transparent sk-text-18-7500-23 sk-text">广发鑫享</text>
                                        <view class="marketPlan--card-left-subtitle marketPlan--text-one-line sk-transparent sk-text-18-7500-440 sk-text">002132</view>
                                        <view class="marketPlan--card-bottom-info">
                                          <view class="marketPlan--card-left-time marketPlan--text-one-line sk-transparent sk-text-18-7500-984 sk-text">郑澄然</view>
                                        </view>
                                      </view>
                                    </view>
                                  </view>
                                </view>
                              </view>
                              <view class="content--content-list">
                                <view is="components/polymers/items/marketPlan/marketPlan">
                                  <view class="marketPlan--card" style="border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;border-bottom-width: 1px;border-top-left-radius: 0px;border-top-right-radius: 0px;">
                                    <view class="marketPlan--card-box" data-item="[object Object]" style="margin-top:0px;padding-top:14px;border-bottom-color: rgba(238, 238, 238, 0.3)">
                                      <image class="marketPlan--image sk-image" mode="aspectFill"></image>
                                    </view>
                                  </view>
                                </view>
                              </view>
                            </view>
                          </view>
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </scroll-view>
          </view>
          <view is="miniprogram_npm/vant-weapp/toast/index" id="van-toast">
            <view is="miniprogram_npm/vant-weapp/transition/index"></view>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
</template>
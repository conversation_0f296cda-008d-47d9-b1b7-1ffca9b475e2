<!-- <text>pages/product/index.wxml</text> -->
<import src="index.skeleton.wxml"/>
<nb-page
    tabBarType="{{tBarType}}"
    navShowBack="{{false}}"
    contentUseView="{{false}}"
    navTitle=""
    floatMarginSize="{{10}}"
    inHomePage="{{true}}"
    showTabBar="{{true}}">
  <template is="skeleton" wx:if="{{loading}}" />
  <x-scroll-view
      wx:else
      enableRefresh="{{true}}"
      class="list-container"
      elementHeight="{{contentHeight}}"
      refreshing="{{refreshing}}"
      nomore="{{nomore}}"
      enableLoadMore="{{false}}"
      resetting="{{true}}"
      scrollTop='{{scrollTop}}'
      floatMarginSize="{{10}}"
      bindpulldownrefresh="handlePullDownRefresh">

    <view wx:if="{{cardInfos.length>0}}">
      <polymers
          wx:if="{{cardInfos.length>0}}"
          data="{{cardInfos}}"
          fromTab="{{pageName}}"
      />
    </view>

    <view wx:else>
      <template is="skeleton" wx:if="{{!refreshing}}"/>
    </view>
  </x-scroll-view>
  <van-toast id="van-toast"/>
</nb-page>


import {
  systemtInfo,
  util,
  eventName,
  storage,
  global,
  enums,
  vLog,
  userAction, breakIn
} from '../../common/index'

import {
  getTabBarConfig,
  getTabBarList,
  setAppHoldStatus,
  setLoadingMoment
} from "../../common/utils/userStorage";

const {
  titleHeight,
  screenHeight,
  footHeight,
} = systemtInfo

const {
  rpx2px,
  isEmptyObject
} = util;

const {
  DISMISS_TAB_BAR,
  SET_SHOW_RECORD,
  FETCH_NET_DATA_ERROR,
  SET_SCROLL_TO_TARGET,
  SET_REFRESH_PAGE,
  TAB_LISTENER_EVENT,
} = eventName

const {
  SHARE_BLOCK_DEFAULT,
} = enums

const app = getApp()
const pageName = 'PRODUCT'

Page({
  /**
   * 页面的初始数据
   */
  data: {
    cardInfos: [],
    pageName,

    refreshing: false,
    nomore: false,
    contentHeight: rpx2px(screenHeight - titleHeight - footHeight),

    loading: true,
    showEmptyPage: false,
    scrollTop: 0,
    tBarType: 0,
    isMarkDay: false
  },

  onLoad() {
    getApp().event.on(TAB_LISTENER_EVENT, (event) => {
      this.onHandleEvent(event)
    })

    let _tConfig = getTabBarConfig()
    for (let [tKey, tValue] of Object.entries(_tConfig)) {
      if (pageName === tValue){
        this.setData({tBarType: tKey * 1})
      }
    }

    return this.init()
  },

  async init() {
    let that = this
    await Promise.allSettled([
      that.getCPInfo(),
      userAction({name: 'initWxInfo', params: {}})
    ]).then((res) => {
      console.log(`${pageName} init res >>`,res)
      that.onProbe()
    }).catch((err) => {
      vLog.error(`${pageName} onLoad err >>>`, err).report()
    })
  },

  onHandleEvent(event = {}) {
    const {key, tab} = event

    switch (key) {
      case SET_SCROLL_TO_TARGET:
        if (tab === pageName){
          this.handlePullDownRefresh()
        }
        break

      case FETCH_NET_DATA_ERROR:
        this.doCancelFresh()
        break

      case SET_REFRESH_PAGE:
        this.setRefreshPage()
        break

      case DISMISS_TAB_BAR:
        this.doSetPageHeight()
        break

      case SET_SHOW_RECORD:
        this.onShowRecordModel()
        break

      default:
        break
    }
  },

  setScrollTo(tab, scrollTo = 0) {
    if (tab === pageName){
      this.setData({
        scrollTop: scrollTo
      }, () => this.setRefreshPage())
    }
  },

  onProbe() {
    const {cardInfos} = this.data
    vLog.log(`${pageName} onProbe cardInfos >>>>`, cardInfos)
    if (!cardInfos || !cardInfos.length){
      this.setData({
        loading: false,
        refreshing: false,
        showEmptyPage: true
      }, () => this.getCPInfo(true))
    }
  },

  setRefreshPage() {
    return this.doRefreshProductInfo()
  },

  doSetPageHeight() {
    this.setData({
      contentHeight: rpx2px(screenHeight - titleHeight)
    })
  },

  onShowRecordModel() {
    return userAction({name: 'showRecordModal', params: {tabName: pageName}})
  },

  doCancelFresh() {
    let timer = setTimeout(() => {
      clearTimeout(timer)
      setLoadingMoment(false)
      this.setData({
        refreshing: false,
      })
    }, 1200)
  },

  async getCPInfo(doFresh = true) {
    let tList = (getApp().globalData.tabList)?.length ? getApp().globalData.tabList : getTabBarList() || []
    vLog.log(`${pageName} tList origin >>>>`, tList)
    vLog.log(`${pageName} getCPInfo this.data  >>>`, this.data)
    vLog.log(`${pageName} getCPInfo doFresh  >>>`, doFresh)
    const {cardInfos} = this.data
    if ((cardInfos && cardInfos.length) && !doFresh){
      return Promise.resolve(cardInfos)
    }

    if (doFresh){
      const _cardInfos = await breakIn({name: 'getTabCardInfo', params: {pageName}})
      this.setData({
        cardInfos: _cardInfos,
        loading: false,
        refreshing: false,
      })

      return Promise.resolve(_cardInfos)
    }

    if (tList && tList.length){
      tList = tList.find((tItem) => {
        const {
          pageTabBarConf: {
            tabName = ''
          }
        } = tItem || {}

        return pageName === tabName
      })
      if (tList && !isEmptyObject(tList)){
        const {cardInfos = []} = tList
        const _cardInfos = []

        cardInfos.forEach((cItem) => {
          const props = {
            ...cItem,
            fromTab: pageName
          }
          _cardInfos.push(props)
        })
        this.setData({
          cardInfos: _cardInfos,
          loading: false,
          refreshing: false,
        })

        return Promise.resolve(_cardInfos)
      }
    }
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {
    setAppHoldStatus(false)
    storage.setStorage(global.STORAGE_GLOBAL_RESET_SHARE_PATH_DATA, {})
  },

  async doRefreshProductInfo() {
    let that = this
    await Promise.allSettled([
      userAction({name: 'initWxInfo', params: {}}),
      this.getCPInfo(true)
    ]).then((res) => {
      console.log(`${pageName} doRefreshProductInfo res >>`,res)
      setLoadingMoment(false)
      that.setData({
        refreshing: false,
      })
    }).catch((err) => {
      vLog.error(`${pageName} doRefreshProductInfo  err>>>`, err).report()
    })
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  handlePullDownRefresh: function() {
    setLoadingMoment(true)
    setAppHoldStatus(false)
    this.setData({
      nomore: false,
    }, () => {
      return this.doRefreshProductInfo()
    })
  },

  handleLoadMore() {
    this.setData({
      nomore: false
    });
  },

  onShareAppMessage() {
    const shareData = breakIn({name: 'getPageShareInfo', params: {fromTab: pageName}})
    getApp().sensors.track('MPShare', {
      share_type: "微信好友",
    })
    return {
      ...SHARE_BLOCK_DEFAULT,
      ...shareData
    }
  },
})

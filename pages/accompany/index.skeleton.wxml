<!--
此文件为开发者工具生成，生成时间: 2022/4/18 下午2:08:57
使用方法：
在 /Users/<USER>/Desktop/work/wbs-wechat-investor/pages/accompany/index.wxml 引入模板

```
<import src="index.skeleton.wxml"/>
<template is="skeleton" wx:if="{{loading}}" />
```

在 /Users/<USER>/Desktop/work/wbs-wechat-investor/pages/accompany/index.wxss 中引入样式
```
@import "./index.skeleton.wxss";
```

更多详细信息可以参考文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/skeleton.html
-->
<template name="skeleton">
  <view class="sk-container">
    <view is="components/nb-page/nb-page">
      <view class="container page--container" style="background-color:#f7f7f7;">
        <scroll-view scroll-y="true" style="height:642px;margin-top:88px; margin-bottom:82px;">
          <view is="components/x-scroll-view/x-scroll-view" class="list-container">
            <view class="scroll-view--refresh-block scroll-view--x-common" style="height:0px;">
              <view>
                <image class="scroll-view--refresh sk-image"></image>
              </view>
              <view>
                <text class="sk-transparent sk-text-18-7500-74 sk-text">下拉刷新</text>
              </view>
              <view></view>
            </view>
            <scroll-view class="scroll-view--scroll_container" scroll-top="0" scroll-y="true" style="position:fixed;width:100%;left:0;height:642px;top:88px;bottom:0px;">
              <view style="min-height:100%;position: absolute;width:100%;">
                <view>
                  <view is="components/polymers/polymers">
                    <view class="polymers--polymers-block">
                      <view is="components/polymers/cards/content/content">
                        <view is="components/nb-card/nb-card">
                          <view class="nb-card card--nb-card case card--case ">
                            <view class="nb-item card--nb-item ">
                              <view is="components/polymers/components/titleBar/titleBar">
                                <view class="titleBar--float-title-block" style="margin-top: 20px">
                                  <view class="titleBar--title-left-block" style="margin-left:0px;margin-right:0px;">
                                    <text class="titleBar--title-txt sk-transparent sk-text-18-7500-852 sk-text" style="margin-left:0px;">话术</text>
                                    <text class="titleBar--subTitle-txt sk-transparent sk-text-18-7500-360 sk-text">广发基金独家售后</text>
                                  </view>
                                  <view class="titleBar--title-right-block" data-info="[object Object]">
                                    <text class="titleBar--more-txt sk-transparent sk-text-18-7500-289 sk-text">更多</text>
                                    <view is="miniprogram_npm/vant-weapp/icon/index" class="titleBar--title-next-icon">
                                      <view class="van-icon icon-index--van-icon van-icon-arrow icon-index--van-icon-arrow sk-pseudo sk-pseudo-circle" style="color: #999;font-size: 12px;"></view>
                                    </view>
                                  </view>
                                </view>
                              </view>
                              <view class="content--content-nav" style="margin-top: 0px">
                                <view is="components/scrollable-tabview/scrollable-tabview" class="content--current-item">
                                  <view class="tabview--container tabview--navBar" style="background-color:#fff;">
                                    <scroll-view scroll-with-animation="true" scroll-x="true" scroll-into-view="tabbar0">
                                      <view class="tabview--tab sk-transparent" data-index="0" data-value="0" id="b2c3421d--tabbar0" style="font-weight:500;color: #333;background-color:#fff;padding-top: 10px;">
                                        基金定投
                                        <view class="tabview--indicator" style="background-color:rgba(242, 77, 40, 1);margin-top:5px"></view>
                                      </view>
                                      <view class="tabview--tab sk-transparent" data-index="1" data-value="1" id="b2c3421d--tabbar1" style="font-weight:normal;color: #999;background-color:#fff;padding-top: 10px;">
                                        营销技巧
                                        <view class="tabview--indicator" style="background-color:;margin-top:5px"></view>
                                      </view>
                                      <view class="tabview--tab sk-transparent" data-index="2" data-value="2" id="b2c3421d--tabbar2" style="font-weight:normal;color: #999;background-color:#fff;padding-top: 10px;">
                                        售后安抚
                                        <view class="tabview--indicator" style="background-color:;margin-top:5px"></view>
                                      </view>
                                    </scroll-view>
                                    <view class="tabview--border-line"></view>
                                  </view>
                                </view>
                              </view>
                              <view class="content--content-list">
                                <view is="components/polymers/items/news/news">
                                  <view class="news--card" style="border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;border-bottom-width: 1px;border-top-left-radius: 0px;border-top-right-radius: 0px;">
                                    <view class="news--card-box" data-item="[object Object]" style="margin-top:0px;padding-top:0px;border-bottom-color: rgba(238, 238, 238, 0.3)">
                                      <view class="news--card-left">
                                        <view class="news--card-left-title news--text-two-line sk-transparent sk-text-18-7500-582 sk-text">第29课：定投客户抱怨不赚钱，怎么办？</view>
                                        <view class="news--card-news-bottom">
                                          <view class="news--card-subtitle-txt news--text-one-line sk-transparent sk-text-18-7500-67 sk-text">
                                            每天1分钟，售后更轻松
                                          </view>
                                        </view>
                                      </view>
                                      <image class="news--image sk-image" mode="aspectFill"></image>
                                    </view>
                                  </view>
                                </view>
                              </view>
                              <view class="content--content-list">
                                <view is="components/polymers/items/news/news">
                                  <view class="news--card" style="border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;border-bottom-width: 1px;border-top-left-radius: 0px;border-top-right-radius: 0px;">
                                    <view class="news--card-box" data-item="[object Object]" style="margin-top:0px;padding-top:14px;border-bottom-color: rgba(238, 238, 238, 0.3)">
                                      <view class="news--card-left">
                                        <view class="news--card-left-title news--text-two-line sk-transparent sk-text-18-7500-917 sk-text">基金定投最全十问十答，看了就能秒懂定投！</view>
                                        <view class="news--card-news-bottom">
                                          <view class="news--card-subtitle-txt news--text-one-line sk-transparent sk-text-18-7500-881 sk-text">
                                            基金定投最全十问十答，看了就能秒懂定投！
                                          </view>
                                        </view>
                                      </view>
                                      <image class="news--image sk-image" mode="aspectFill"></image>
                                    </view>
                                  </view>
                                </view>
                              </view>
                              <view class="content--content-list">
                                <view is="components/polymers/items/news/news">
                                  <view class="news--card" style="border-bottom-left-radius: 6px;border-bottom-right-radius: 6px;border-bottom-width: 0px;border-top-left-radius: 0px;border-top-right-radius: 0px;">
                                    <view class="news--card-box" data-item="[object Object]" style="margin-top:0px;padding-top:14px;border-bottom-color: #fff">
                                      <view class="news--card-left">
                                        <view class="news--card-left-title news--text-two-line sk-transparent sk-text-18-7500-960 sk-text">【干货收藏】基金定投拒绝话术——情景篇</view>
                                        <view class="news--card-news-bottom">
                                          <view class="news--card-subtitle-txt news--text-one-line sk-transparent sk-text-18-7500-178 sk-text">
                                            基金定投拒绝话术——情景篇
                                          </view>
                                        </view>
                                      </view>
                                      <image class="news--image sk-image" mode="aspectFill"></image>
                                    </view>
                                  </view>
                                </view>
                              </view>
                            </view>
                          </view>
                        </view>
                      </view>
                    </view>
                    <view class="polymers--polymers-block">
                      <view is="components/polymers/cards/content/content">
                        <view is="components/nb-card/nb-card">
                          <view class="nb-card card--nb-card case card--case ">
                            <view class="nb-item card--nb-item ">
                              <view is="components/polymers/components/titleBar/titleBar">
                                <view class="titleBar--float-title-block" style="margin-top: 10px">
                                  <view class="titleBar--title-left-block" style="margin-left:0px;margin-right:0px;">
                                    <text class="titleBar--title-txt sk-transparent sk-text-18-7500-457 sk-text" style="margin-left:0px;">课程</text>
                                    <text class="titleBar--subTitle-txt sk-transparent sk-text-18-7500-327 sk-text">广发基金独家课程</text>
                                  </view>
                                  <view class="titleBar--title-right-block" data-info="[object Object]">
                                    <text class="titleBar--more-txt sk-transparent sk-text-18-7500-660 sk-text">更多</text>
                                    <view is="miniprogram_npm/vant-weapp/icon/index" class="titleBar--title-next-icon">
                                      <view class="van-icon icon-index--van-icon van-icon-arrow icon-index--van-icon-arrow sk-pseudo sk-pseudo-circle" style="color: #999;font-size: 12px;"></view>
                                    </view>
                                  </view>
                                </view>
                              </view>
                              <view class="content--content-nav" style="margin-top: 0px">
                                <view is="components/scrollable-tabview/scrollable-tabview" class="content--current-item">
                                  <view class="tabview--container tabview--navBar" style="background-color:#fff;">
                                    <scroll-view scroll-with-animation="true" scroll-x="true" scroll-into-view="tabbar0">
                                      <view class="tabview--tab sk-transparent" data-index="0" data-value="0" id="e0ea6a3a--tabbar0" style="font-weight:500;color: #333;background-color:#fff;padding-top: 10px;">
                                        初级课程
                                        <view class="tabview--indicator" style="background-color:rgba(242, 77, 40, 1);margin-top:5px"></view>
                                      </view>
                                      <view class="tabview--tab sk-transparent" data-index="1" data-value="1" id="e0ea6a3a--tabbar1" style="font-weight:normal;color: #999;background-color:#fff;padding-top: 10px;">
                                        中级课程
                                        <view class="tabview--indicator" style="background-color:;margin-top:5px"></view>
                                      </view>
                                      <view class="tabview--tab sk-transparent" data-index="2" data-value="2" id="e0ea6a3a--tabbar2" style="font-weight:normal;color: #999;background-color:#fff;padding-top: 10px;">
                                        高级课程
                                        <view class="tabview--indicator" style="background-color:;margin-top:5px"></view>
                                      </view>
                                      <view class="tabview--tab sk-transparent" data-index="3" data-value="3" id="e0ea6a3a--tabbar3" style="font-weight:normal;color: #999;background-color:#fff;padding-top: 10px;">
                                        客户管理课程
                                        <view class="tabview--indicator" style="background-color:;margin-top:5px"></view>
                                      </view>
                                    </scroll-view>
                                    <view class="tabview--border-line"></view>
                                  </view>
                                </view>
                              </view>
                              <view class="content--content-list">
                                <view is="components/polymers/items/news/news">
                                  <view class="news--card" style="border-bottom-left-radius: 6px;border-bottom-right-radius: 6px;border-bottom-width: 0px;border-top-left-radius: 0px;border-top-right-radius: 0px;">
                                    <view class="news--card-box" data-item="[object Object]" style="margin-top:0px;padding-top:0px;border-bottom-color: #fff">
                                      <view class="news--card-left">
                                        <view class="news--card-left-title news--text-two-line sk-transparent sk-text-18-7500-694 sk-text">理财的十万个为什么</view>
                                        <view class="news--card-news-bottom">
                                          <view class="news--card-subtitle-txt news--text-one-line sk-transparent sk-text-18-7500-820 sk-text">
                                            《带着菜单去讲课》系列课程
                                          </view>
                                        </view>
                                      </view>
                                      <image class="news--image sk-image" mode="aspectFill"></image>
                                    </view>
                                  </view>
                                </view>
                              </view>
                            </view>
                          </view>
                        </view>
                      </view>
                    </view>
                    <view class="polymers--polymers-block">
                      <view is="components/polymers/cards/content/content">
                        <view is="components/nb-card/nb-card">
                          <view class="nb-card card--nb-card case card--case ">
                            <view class="nb-item card--nb-item ">
                              <view is="components/polymers/components/titleBar/titleBar">
                                <view class="titleBar--float-title-block" style="margin-top: 10px">
                                  <view class="titleBar--title-left-block" style="margin-left:0px;margin-right:0px;">
                                    <text class="titleBar--title-txt sk-transparent sk-text-18-7500-490 sk-text" style="margin-left:0px;">文章</text>
                                    <text class="titleBar--subTitle-txt sk-transparent sk-text-18-7500-682 sk-text">公众号热文</text>
                                  </view>
                                  <view class="titleBar--title-right-block" data-info="[object Object]">
                                    <text class="titleBar--more-txt sk-transparent sk-text-18-7500-169 sk-text">更多</text>
                                    <view is="miniprogram_npm/vant-weapp/icon/index" class="titleBar--title-next-icon">
                                      <view class="van-icon icon-index--van-icon van-icon-arrow icon-index--van-icon-arrow sk-pseudo sk-pseudo-circle" style="color: #999;font-size: 12px;"></view>
                                    </view>
                                  </view>
                                </view>
                              </view>
                              <view class="content--content-nav" style="margin-top: 0px">
                                <view is="components/scrollable-tabview/scrollable-tabview" class="content--current-item">
                                  <view class="tabview--container tabview--navBar" style="background-color:#fff;">
                                    <scroll-view scroll-with-animation="true" scroll-x="true" scroll-into-view="tabbar0">
                                      <view class="tabview--tab sk-transparent" data-index="0" data-value="0" id="65fb1610--tabbar0" style="font-weight:500;color: #333;background-color:#fff;padding-top: 10px;">
                                        定投专区
                                      </view>
                                      <view class="tabview--tab sk-transparent" data-index="1" data-value="1" id="65fb1610--tabbar1" style="font-weight:normal;color: #999;background-color:#fff;padding-top: 10px;">
                                        养基课堂
                                      </view>
                                      <view class="tabview--tab sk-transparent" data-index="2" data-value="2" id="65fb1610--tabbar2" style="font-weight:normal;color: #999;background-color:#fff;padding-top: 10px;">
                                        FOF小课堂
                                      </view>
                                      <view class="tabview--tab sk-transparent" data-index="3" data-value="3" id="65fb1610--tabbar3" style="font-weight:normal;color: #999;background-color:#fff;padding-top: 10px;">
                                        固收+小课堂
                                      </view>
                                    </scroll-view>
                                  </view>
                                </view>
                              </view>
                            </view>
                          </view>
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </scroll-view>
          </view>
          <view is="miniprogram_npm/vant-weapp/toast/index" id="van-toast">
            <view is="miniprogram_npm/vant-weapp/transition/index"></view>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
</template>
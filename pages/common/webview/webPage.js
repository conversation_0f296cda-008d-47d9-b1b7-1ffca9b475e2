// pages/common/webPage.js
import {
  eventName,
  global,
  qs,
  storage,
  util,
  enums,
  vLog,
  interaction,
  breakIn
} from "../../../common/index.js";
import { getShareUuid } from "../../../common/network/api";
import {
  getUnionID,
  getOpenId,
  getToken,
  getUserId,
  getWechatInfoId,
  getWebviewMoment,
  setWebviewMoment,
  setAppHoldStatus,
  getSpringboardStatus,
  setSpringboardStatus,
  getSystemInfo,
  getUser,
  setUserRole,
  getPreviousPath,
  setPreviousPath,
  setRouterInRegister,
  getCurrRoleType,
  getFirstPath,
  setUserLogin
} from "../../../common/utils/userStorage";
import { getShareConfig } from "../../../common/nb/home";
const {
  isEmptyObject,
  getUrlQuery,
  timeFormat,
  doUrlDeCode,
  getQueryParams,
  panDClone,
  getQueryString,
  repairLaunchPath
} = util

const {
  SEND_WEBVIEW_OPTIONS,
  REFRESH_PAGE_DATA,
  SET_REFRESH_PAGE,
  TAB_LISTENER_EVENT,
} = eventName

const {
  EnterSource,
  SHARE_RESPONDENT_REALM,
  TAB_BAR_PATH,
  LIST_PAGE_PATH,
  LIST_PAGE_SEARCH,
  LIST_SHARE_PATH,
  COUNTER_PAGE,
  FILTER_KEYS_BASE,
  FILTER_KEYS_REGISTER,
  FILTER_KEYS_TEMP,
  FILTER_KEYS_SHARE_LIST,
  GLOBAL_START_PATH,
  SHARE_IMG_DEFAULT
} = enums

const DISABLE_SHARE_PAGE = [
  'AppAdvProfileInfo',
  'AppAdvMarketingClue',
  'AppAdvBiBlock'
]

const MARKET_TYPE = [
  'advProductDataNewDetail',
  'advProductDataDetail'
]

const CALCULATOR_PAGE = [
  'advFundScram',
  'reportDetail'
]

const COUNT_INPUT_PAGE = [
  'wechatProfitProbabilityCalculator',
  'wechatFixedInvestmentCalculator',
  'advFixedCaster',
  'advTargetProfit',
  'advFixedCompass'
]

const countImgUrl = 'https://aim-pic-dev.gffunds.com.cn/image/course/2022-11-04/98a075d9-869c-4cb1-9362-81dbcaee06e5.jpg?x-oss-process=image/format,jpg/resize,m_pad,h_400,w_500'
const targetUrl = '/pages/common/webview/webPage'

const ERROR_STATUS = ['null', 'undefined', '', '[object Object]']

const { environment } = wx.getSystemInfoSync()

const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    url: '',
    title: '',
    imgUrl: '',
    pageType: '',
    shareData: {},
    shareFrom: '',
    shareUploadInfo: {},
    shareUInfo: {},
    banShareInfo: {},

    ossCrop: '',
    fillColor: 'FFFFFF',
    targetParams: {},
    hideHomeNav: 0,
    banShare: 0,
    pageFlag: '',

    wxInfo: {},
    sysInfo: {},
    options: {},
    customerType: '',
    tS: +new Date(),
    isEntWx: 0,
    entryQY: 'normal'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(opts = {}) {
    wx.hideShareMenu({ menus: ['shareAppMessage', 'shareTimeline'] })
    vLog.log('WEBVIEW opts,qs.parse(opts) >>>>', opts, qs.parse(opts))
    vLog.info('WEBVIEW onLoad').report()
    const options = { ...qs.parse(opts) }
    for (const item of Object.entries(options)) {
      let [key, value] = item
      key = this.decodeUrl(key)
      value = this.decodeUrl(value)
      options[key] = value
    }

    const sysInfo = getSystemInfo()
    const wxInfo = getUser()
    const customerType = getCurrRoleType()
    const tS = +new Date()
    this.setData({
      tS,
      sysInfo,
      wxInfo,
      options,
      customerType,
      banShare: options?.banShare && Number(options?.banShare) || 0,
      hideHomeNav: options?.hideHomeNav && Number(options?.hideHomeNav) || 0,
      fillColor: options?.fillColor || 'FFFFFF',
      ossCrop: options?.ossCrop || ''
    })

    this.isEntWx()
    this.canSendChatMessage()
    const eventChannel = this.getOpenerEventChannel()
    vLog.info(`WEBVIEW eventChannel:${eventChannel && eventChannel.hasOwnProperty('on')}`).report()
    if (eventChannel && eventChannel.hasOwnProperty('on')) {
      eventChannel.on(SEND_WEBVIEW_OPTIONS, (data) => {
        vLog.info('WEBVIEW SEND_WEBVIEW_OPTIONS data >>>', JSON.stringify(data)).report()
        const { pageFlag = '' } = data || {}
        if (pageFlag && pageFlag === 'SHARE_LIST') {
          return this.slInitWebViewInfo(data)
        }
        this.initWebOptions(data, options)
      })
      if (!isEmptyObject(options)) {
        return this.initWebviewInfo(options)
      }
    } else {
      if (!isEmptyObject(options)) {
        this.initWebviewInfo(options)
      }
    }
  },

  isEntWx() {
    this.data.isEntWx = (environment === 'wxwork' ? 1 : 0)
  },

  canSendChatMessage() {
    this.data.entryQY = global.entryQY
  },

  initWebviewInfo(options) {
    vLog.info('WEBVIEW initWebviewInfo options >>>>', JSON.stringify(options)).report()
    let {
      pageType = '',
      hideHomeNav = 0,
      pageFlag = '',
      url = '',
      customerType = '',
    } = options || {}
    const { isEntWx, entryQY } = this.data
    if (options.hasOwnProperty('token') && !options.token) {
      options['token'] = getToken()
    }
    if (options.hasOwnProperty('wechatInfoId') && !options.wechatInfoId) {
      options['wechatInfoId'] = getWechatInfoId()
    }
    let fRes = { isEntWx, entryQY }
    switch (pageFlag) {
      case "REGISTER": {
        for (const item of Object.entries(panDClone(options))) {
          const [key, value] = item
          if (!FILTER_KEYS_REGISTER.includes(key)) {
            if (!ERROR_STATUS.includes(doUrlDeCode(value + ''))) {
              fRes[key] = doUrlDeCode(value + '')
            }
          }
        }

        url = `${url}?${qs.stringify(fRes)}`
        url = breakIn({ name: 'doFilterUrlKey', url: url })
        console.log('---------url-REGISTER--------' + url)
        this.setData({
          url,
          pageType,
          pageFlag,
        })
        return
      }

      case "SHARE_LIST": {
        for (const item of Object.entries(panDClone(options))) {
          const [key, value] = item
          if (!FILTER_KEYS_SHARE_LIST.includes(key)) {
            if (!ERROR_STATUS.includes(doUrlDeCode(value + ''))) {
              fRes[key] = doUrlDeCode(value + '')
            }
          }

          if (FILTER_KEYS_BASE.includes(value)) {
            storage.setStorage(key, value)
          }
        }
        url = `${url}?${qs.stringify(fRes)}`
        url = breakIn({ name: 'doFilterUrlKey', url: url })
        console.log('---------url-SHARE_LIST--------' + url)
        this.setData({
          url,
          pageType,
          pageFlag,
          hideHomeNav: hideHomeNav && Number(hideHomeNav),
        }, () => {
          if (pageType) {
            return this.initGetShareId(url)
          }
        })
        return
      }

      case "WEBVIEW": {
        url = `${url}`
        let _line = '?'
        if (customerType) {
          if (`${url}`.includes('?')) {
            _line = '&'
          }
          url = `${url}${_line}customerType=${customerType}&isEntWx=${isEntWx}&entryQY=${entryQY}&token=${getToken()}`
        }
        url = breakIn({ name: 'doFilterUrlKey', url: url })
        console.log('---------url-WEBVIEW--------' + url)
        this.setData({
          url,
          pageType,
          pageFlag,
        }, () => {
          if (pageType) {
            return this.initGetShareId(url)
          }
        })
        return
      }

      case "TEMP_MSG": {
        for (const item of Object.entries(panDClone(options))) {
          const [key, value] = item
          if (!FILTER_KEYS_TEMP.includes(key)) {
            if (!ERROR_STATUS.includes(doUrlDeCode(value + ''))) {
              fRes[key] = doUrlDeCode(value + '')
            }
          }
        }

        const msgBase = {
          isEntWx,
          entryQY,
          token: getToken(),
          openid: getOpenId(),
        }

        url = `${url}?${qs.stringify(fRes)}&${qs.stringify(msgBase)}`
        url = breakIn({ name: 'doFilterUrlKey', url: url })
        console.log('---------url-TEMP_MSG-------' + url)
        this.setData({
          url,
          pageType,
          pageFlag,
        })
        return
      }
      default:
        break
    }

    if (!isEmptyObject(options)) {
      return this.initWebOptions(options)
    }
  },

  async initGetShareId(url = '') {
    interaction.showLoading('加载中...')
    const { pageType = '', title = '', url: vUrl = '' } = this.data
    const { value = '' } = getUrlQuery(doUrlDeCode(url))

    const { param } = await getShareUuid({
      timeStamp: +new Date(),
      shareType: 'SHARE_CHAT'
    })
    interaction.hideLoading()
    const shareUploadInfo = {
      "respondent.entityId": value || '',
      "respondent.realm": SHARE_RESPONDENT_REALM[pageType] || 'ARTICLE',
      title: title || '',
      type: 'SHARE_CHAT',
      id: param,
    }

    const [vLink = ''] = `${vUrl}`.split('url=')
    let vQuery = vLink ? getUrlQuery(decodeURIComponent(vLink)) : {}

    const {
      parentid = '',
      sharetype = '',
      sharetime = '',
      faid = ''
    } = vQuery || {}
    const shareUInfo = {
      unionId: getUnionID(),
      type: "VIEW",
      respondent: {
        entityId: value || parentid || '',
        realm: SHARE_RESPONDENT_REALM[pageType] || 'ARTICLE',
      },
      title: title || '',
      userId: faid || getUserId(),
      parentId: parentid,
      shareType: `${sharetype}`.toUpperCase() || 'SHARE_CHAT',
      shareTime: timeFormat(Number(sharetime), 'T') || '',
    }

    this.setData({
      shareUploadInfo,
      shareUInfo
    })
  },

  slInitWebViewInfo(data = {}) {
    const { perfix = '' } = data || {}
    let cInstall = {}
    for (const item of Object.entries(panDClone(data))) {
      let [key, value] = item
      key = doUrlDeCode(key)
      value = doUrlDeCode(value)
      if (!FILTER_KEYS_SHARE_LIST.includes(key)) {
        cInstall[key] = value
      }

      if (FILTER_KEYS_BASE.includes(key)) {
        storage.setStorage(key, value)
      }
    }

    let _url = `${perfix}?${qs.stringify(cInstall)}`
    vLog.log('WEBVIEW slInitWebViewInfo _url @@@@ >>>>', _url)
    _url = breakIn({ name: 'doFilterUrlKey', url: _url })
    this.setData({
      url: _url,
      banShare: 1,
      pageFlag: 'SHARE_LIST'
    }, () => {
      this.setCanShare(_url, false)
    })
  },

  initWebOptions(data = {}, options = {}) {
    vLog.info('WEBVIEW initWebOptions data >>>', JSON.stringify(data)).report()
    vLog.info('WEBVIEW initWebOptions options >>>', JSON.stringify(options)).report()
    const { customerType: cType = '', options: optObj } = this.data
    let _tempObj = {
      ...data,
      ...options,
    }

    if (isEmptyObject(_tempObj)) {
      return
    }

    this.setData({
      banShare: _tempObj?.banShare && Number(_tempObj?.banShare) || 0,
    })
    if (isEmptyObject(data)) {
      let { url = '' } = options || {}
      url = breakIn({ name: 'doFilterUrlKey', url: url })
      this.setData({ url })
      return
    }

    const pParams = {
      token: getToken(),
      openid: getOpenId() || '',
      unionid: getUnionID() || '',
      wechatInfoId: getWechatInfoId(),
    }

    let {
      token = getToken() || '',
      openid = getOpenId() || '',
      unionid = getUnionID() || '',
      wechatInfoId = getWechatInfoId() || '',
      value = '',
      id = '',
      url = '',
      perfix = '',
      pageType = '',
      shareConfig = true,
      categoryId = '',
      title = '',
      clueParams = '',
      userId = '',
      banShare = 0,
      fundcode = '',
      fundname = '',
      dateValue = '',
      customerType = '',
      cName = '',
    } = data || {}

    const bParams = {
      token,
      openid,
      unionid,
      wechatInfoId,
      customerType: customerType || cType,
      clueParams,
      content_category: cName
    }

    if (!clueParams || CALCULATOR_PAGE.includes(pageType)) {
      delete bParams.clueParams
    }

    const _mFrom = {
      shareConfig,
      categoryId
    }

    const _mScram = {
      fundcode,
      dateValue
    }

    const _mReport = {
      fundcode,
    }

    const _baseFromUrl = qs.stringify(bParams)
    const _mScramUrl = qs.stringify(_mScram)
    const _mReportUrl = qs.stringify(_mReport)
    vLog.log('===== WEBVIEW _baseFromUrl,_mScramUrl,_mReportUrl >>>', _baseFromUrl, _mScramUrl, _mReportUrl)

    let _resUrl = `${perfix}?token=${token}&isEntWx=${this.data.isEntWx}&entryQY=${this.data.entryQY}`
    vLog.log('WEBVIEW pageType, _resUrl >>>', pageType, _resUrl)
    switch (pageType) {
      // 海报详情
      case 'AppAdvPosterInfo':
        _resUrl = `${_resUrl}&value=${value}&url=${url}&${_baseFromUrl}`
        break

      case "AppAdvInvestorTrainCom": // 课程详情
      case "investorTrainCom": // 课程详情
        const _mediaFromUrl = qs.stringify(_mFrom)
        _resUrl = `${_resUrl}&value=${id || value}&${_mediaFromUrl}&${_baseFromUrl}&banShare=${banShare}`
        break

      case "discoverNews": // 资讯详情
      case "AppAdvNewsDetail": // 资讯详情
      case "AppAdvLive": // 直播
      case "advProductDataNewDetail": // 营销资料 瀑布流
      case "advProductDataDetail": // 营销资料 分页切换
        _resUrl = `${_resUrl}&value=${id || value}&${_baseFromUrl}&categoryId=${categoryId}&banShare=${banShare}`
        break

      case "AppAdvProfileInfo": // 修改个人信息
      case "AppAdvMarketingClue": // 营销线索
      case "AppAdvCard": // 智能名片
      case "wechatFixedInvestmentCalculator": // 盈利概率测算
      case "wechatProfitProbabilityCalculator": // 定投策略测算
      case "advFixedCaster": // 定投解套器
      case "advTargetProfit": // 目标盈定投
      case "advFixedCompass": //定投计算器
      case "calcPosterMaker": //定投海报
      case 'advRankingList': //排行榜列表
      case 'calcGladPosterMaker': //喜报
        _resUrl = `${_resUrl}&${_baseFromUrl}`
        break

      case "AppAdvBiBlock": // Bi看板
        _resUrl = `${_resUrl}&userId=${userId}`
        break

      case "advFundScram": // 基金解读
        _resUrl = `${_resUrl}&${_mScramUrl}&fundName=${fundname}&${_baseFromUrl}&isWx=1&banShare=${banShare}`
        break

      case "reportDetail": // 运作报告
        _resUrl = `${_resUrl}&${_mReportUrl}&fundName=${fundname}&${_baseFromUrl}&isWx=1&banShare=${banShare}`
        break

      case "outLinks": // 外链
        _resUrl = `${decodeURIComponent(url)}`
        break

      default:
        break
    }
    vLog.info('WEBVIEW _resUrl before >>', _resUrl).report()
    let qParams = getQueryParams(_resUrl)
    if (!qParams.hasOwnProperty('banShare') && pageType !== 'AppAdvBiBlock') {
      qParams = {
        ...qParams,
        ...pParams,
        banShare: 0
      }
    }

    _resUrl = `${perfix}?${qs.stringify(qParams)}`
    if (`${_resUrl}`.startsWith('?')) {
      _resUrl = optObj?.url
    }
    _resUrl = breakIn({ name: 'doFilterUrlKey', url: _resUrl })
    vLog.info('WEBVIEW _resUrl after >>>', _resUrl).report()
    this.setData({
      url: _resUrl,
      pageType,
    }, () => {
      if (title) {
        return wx.setNavigationBarTitle({ title })
      }
    })
  },

  decodeUrl(url = '') {
    let _url = decodeURIComponent(url)
    if (`${_url}`.startsWith('%') || `${_url}`.startsWith('http%') || `${_url}`.startsWith('https%')) {
      _url = this.decodeUrl(_url)
    }
    return _url
  },

  onShow() {
    const { hideHomeNav } = this.data
    const { appid = '', path = '' } = storage.getStorage(global.STORAGE_GLOBAL_SPRINGBOARD_PARAMS)

    if (getSpringboardStatus()) {
      return wx.showModal({
        title: '温馨提示',
        content: '跳转小程序？',
        showCancel: true,//是否显示取消按钮
        cancelText: "取消",//默认是“取消”
        cancelColor: '#333',//取消文字的颜色
        confirmText: "确定",//默认是“确定”
        confirmColor: '#37c',//确定文字的颜色
        success(res) {
          if (res.cancel) {
            vLog.info('WEBVIEW cancel res >>>>', res).report()
          } else {
            return wx.navigateToMiniProgram({
              appId: appid,
              path: `${path}`,
              envVersion: "release",
              complete() {
                setAppHoldStatus(true)
                setSpringboardStatus(false)
              }
            })
          }
        }
      })
    }

    if (hideHomeNav) {
      wx.hideHomeButton()
    }

    if (getWebviewMoment()) {
      setWebviewMoment(false)
    }
  },

  onReady() {
    const { pageType, banShare = 0, url } = this.data
    if (DISABLE_SHARE_PAGE.includes(pageType) || banShare) {
      // wx.hideShareMenu({ menus: ['shareAppMessage', 'shareTimeline'] })
      // const pathInfo = { pageType, banShare }
      // const banShareInfo = breakIn({ name: 'getPageShareInfo', params: { pathInfo } })
      // this.setData({ banShareInfo })
      this.setCanShare(url, false)
    } else {
      this.setCanShare(url)
    }
  },

  onUnload() {
    const { options = {}, pageType: pType = '' } = this.data
    const {
      pageType,
      pageFlag,
      hasLogin: shouldLogin = '',
      hasInvite: shouldInvite = '',
    } = options
    let _pType = pageType || pType
    setAppHoldStatus(false)
    setSpringboardStatus(false)
    setRouterInRegister({})
    setPreviousPath('')
    storage.setStorage(global.STORAGE_GLOBAL_SCREEN_QUERY, '')
    getApp().event.emit(TAB_LISTENER_EVENT, { key: SET_REFRESH_PAGE })

    let backPath = getFirstPath()
    vLog.log('WEBVIEW onUnload backPath >>>', backPath).report()

    if (getApp().globalData.filePath) {
      breakIn({ name: 'doClearSaveFiles' })
    }

    if (!options.hasOwnProperty('code')) {
      // 登录 || 注册
      if ((shouldLogin && shouldLogin == 2) || (shouldInvite && shouldInvite == 2)) {
        return
      }
    }

    if (pageFlag && pageFlag === 'REGISTER') {
      return wx.switchTab({
        url: `${backPath}`,
        complete: () => {
          app.globalData.emitter.emit(REFRESH_PAGE_DATA, { isRefresh: true, pageType, registerRouter: true })
        }
      })
    }

    // 个人中心
    if (_pType === "AppAdvProfileInfo") {
      app.globalData.emitter.emit(REFRESH_PAGE_DATA, { isRefresh: true, pageType, registerRouter: false })
    }

    let redirectPath = getPreviousPath() || ''
    vLog.info('WEBVIEW onUnload redirectPath  >>>', redirectPath).report()
    if (redirectPath) {
      app.globalData.emitter.emit(REFRESH_PAGE_DATA, {
        isRefresh: true,
        pageType: 'AppAdvProfileInfo',
        registerRouter: true
      })

      vLog.info('WEBVIEW onUnload TAB_BAR_PATH  >>>', TAB_BAR_PATH.includes(redirectPath)).report()
      if (TAB_BAR_PATH.includes(redirectPath)) {
        redirectPath = `${redirectPath}`.startsWith('/') ? redirectPath : '/' + redirectPath
        return wx.switchTab({
          url: `${redirectPath}`
        })
      }

      vLog.log('WEBVIEW onUnload LIST_PAGE_PATH  >>>', LIST_PAGE_PATH.includes(redirectPath))
      if (LIST_PAGE_PATH.includes(redirectPath)) {
        let lParams = storage.getStorage(global.STORAGE_GLOBAL_PREVIOUS_PARAMS) || {}
        vLog.log('=WEBVIEW onUnload lParams  >>>', lParams, qs.stringify(lParams))

        return wx.redirectTo({
          url: `../../../${redirectPath}?redirectBack=1&${qs.stringify(lParams)}`,
        })
      }

      vLog.log('WEBVIEW onUnload LIST_PAGE_SEARCH >>>', LIST_PAGE_SEARCH.includes(redirectPath)).report()
      if (LIST_PAGE_SEARCH.includes(redirectPath)) {
        let lParams = storage.getStorage(global.STORAGE_GLOBAL_PREVIOUS_PARAMS) || {}
        vLog.log('WEBVIEW onUnload lParams  >>>', lParams, qs.stringify(lParams))
        return wx.redirectTo({
          url: `../../../${redirectPath}?redirectBack=1&${qs.stringify(lParams)}`,
        })
      }

      vLog.log('WEBVIEW onUnload LIST_SHARE_PATH  >>>', LIST_SHARE_PATH.includes(redirectPath)).report()
      if (LIST_SHARE_PATH.includes(redirectPath)) {
        let lParams = storage.getStorage(global.STORAGE_GLOBAL_PREVIOUS_PARAMS) || {}
        vLog.log('WEBVIEW LIST_SHARE_PATH onUnload lParams  >>>', lParams, qs.stringify(lParams))
        redirectPath = repairLaunchPath(redirectPath)
        return wx.reLaunch({
          url: `/${redirectPath}?redirectBack=1&${qs.stringify(lParams)}`
        })
      }
    }

    if (`${_pType}`.toLowerCase().includes('temp')) {
      getApp().globalData.refersMark = true
    }
  },

  onSaveExitState() {
    vLog.info('WEBVIEW onSaveExitState').report()
    setWebviewMoment(false)
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
    const { pageFlag = '' } = this.data
    vLog.info('WEBVIEW onHide').report()
    setWebviewMoment(true)
    setAppHoldStatus(true)
    setSpringboardStatus(false)
    if (pageFlag && pageFlag === 'TEMP_MSG') {
      getApp().globalData.refersMark = false
    }
    if (getToken()) {
      setUserRole(0)
      setUserLogin(true)
    }
  },

  handleLoadDone(event) {
    vLog.log('WEBVIEW handleLoadDone event >>>', event)
    const { tS } = this.data
    const tE = +new Date();
    const { src = '' } = event?.detail || {}
    if (src) {
      vLog.info('WEBVIEW handleLoadDone >>', tE - tS).report()
    }
  },

  handleLoadError(event) {
    const { url = '', fullUrl = '' } = event?.detail || {}
    if (fullUrl) {
      vLog.info('WEBVIEW handleLoadError url,fullUrl >>', url, fullUrl).report()
    }
  },

  // 当 webview 被销毁时，该方法被触发
  handlePostMessage(event = {}) {
    vLog.log('WEBVIEW handlePostMessage event >>>', event)
    vLog.log('WEBVIEW handlePostMessage this.data >>>', this.data)
    let { pageType, fillColor: fColor, ossCrop: crop = '' } = this.data
    const { data } = event?.detail || {};

    if (data && Array.isArray(data)) {
      let len = data.length && data.length - 1 || 0
      let shareData = data[len] || {}
      if (Object.keys(shareData).length > 10) {
        len = len === 0 ? len : len - 1
        shareData = data[len]
      }
      vLog.log('WEBVIEW handlePostMessage before shareData >>>', shareData)
      if (pageType === 'AppAdvCard') {
        shareData.title = `${shareData.title + '，请惠存'}`
      }

      vLog.log(`WEBVIEW handlePostMessage COUNTER_PAGE.includes(${pageType}) >>>`, COUNTER_PAGE.includes(pageType))
      if (COUNTER_PAGE.includes(pageType)) {
        let [prefixUrl, params] = shareData.url.split('?')
        if (params) {
          let sParams = qs.parse(params)
          shareData.url = prefixUrl + `?${qs.stringify(sParams)}`
        } else {
          shareData.url = prefixUrl
        }
        vLog.log(`WEBVIEW handlePostMessage COUNTER_PAGE.includes(${pageType}) shareData >>>`, shareData)
      }
      let { fillColor = '', ossCrop = '' } = shareData || {}
      if (fillColor) {
        fColor = fillColor
      }
      if (ossCrop) {
        crop = ossCrop
      }

      vLog.log('WEBVIEW handlePostMessage before shareData >>>', shareData)
      this.setData({
        fillColor: `${fColor}`.toUpperCase(),
        shareData,
        ossCrop: crop,
        url: this.data.url
      })
    }
  },

  doFilterImage(imgUrl = '') {
    let _imageUrl = imgUrl
    let _splitImg = _imageUrl.split('?x-oss-process=image')
    if (_splitImg && _splitImg.length > 1) {
      _imageUrl = _splitImg[0]
    }

    return _imageUrl
  },

  async setCanShare(url, canShare = true) {
    if (url) {
      const params = url.split('?')[1]
      const result = qs.parse(params)
      if ((url.includes('/AppAdvInvestorTrainCom?') ||
        url.includes('/investorTrainCom?')) &&
        result?.categoryId) {
        const { code, data } = await getShareConfig({ categoryId: result?.categoryId })
        if (code === 0 && !data) {
          wx.hideShareMenu({ menus: ['shareAppMessage', 'shareTimeline'] })
        } else {
          setTimeout(() => { wx.showShareMenu({ menus: ['shareAppMessage'] }) }, 3000);
        }
      } else {
        if (canShare) {
          setTimeout(() => { wx.showShareMenu({ menus: ['shareAppMessage'] }) }, 3000);
        } else {
          wx.hideShareMenu({ menus: ['shareAppMessage', 'shareTimeline'] })
        }
      }
    }
  },

  onShareAppMessage() {
    vLog.log('WEBVIEW  onShareAppMessage this.data >>>> ', this.data)
    //1.option.webViewUrl 这边拿到的就是当前展示webview的url链接
    let {
      shareData: {
        imgUrl = '',
        title = '',
        url = '',
        ossCrop = ''
      },
      banShareInfo,
      url: oUrl = '',
      title: oTitle = '',
      imgUrl: oImgUrl = '',
      pageType = '',
      fillColor,
      ossCrop: oCrop = '',
      banShare,
      sysInfo,
      customerType
    } = this.data
    getApp().sensors.track('MPShare', {
      share_type: "微信好友",
      need_login: getQueryString(url, 'hasLogin') === 2 ? true : false,
      show_namecard: getQueryString(url, 'hasCard') === 2 ? true : false,
      invite_signup: getQueryString(url, 'hasInvite') === 2 ? true : false,
    })
    let _imageUrl = this.doFilterImage(imgUrl)
    // 小程序二次转发
    if (oUrl && oTitle) {
      _imageUrl = oImgUrl
    }

    let _oUrl = url || oUrl
    if (DISABLE_SHARE_PAGE.includes(pageType) || banShare) {
      return {
        ...banShareInfo
      }
    }

    let shareTitle = title || oTitle || '广发木棉花';
    const webShareParams = {
      routerPage: targetUrl,
      url: _oUrl,
      imgUrl: _imageUrl || oImgUrl,
      title: title || oTitle,
      pageType,
      shareFrom: EnterSource.WEBVIEW,
      fillColor,
      ossCrop: oCrop,
      customerType,
    }
    // 这边作为分享打开的页面
    let path = `${GLOBAL_START_PATH}?${qs.stringify(webShareParams)}`
    vLog.log('WEBVIEW onShareAppMessage path >>>>', path)

    const promise = new Promise(resolve => {
      if (MARKET_TYPE.includes(pageType)) {
        wx.request({
          url: `${_imageUrl}?x-oss-process=image/info`,
          method: 'get',
          success(res) {
            vLog.log('WEBVIEW onShareAppMessage res >>>', res)
            const {
              data: {
                ImageWidth: {
                  value: imgW = 1
                },
                ImageHeight: {
                  value: imgH = 1
                }
              }
            } = res || {}

            let imageUrl = `${_imageUrl || oImgUrl}?x-oss-process=image/format,jpg/${ossCrop}resize,m_pad,h_400,w_500,color_${fillColor}`
            if ((imgH / imgW) > 3) {
              imageUrl = `${_imageUrl || oImgUrl}?x-oss-process=image/resize,m_mfit,w_300,limit_0/crop,h_400,g_north`
            }

            vLog.log('WEBVIEW MARKET_TYPE imageUrl >>>>', imageUrl)
            shareTitle = title || oTitle || '广发木棉花'
            resolve({
              title: shareTitle,
              path,
              imageUrl,
            })
          },
          fail(err) {
            vLog.error('WEBVIEW onShareAppMessage promise err >>>', err).report()
            let imageUrl = `${_imageUrl || oImgUrl}?x-oss-process=image/format,jpg/${ossCrop}resize,m_pad,h_400,w_500,color_${fillColor}`

            shareTitle = title || oTitle || '广发木棉花'
            resolve({
              title: shareTitle,
              path,
              imageUrl,
            })
          }
        })
      } else {
        shareTitle = title || oTitle || '广发木棉花'
        let imageUrl = `${_imageUrl || oImgUrl}?x-oss-process=image/format,jpg/${ossCrop}resize,m_pad,h_400,w_500,color_${fillColor}`
        vLog.log('WEBVIEW MARKET_TYPE promise imageUrl >>>>', imageUrl)

        resolve({
          title: shareTitle,
          path,
          imageUrl
        })
      }
    })

    vLog.log('WEBVIEW onShareAppMessage MARKET_TYPE.includes(pageType) >>>>', MARKET_TYPE.includes(pageType))
    if (MARKET_TYPE.includes(pageType)) {
      vLog.log('WEBVIEW onShareAppMessage MM _oUrl.includes(shareCardEmp) >>>>', `${_oUrl}`.includes('shareCardEmp'))
      if (`${_oUrl}`.includes('shareCardEmp')) {
        fillColor = `${fillColor}`.toUpperCase()
        shareTitle = title || '我的名片'

        let imageUrl = `${imgUrl}?x-oss-process=image/format,jpg/resize,m_pad,h_400,w_500,color_${fillColor}`
        vLog.log('WEBVIEW MARKET_TYPE imageUrl >>>>', imageUrl)
        return {
          title: shareTitle + '，请惠存',
          path,
          imageUrl
        }
      }

      shareTitle = title || oTitle || '广发木棉花'
      let imageUrl = `${_imageUrl || oImgUrl}?x-oss-process=image/format,jpg/${ossCrop}resize,m_pad,h_400,w_500,color_${fillColor}`
      vLog.log('WEBVIEW MARKET_TYPE res imageUrl >>>>', imageUrl)
      return {
        title: shareTitle,
        path,
        imageUrl,
        promise,
      }
    }

    vLog.log('WEBVIEW onShareAppMessage pageType==advFundScram >>>>', pageType === 'advFundScram')
    // 基金解读
    if (pageType === 'advFundScram') {
      let imageUrl = `${_imageUrl || oImgUrl}`
      if (!imageUrl.includes('9bfb5ac9-61a4-4d25-ab0d-bbcbd74c523c')) {
        imageUrl = `${imageUrl}?x-oss-process=image/crop,x_0,y_150/resize,m_pad,h_416,w_520,color_FFFFFF`
      }

      vLog.log('WEBVIEW onShareAppMessage pageType==advFundScram >> shareCardEmp, title,path,imageUrl >>>>', title, path, imageUrl)
      if (`${_oUrl}`.includes('shareCardEmp')) {
        fillColor = `${fillColor}`.toUpperCase()
        shareTitle = title || '我的名片'
        return {
          title: shareTitle + '，请惠存',
          path,
          imageUrl
        }
      }

      vLog.log('WEBVIEW onShareAppMessage pageType==advFundScram, title,path,imageUrl >>>>', title, path, imageUrl)
      shareTitle = title || oTitle || '广发木棉花'
      return {
        title: shareTitle,
        path,
        imageUrl
      }
    }

    vLog.log('WEBVIEW onShareAppMessage pageType==reportDetail >>>>', pageType === 'reportDetail')
    // 运作报告
    if (pageType === 'reportDetail') {
      let imageUrl = `${_imageUrl || oImgUrl}?x-oss-process=image/format,jpg/resize,m_pad,h_400,w_500,color_${fillColor}`

      vLog.log('WEBVIEW onShareAppMessage pageType==reportDetail >> shareCardEmp, title,path,imageUrl >>>>', title, path, imageUrl)
      if (`${_oUrl}`.includes('shareCardEmp')) {
        fillColor = `${fillColor}`.toUpperCase()
        shareTitle = title || '我的名片'
        return {
          title: shareTitle + '，请惠存',
          path,
          imageUrl
        }
      }

      vLog.log('WEBVIEW onShareAppMessage pageType==reportDetail, title,path,imageUrl >>>>', title, path, imageUrl)
      shareTitle = title || oTitle || '广发木棉花'
      return {
        title: shareTitle,
        path,
        imageUrl
      }
    }

    // 定投相关
    if (COUNT_INPUT_PAGE.includes(pageType) || (
      `${_oUrl}`.includes('wechatProfitProbabilityCalculator')
      || `${_oUrl}`.includes('wechatFixedInvestmentCalculator')
      || `${_oUrl}`.includes('advFixedCaster')
      || `${_oUrl}`.includes('advTargetProfit')
      || `${_oUrl}`.includes('advFixedCompass')
    )) {
      vLog.log('WEBVIEW onShareAppMessage COUNT_INPUT_PAGE_OR_Calculator path >>>>', path)
      let imageUrl = countImgUrl || sysInfo?.staff?.clientParams?.compassConfig?.headerUrl || ''
      path = `${path}&calculatorType=Calculator`

      if (!title) {
        if (`${_oUrl}`.includes('advFixedCaster')) {
          title = '定投解套器'
        }

        if (`${_oUrl}`.includes('advTargetProfit')) {
          title = '目标盈定投'
        }

        if (`${_oUrl}`.includes('advFixedCompass')) {
          title = '定投计算器'
        }
      }

      if (imgUrl && !imgUrl.includes('?x-oss-process')) {
        imageUrl = `${imgUrl}?x-oss-process=image/format,jpg/resize,m_fill,h_400,w_500`
      }

      if (`${_oUrl}`.includes('shareCardEmp')) {
        fillColor = `${fillColor}`.toUpperCase()
        shareTitle = title || '我的名片'
        return {
          title: shareTitle + '，请惠存',
          path,
          imageUrl: `${imgUrl}?x-oss-process=image/format,jpg/resize,m_pad,h_400,w_500,color_${fillColor}`
        }
      }

      vLog.log('WEBVIEW onShareAppMessage COUNT_INPUT_PAGE_OR_Calculator title,path,imageUrl >>>', title, path, imageUrl)
      shareTitle = title || oTitle || '广发木棉花'
      return {
        title: shareTitle,
        path,
        imageUrl,
      }
    }

    shareTitle = title || oTitle || '广发木棉花'
    _imageUrl = `${_imageUrl || oImgUrl}?x-oss-process=image/format,jpg/${ossCrop}resize,m_pad,h_400,w_500,color_${fillColor}`
    vLog.log('WEBVIEW onShareAppMessage NORMAL shareTitle>>>', title)
    vLog.log('WEBVIEW onShareAppMessage NORMAL path>>>', path)
    vLog.log('WEBVIEW onShareAppMessage NORMAL imageUrl>>>', _imageUrl)
    if (!`${_imageUrl}`.startsWith('http')) {
      _imageUrl = SHARE_IMG_DEFAULT
    }

    return {
      title: shareTitle,
      path,
      imageUrl: _imageUrl,
    }
  }
})

/**
 * bugReview
 * https://developers.weixin.qq.com/community/develop/doc/000862c5270af059ff5d10cc05b000?highLine=web-view%2520ios15.
 * https://developers.weixin.qq.com/community/develop/doc/0008e66eadcf50b1296dcc1e65b400?highLine=webview%25208.0.17
 **/

<wxs src="../../../wxs/common.wxs" module="tools" />

<cover-view wx:if="{{!modePlat}}" class="introduce-android introduce-header-bar" style='height:{{navHeight}}px'>
  <cover-image wx:if="{{rStatus}}" bindtap="_onPressBack" src="../../../imgs/nav_icon_white.png" mode="aspectFill" class="left-icon icon-android" />
  <cover-image wx:else bindtap="_onPressBack" src="../../../imgs/nav_icon_home_white.png" mode="aspectFill" class="home-icon icon-android" />
</cover-view>

<view wx:else class="introduce-ios introduce-header-bar" style='height:{{navHeight}}px;margin-top: {{modePlat?0:14}}rpx'>
  <view class="normal-nav-back" style='height: {{model.height}}px!important; width: {{model.width}}px!important;' bindtap="_onPressBack">

    <van-icon wx:if="{{rStatus}}" size="24px" color="{{loading?'#333':'#fff'}}" class="nav-icon" name="arrow-left" />
    <cover-image wx:else src="../../../imgs/nav_icon_home_white.png" mode="aspectFill" class="home-icon" />
  </view>
</view>

<view wx:if="{{loading}}" class="loading-page" style="margin-top: {{navHeight}}px;height: {{contentHeight-navHeight}}px;">
  <van-loading type="spinner" size="40px" color="#333" />
  <view class="loading-tips">{{'加载中...'}}</view>
</view>

<scroll-view wx:else id="scroll-view" style="position:fixed;width:100%;left:0;height:{{contentHeight}}px;;" class="list-container" scroll-y="true" scroll-with-animation enable-back-to-top>
  <view class="content-block">
    <image src="{{contentInfo.header}}" mode="widthFix" class="background" />
    <view class="activity-content-view" style="height: {{headerSize}}rpx;margin-top: -{{headerSize}}rpx">
      <view class="content-box">
        {{'活动时间:' + startDate + '至' + endDate}}
      </view>
    </view>
  </view>

  <view class="content-block">
    <image src="{{contentInfo.rewards_register}}" mode="widthFix" class="background" />
    <view class="activity-content-view" style="height: {{rewards_registerSize}}rpx;margin-top: -{{rewards_registerSize}}rpx">
      <view class="content-btn-box">
        <image src="{{pageElement.btn}}" data-type="REG" mode="widthFix" bind:tap="onBtnAction" class="content-btn-img" />
      </view>
    </view>
  </view>

  <view class="content-block">
    <image src="{{contentInfo.rewards_invite}}" mode="widthFix" class="background" />
    <view class="activity-content-view" style="height: {{rewards_inviteSize}}rpx;margin-top: -{{rewards_inviteSize}}rpx">
      <view class="content-btn-box2">
        <image src="{{pageElement.btn}}" data-type="INVITE" mode="widthFix" bind:tap="onBtnAction" class="content-btn-img" />
      </view>
    </view>
  </view>

  <view class="content-block">
    <image src="{{contentInfo.rewards_share}}" mode="widthFix" class="background" />
    <view class="activity-content-view" style="height: {{rewards_shareSize}}rpx;margin-top: -{{rewards_shareSize}}rpx">
      <view class="content-btn-box3">
        <image src="{{pageElement.btn}}" data-type="SHARE" mode="widthFix" bind:tap="onBtnAction" class="content-btn-img" />
      </view>
    </view>
  </view>

  <view class="content-block">
    <image src="{{contentInfo.rule}}" mode="widthFix" class="background" />
  </view>

  <view class="content-block">
    <image src="{{contentInfo.footer}}" mode="widthFix" class="background" />
  </view>
</scroll-view>
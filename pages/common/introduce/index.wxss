.list-container{
    background-color: #f2f2f2;
}

.introduce-android{
    position: fixed;
    overflow: hidden;
    background-color: transparent;
}

.introduce-ios{
    position: absolute;
}

.introduce-header-bar{
    top: 0;
    left: 0;
    display: flex;
    flex-direction: row;
    align-items: flex-end;
    z-index: 9999;
    width: 100%;
}

.normal-nav-back{
    position: fixed;
    display: flex;
    align-items: center;
    padding: 3rpx 6rpx;
    height: 55rpx !important;
    margin: 10rpx 0 15rpx 15rpx;
    z-index: 99999;
}

.nav-icon{
    width: 20rpx;
    height: 35rpx;
    padding-right: 30rpx;
    margin-bottom: 10rpx;
}

.left-icon{
    width: 22rpx;
    height: 40rpx;
    padding-right: 20rpx;
    padding-left: 30rpx;
}

.home-icon{
    width: 40rpx;
    height: 38rpx;
    padding-right: 20rpx;
    padding-left: 30rpx;
}

.icon-android{
    margin-bottom: 20rpx;
}

.content-block{
    display: flex;
    flex-direction: column;
    width: 100vw;
    margin-top: -1px;
    align-items: center;
    flex: 1;
}

.float-tips-bar{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 100vw;
    margin-top: 160rpx;
    padding: 0 80rpx;
    /*background-color: rgba(0, 0, 0, 0.35);*/
}

.float-tips-txt{
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: 10Px;
    color: #f76343;
}

.float-tips-top{
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: 12Px;
    color: #f76343;
}

.float-box{
    padding: 0 10rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #f76343;
    border-radius: 10rpx;
    font-size: 12Px;
    color: #fff;
    margin-left: 10rpx;
}

.activity-content-view{
    display: flex;
    flex-direction: column;
    position: relative;
    width: 100vw;
    height: 50vw;
    margin-top: -50vw;
    /*background-color: rgba(0, 0, 0, 0.35);*/
    z-index: 999;
}

.content-box{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    margin-top: 46px;
    font-size: 14Px;
    color: #fff;
}

.content-btn-box{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;
    flex: 1;
    padding-bottom: 70rpx;
}
.content-btn-box2{
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  flex: 1;
  padding-bottom: 100rpx;
}

.content-btn-box3{
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  flex: 1;
  padding-bottom: 100rpx;
}

.content-btn-img{
    width: 150px;
    height: 25px;
}

.activity-date-block{
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: absolute;
    top: 0;
    left: 0;
    background-color: #fff;
    width: 92vw;
    height: 25vw;
    margin-left: 4vw;
    margin-top: 20vw;
    border-radius: 20rpx;
    box-shadow: 0 10px 10px 0 rgba(3, 3, 3, 0.15);
    z-index: 999;
    padding: 0 40rpx;

    color: #e75949ff;
}

.date-header{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
}

.line-view{
    margin: 14rpx 0;
}

.background{
    width: 100%;
}

._title_bar{
    margin-left: 4.5vw;
    margin-right: 4vw;
    width: 91.5vw;
}

.share-float-block{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-around;
    width: 91.5vw;
    margin-left: 4.5vw;
    height: 120rpx;
    margin-top: -150rpx;
    /*background-color: rgba(0, 0, 0, 0.45);*/
    z-index: 999;
}

.float-content{
    display: flex;
    flex-direction: column;
    height: 120rpx;
    align-items: center;
    justify-content: center;
    max-width: 40vw;

    font-size: 22Px;
    font-weight: bold;
    color: #ffffff;
}

.death-line-time{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: 91.5vw;
    height: 120rpx;
    margin-top: 10rpx;
    /*background-color: rgba(0, 0, 0, 0.45);*/
    margin-left: 4.5vw;
    z-index: 999;
}

.alert-icon{
    width: 32rpx;
    height: 32rpx;
}

.alert-tips{
    font-size: 16Px;
    color: #FF4800;
    margin-left: 10rpx;
    margin-right: 10rpx;
}

.time-block{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 60rpx;
    height: 60rpx;
    border-radius: 4rpx;
    background-color: #e75949ff;
    margin: 0 8rpx;
    color: #FFFFFF;
    font-size: 16Px;
}

.time-code{
    color: #e75949ff;
    font-size: 20Px;
    margin: 0 2rpx;
}

.death-line-block{
    display: flex;
    flex-direction: row;
    align-items: center;
}

.content-tips{
    width: 100%;
    max-width: 86vw;
    align-self: center;
    margin-top: 20rpx;
}

.content-btn{
    width: 100%;
    max-width: 80vw;
    align-self: center;
    margin-top: 20rpx;
}

.explain-box{
    display: flex;
    flex-direction: column;
    margin-top: 20rpx;
}

.explain-tips{
    align-self: center;
    font-size: 14Px;
    line-height: 28Px;
    color: #fff;
    width: 86vw;
}

.loading-page{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: absolute;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100%;
    z-index: 9999;
    background-color: #fff;
    /*background-color: rgba(0, 0, 0, 0.55);*/
}

.loading-tips{
    margin-top: 10px;
}

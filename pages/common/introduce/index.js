import {
  global,
  storage,
  util,
  systemtInfo,
  enums,
  qs,
  interaction,
  breakIn,
  vLog,
  eventName
} from "../../../common/index.js";

import {
  titleHeightPx,
  platform
} from '../../../common/const/systeminfo.js'

import {
  getFirstPath,
  getUserRole,
  getSystemInfo,
  getUser,
  getCurrRoleType,
  setSystemInfo,
  getUserLogin,
  getUserId,
  getToken,
  getOpenId,
  getUnionID,
  getWechatInfoId,
  setPreviousPath
} from "../../../common/utils/userStorage";

import {
  getUCSystemApiConfig,
  checkDisqualified
} from "../../../common/nb/home";

const {
  EnterSource,
  GLOBAL_START_PATH,
} = enums

const {
  SEND_REGISTER_OPTIONS,
} = eventName

const dayJs = require('dayjs')

const app = getApp()
const {screenHeight} = systemtInfo
const {rpx2px, isEmptyObject, doJSON_PARSE} = util
const queryImgUrlInfo = ['header', 'rewards_register', 'rewards_invite', 'rewards_share']

Page({
  data: {
    modePlat: (platform === "ios"),
    contentHeight: rpx2px(screenHeight),
    navHeight: titleHeightPx,
    fromTab: '',
    rStatus: false,
    loading: true,

    headerSize: 1074,
    rewards_inviteSize: 570,
    rewards_registerSize: 904,
    rewards_shareSize: 802,

    contentInfo: {},
    pageElement: {},
    activityInfo: {},

    startDate: '',
    endDate: '',
    explain: [],
    model: wx.getMenuButtonBoundingClientRect(),

    userRole: null,
    openByInline: false,
    options: {},
    sysInfo: {},
    wInfo: {},
    showModal: false
  },

  onLoad(options) {
    vLog.log('INTRODUCE >>>', options)
    const {fromTab = 'MINE', routerStatus = 'PAGE'} = options || {}

    const sysInfo = getSystemInfo()
    const hasLogin = getUserLogin()
    const userRole = getUserRole()
    let model = wx.getMenuButtonBoundingClientRect()

    this.setData({
      options,
      userRole,
      wInfo: getUser(),
      sysInfo,
      hasLogin,
      openByInline: storage.getStorage(global.STORAGE_GLOBAL_INLINE_PAGE) || '',
      model,
      fromTab,
      rStatus: routerStatus === 'PAGE',
      activityInfo: sysInfo?.staff?.clientParams?.activityConfig || {}
    }, () => this.initActivity())

    breakIn({name: 'initVersionInfo'})
    this.onProbe()
  },

  onProbe() {
    let _ProbeTimer = setTimeout(() => {
      clearTimeout(_ProbeTimer)

      let userRole = getUserRole()
      if (typeof userRole === 'string'){
        userRole = ''
      }

      this.setData({
        userRole,
        loading: false,
      })
    }, 1500)
  },

  async initActivity() {
    vLog.log('INTRODUCE initActivity this.data >>>', this.data)
    let { activityInfo, hasLogin } = this.data
    if (hasLogin) {
      const { data = false } = await checkDisqualified()
      this.data.showModal = data
    }
    if (!activityInfo || isEmptyObject(activityInfo)){
      let customerType = getCurrRoleType()
      interaction.showLoading('加载中...')
      const {success, data, code, msg} = await getUCSystemApiConfig({customerType})
      interaction.hideLoading()
      vLog.log('INTRODUCE initActivity getUCSystemApiConfig >>>', success, data, code, msg)
      if (success && code === 0){
        let sysInfo = {...data}
        for (const [key, value] of Object.entries(sysInfo)) {
          if (Array.isArray(value)){
            sysInfo[key] = [].concat(value)
          } else if (value instanceof Object){
            sysInfo[key] = {...value}
          } else {
            sysInfo[key] = doJSON_PARSE(value || '{}' + '')
          }
        }
        setSystemInfo(sysInfo)
        activityInfo = sysInfo?.staff?.clientParams?.activityConfig || {}
      }
    }

    let {
      startDate = '',
      endDate = '',
      welcome = {}
    } = activityInfo || {}

    const {pageImg = {}, pageElement = {}} = welcome || {}

    endDate = dayJs(endDate).format('YYYY-MM-DD HH:mm:ss')
    startDate = dayJs(startDate).format('YYYY-MM-DD HH:mm:ss')

    const urlPool = {}
    let heightPool = {}
    let explain = []
    if (Object.keys(pageImg).length){
      for (const [key, value] of Object.entries(pageImg)) {
        if (queryImgUrlInfo.includes(`${key}`)){
          urlPool[key] = `${value}?x-oss-process=image/info`
        }
      }
    }

    for (const item of Object.entries(urlPool)) {
      let [key, value] = item
      await wx.request({
        url: `${value}`,
        method: 'GET',
        success: (res) => {
          const {
            data: {
              ImageHeight: {
                value: iHeight
              }
            }
          } = res || {}
          key = `${key}Size`
          heightPool[key] = iHeight * 1
        },
        fail: (err) => {
          vLog.error(`INTRODUCE wx.request ${value} err >>> `, err).report()
        }
      })
    }
    vLog.log('INTRODUCE initActivity urlPool,heightPool >>>', urlPool, heightPool)

    startDate = dayJs(startDate).format('YYYY年MM月DD日')
    endDate = dayJs(endDate).format('YYYY年MM月DD日')

    this.setData({
      loading: false,
      contentInfo: {...pageImg},
      pageElement,
      startDate,
      endDate,
      explain,
      ...heightPool,
    })
  },

  onBtnAction(e = {}) {
    const {
      dataset: {
        type = ''
      }
    } = e.currentTarget || {};
    const { options, showModal, hasLogin } = this.data
    vLog.log('INTRODUCE onBtnAction type,e >>>>', type, e)
    // 游客登录
    if (!hasLogin) {
      return this.showLoginModal()
    }

    if (!showModal) {
      return wx.showModal({
        title: '仅限理财经理参与',
        showCancel: false,
        confirmText: "确定",
      })
    }
    
    switch (type) {
      case "REG": {
        const params = {
          pageType: 'advLotteryActivityPage',
          ...options,
        }
        return wx.navigateTo({
          url: `/pages/common/h5/index?${qs.stringify(params)}`
        })
      }

      case "INVITE":
      case "SHARE": {
        const params = {
          pageType: 'advLotteryActivityPage',
          ...options,
        }
        return wx.navigateTo({
          url: `/pages/loginAndRegist/activity/activity?${qs.stringify(params)}`
        })
      }

      default:
        break
    }
  },

    /**
   * 登录弹窗
   */
  showLoginModal() {
    const { fromTab, options } = this.data || {}
    let params = {
      token: getToken(),
      openid: getOpenId(),
      unionid: getUnionID(),
      wechatInfoId: getWechatInfoId(),
      pageType: 'AppAdvActivity',
    }

    params = {
      ...options,
      ...params,
      TAG: 'AppAdvActivity',
      fromTab,
      routerStatus: 'SHARE',
      shareFrom: EnterSource.ACTIVITY,
      targetPath: '/pages/common/introduce/index',
    }

    return wx.showModal({
      title: '请先登录,再参加活动',
      content: '',
      confirmText: '立即登录',
      success: res => {
        if (res.confirm){
          let pagePath = getCurrentPages().pop();
          vLog.log('ACTIVITY  pagePath >>>', pagePath?.route)
          setPreviousPath(pagePath?.route)
          return wx.navigateTo({
            url: '/pages/user/login/index',
            success(res) {
              vLog.log('ACTIVITY go login event >>>', params)
              res.eventChannel.emit(SEND_REGISTER_OPTIONS, params)
            }
          })
        }
      }
    })
  },

  _onPressBack() {
    vLog.log('INTRODUCE _onPressBack this.data >>>', this.data)
    const {rStatus} = this.data
    if (rStatus){
      return wx.navigateBack({})
    } else {
      let _fPath = getFirstPath()
      return wx.switchTab({
        url: `${_fPath}`
      })
    }
  },

  onShareAppMessage() {
    const {sysInfo, fromTab} = this.data
    let imageUrl = sysInfo?.staff?.clientParams?.coverList?.welcome || ''
    getApp().sensors.track('MPShare', {
      share_type: "微信好友",
    })
    const sParams = {
      routerPage: `/pages/common/introduce/index`,
      shareFrom: EnterSource.WELCOME,
      routerStatus: 'SHARE',
      fromTab,
      faId: getUserId()
    }
    let path = `${GLOBAL_START_PATH}?${qs.stringify(sParams)}`

    return {
      title: '三重好礼等你来拿',
      path,
      imageUrl
    }
  }
});

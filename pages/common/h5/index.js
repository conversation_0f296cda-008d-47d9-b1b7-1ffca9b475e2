// pages/common/h5.js
import {
  qs,
  util,
  wbs,
  enums,
  vLog
} from "../../../common/index.js";

import {
  getUnionID,
  getOpenId,
  getToken,
  getWechatInfoId,
  setWebviewMoment,
  setAppHoldStatus,
  setSpringboardStatus,
  getSystemInfo,
  getUser,
  setUserRole,
  getCurrRoleType,
  setUserLogin,
  getUserId,
} from "../../../common/utils/userStorage";

const {
  EnterSource,
  GLOBAL_START_PATH
} = enums

const {
  isEmptyObject,
} = util

const ERROR_STATUS = ['null', 'undefined', '', '[object Object]']

const fpPlugin = requirePlugin('tdfp-plugin')
const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    url: '',
    pageType: '',
    shareData: {},

    fillColor: 'FFFFFF',
    hideHomeNav: 0,
    banShare: 0,

    wxInfo: {},
    sysInfo: {},
    options: {},
    customerType: '',
    blackBox: '',
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(opts = {}) {
    let fragment = new fpPlugin.FMAgent(app.globalData._fmOpt)
    if (fragment) { this.initBlackbox(fragment) }
    vLog.log('H5 opts,qs.parse(opts) >>>>', opts, qs.parse(opts))
    let options = {...qs.parse(opts)}
    for (const [key, value] of Object.entries(options)) {
      const newKey = this.decodeUrl(key)
      const newValue = this.decodeUrl(value)
      options[newKey] = newValue
    }

    const sysInfo = getSystemInfo()
    const wxInfo = getUser()
    const customerType = getCurrRoleType()
    this.setData({
      sysInfo,
      wxInfo,
      options,
      customerType,
      banShare: options?.banShare && Number(options?.banShare) || 0,
      hideHomeNav: options?.hideHomeNav && Number(options?.hideHomeNav) || 0,
      fillColor: options?.fillColor || 'FFFFFF',
    }, () => this.initWebOptions(options))
  },

  initBlackbox(fragment) {
    let that = this
    fragment && fragment.getInfo({
      page: that,
      mode: 'plugin',
      noClipboard: true,
      openid: getOpenId(),
      unionid: getUnionID(),
      success: function(blackBox) {
        that.data.blackBox = blackBox
      },
      fail: function(err) {
        vLog.error('REGISTER initBlackbox failed >>>', err).report()
      }
    })
  },

  onShow() {
    wx.hideShareMenu({
      menus: ['shareTimeline']
    })
  },

  initWebOptions(options = {}) {
    vLog.log('H5 initWebOptions options >>>', options)
    if (isEmptyObject(options)){
      return
    }

    let baseInfo = {
      token: getToken(),
      openid: getOpenId(),
      unionid: getUnionID(),
      wechatInfoId: getWechatInfoId(),
      blackBox: this.data.blackBox,
      faId: options?.faId || ""
    }
    let bParams = {}
    for (let [bKey, bValue] of Object.entries(baseInfo)) {
      if (!ERROR_STATUS.includes(`${bValue}`)){
        bParams[`${bKey}`] = bValue
      }
    }

    const {pageType} = options || {}
    const _baseFromUrl = qs.stringify(baseInfo)
    vLog.log('H5 _baseFromUrl @@@@ >>>>', _baseFromUrl)
    let _resUrl = ''
    switch (pageType) {
      // 抽奖
      case 'advLotteryActivityPage': //端内
        _resUrl = `${wbs.gfH5}/share/${pageType}?${_baseFromUrl}`
        break

      case 'advLotteryActivityPageOut': //分享后的
        _resUrl = `${wbs.gfH5}/share/advLotteryActivityPage?${_baseFromUrl}`
        break

      default:
        break
    }
    vLog.log('H5 _resUrl before @@@@ >>>>', _resUrl)

    this.setData({
      url: _resUrl,
      pageType,
    })
  },

  decodeUrl(url = '') {
    let _url = decodeURIComponent(url)
    if (`${_url}`.startsWith('%') || `${_url}`.startsWith('http%') || `${_url}`.startsWith('https%')){
      _url = this.decodeUrl(_url)
    }
    return _url
  },

  onSaveExitState() {
    vLog.log('H5 onSaveExitState')
    setWebviewMoment(false)
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {
    vLog.log('H5 onHide')
    setWebviewMoment(true)
    setAppHoldStatus(true)
    setSpringboardStatus(false)
    if (getToken()){
      setUserRole(0)
      setUserLogin(true)
    }
  },

  // 当 webview 被销毁时，该方法被触发
  handlePostMessage(event={}) {
    vLog.log('H5 handlePostMessage event >>>', event)
    vLog.log('H5  handlePostMessage this.data >>>', this.data)
    let {fillColor: fColor} = this.data
    const {data} = event?.detail || {};

    if (data && Array.isArray(data)){
      let len = data.length && data.length - 1 || 0
      let shareData = data[len] || {}
      if (Object.keys(shareData).length > 10){
        len = len === 0 ? len : len - 1
        shareData = data[len]
      }

      let {fillColor = ''} = shareData || {}
      if (fillColor){
        fColor = fillColor
      }
      vLog.log('H5 handlePostMessage before shareData >>>', shareData)
      this.setData({
        fillColor: `${fColor}`.toUpperCase(),
        shareData,
        url: this.data.url
      })
    }
  },

  onShareAppMessage() {
    const {sysInfo} = this.data
    let imageUrl = sysInfo?.staff?.clientParams?.coverList?.lottery || ''
    let params = qs.stringify({pageType: 'advLotteryActivityPageOut'})
    getApp().sensors.track('MPShare', {
      share_type: "微信好友",
    })
    const sParams = {
      routerPage: `/pages/common/h5/index`,
      shareFrom: EnterSource.H5,
      params,
      faId: getUserId()
    }
    let path = `${GLOBAL_START_PATH}?${qs.stringify(sParams)}`

    return {
      title: '注册赢取好礼',
      path,
      imageUrl
    }
  }
})

import {
  eventName,
  global,
  interaction,
  storage,
  vLog
} from '../../../common/index.js'

const {platform} = wx.getSystemInfoSync()
const {
  SET_PAGE_BACK
} = eventName

const FILE_TYPES = ["doc", "xls", "ppt", "pdf", "docx", "xlsx", "pptx"]
const app = getApp()

Page({
  data: {
    name: '',
    ios: platform === "ios"
  },

  onLoad(options = {}) {
    const {ios} = this.data
    vLog.info('OPEN_FILE options >>', JSON.stringify(options)).report()
    const {url = '', name = ''} = options || {}
    wx.showLoading({
      title: '文件加载中...',
      mask: true
    })

    getApp().event.on(SET_PAGE_BACK, () => {
      let openFileKey = storage.getStorage(global.STORAGE_GLOBAL_OPEN_FILE_START) || 0
      vLog.log('OPEN_FILE SET_PAGE_BACK ios && openFileKey >>>', ios && openFileKey)
      if (ios && openFileKey){
        storage.setStorage(global.STORAGE_GLOBAL_OPEN_FILE_START, 0)
        return wx.navigateBack({delta: 1})
      }
    });

    if (url && ios){
      const fileUrl = decodeURIComponent(url)
      vLog.info('OPEN_FILE fileUrl url && ios >>>', fileUrl).report()

      wx.downloadFile({
        url: fileUrl,
        filePath: `${wx.env.USER_DATA_PATH}/${name}`,
        success: function(res) {
          getApp().globalData.filePath = `${wx.env.USER_DATA_PATH}/${name}`
          vLog.log('OPEN_FILE downloadFile success  res >>>', res).report()
          const {filePath = ''} = res || {}
          const _fFiles = `${filePath}`.split('.')
          let _fType = _fFiles.pop()
          vLog.info('OPEN_FILE fType >>>>', _fType).report()
          if (!FILE_TYPES.includes(_fType)){
            interaction.showToast('该文件类型无法下载！')
            let failTimer = setTimeout(() => {
              clearTimeout(failTimer)
              wx.navigateBack()
            }, 1500)
          } else {
            wx.openDocument({
              filePath: filePath,
              showMenu: true,
              fileType: _fType,
              success: function(result) {
                vLog.info('OPEN_FILE openDocument success  result >>>', result).report()
              },
              fail: function(e) {
                vLog.error('OPEN_FILE openDocument fail  e >>>', e).report()
                interaction.showToast('文件下载失败！')
                let failTimer = setTimeout(() => {
                  clearTimeout(failTimer)
                  wx.navigateBack()
                }, 1500)
              }
            })
          }
        },
        fail: function(e) {
          vLog.error('OPEN_FILE downloadFile fail  e >>>', e).report()
          interaction.showToast('文件下载失败！')
          let failTimer = setTimeout(() => {
            clearTimeout(failTimer)
            wx.navigateBack()
          }, 1500)
        },
      })
    } else {
      const fileUrl = decodeURIComponent(url)
      vLog.error('OPEN_FILE fileUrl android >>>', fileUrl).report()
      return this.onOpenFile(url, name)
    }
  },

  onOpenFile(url = '', name = '') {
    const filePath = wx.env.USER_DATA_PATH  + `/${name}`
    vLog.info('OPEN_FILE filePath >>>', filePath).report()
    wx.downloadFile({
      url: decodeURIComponent(url),
      filePath: filePath,
      success: function(res) {
        getApp().globalData.filePath = `${wx.env.USER_DATA_PATH}/${name}`
        vLog.info('OPEN_FILE res >>>', res).report()

        const {filePath = ''} = res || {}
        wx.navigateBack()
        let timer = setTimeout(() => {
          clearTimeout(timer)
          wx.openDocument({
            filePath,
            showMenu: true,
            success: function(result) {
              vLog.info('OPEN_FILE result >>>', result).report()
            },
            fail: function(e) {
              vLog.info('OPEN_FILE wx.openDocument fail >>>', e).report()
              interaction.showToast('文件打开失败！')
              let failTimer = setTimeout(() => {
                clearTimeout(failTimer)
                wx.navigateBack()
              }, 1500)
            },
            complete: function() {
              wx.hideLoading()
            }
          })
        }, 500)
      },
      fail: function(e={}) {
        interaction.showToast('文件下载失败！')
        vLog.error('OPEN_FILE wx.downloadFile fail >>>', e).report()
        let failTimer = setTimeout(() => {
          clearTimeout(failTimer)
          wx.navigateBack()
        }, 1500)
      },
      complete: function() {
        wx.hideLoading()
      }
    })
  },

  onHide() {
    vLog.info('OPEN_FILE onHide').report()
    const {ios} = this.data
    if (ios){
      wx.hideLoading()
    }
  },

  onUnload() {
    vLog.info('OPEN_FILE onUnload').report()
    const {ios} = this.data
    if (ios){
      wx.hideLoading()
    }

    let timer = setTimeout(() => {
      clearTimeout(timer)
    }, 500)
  }
})

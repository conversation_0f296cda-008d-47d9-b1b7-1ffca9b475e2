import {
  systemtInfo,
  global,
  util,
  enums,
  storage,
  eventName,
  qs,
  interaction,
  recordClue,
  vLog
} from '../../../common/index'

import {
  getNewsList,
  getMulitList,
  getPosterList,
  getRecommendList,
  getHomeMarketPlanList,
  getArticleClassify,
  getPosterClassify,
  getMulitClassify,
  getMarketPlanClassify
} from "../../../common/nb/home";

import {
  getCategoryListByIds,
  getLiveList,
} from "../../../common/network/api";

import {
  getUserRole,
  getUser,
  getUserLogin,
} from "../../../common/utils/userStorage";

const { platform } = wx.getSystemInfoSync()

const {
  screenHeight,
  footHeight,
} = systemtInfo

const {
  reportLeave,
  reportReOpen
} = recordClue

const {
  SEND_EVENT_TO_SHARE_LIST,
  SEND_EVENT_TO_POLYMERS,
} = eventName

const {
  RealmType,
} = enums

const {
  rpx2px,
  doUrlDeCode,
  isEmptyObject,
  encodeFormObject,
  transformObject
} = util;


const app = getApp()
const dayJs = require('dayjs')

Page({
  data: {
    content: {},
    params: {},
    wInfo: {},
    modePlat: platform === "ios",
    contentHeight: rpx2px(screenHeight - footHeight),

    aggPage: false,
    refreshing: false,
    nomore: false,
    loadmoring: false,
    hasLogin: false,

    currList: [],
    page: 0,
    pageSize: 21,
    currTotalPages: 1,

    type: RealmType.ARTICLE,
    banner: '',
    orgId: '',
    targetTabId: '',
    fUID: "",
    OID: '',
    OType: '',

    title: '',
    placeholder: '请输入关键词',
    showSearch: true,
    hasHide: false,
  },

  onLoad(options = {}) {
    storage.setStorage(global.STORAGE_GLOBAL_PREVIOUS_PARAMS, options)
    vLog.log('SHARE_LIST onLoad options >>>>', qs.parse(options))
    let opts = {}

    options = encodeFormObject(options)
    console.log('%c SHARE_LIST onLoad encodeFormObject options:', 'color: #00ff00; font-size: 14px;', options)

    const entriesObject = Object.entries(transformObject(options))
    for (const [key, value] of entriesObject) {
      if (typeof value !== 'object') {
        console.log('SHARE_LIST onLoad typeof value !== object:', doUrlDeCode(value + ''))
        opts[key] = doUrlDeCode(value + '')
      } else {
        let temp = {}
        console.log('SHARE_LIST onLoad typeof value,value == object:', value, Object.assign(value))
        const entriesValue = Object.entries(value)
        for (const [tKey, tValue] of entriesValue) {
          temp[tKey] = doUrlDeCode(tValue + '')
        }
        opts[key] = { ...temp }
      }
    }
    vLog.log('SHARE_LIST onLoad opts >>>>', opts)
    let {
      id = '',
      categoryId = '',
      categoryIds = '',
      listType = '',

      name: nTitle = '',
      content = {},
      type: oType,
      name = '',
      avatar = '',
      targetTabId = '',

      userId = '',
      orgId = '',
      orgType = '',
      currCoverUrl = '',
    } = opts || {}

    let {
      aggregationPage = '',
      categoryId: unionCId = '',
      id: unionId = ''
    } = content || {}

    let type = oType || ''
    let title = name || ''
    let aggPage = false
    let fUID = userId || ''
    let OID = orgId || ''
    let OType = orgType || ''

    if (listType === 'UNION' || (aggregationPage && aggregationPage == 'true')) {
      id = unionId || unionCId || ''
      aggPage = true
    } else {
      id = targetTabId || ''
    }

    const hasLogin = getUserLogin()
    app.globalData.emitter.on(SEND_EVENT_TO_SHARE_LIST, (e) => {
      vLog.log('SHARE_LIST SEND_EVENT_TO_SHARE_LIST e >>>>  ', e)
      if (!isEmptyObject(e)) {
        return this.onHandleAction(e)
      }
    })

    this.setData({
      name,
      type,
      content,
      fUID,
      OID,
      OType,
      aggPage,
      hasLogin,
      params: opts,
      wInfo: getUser(),
      title: nTitle || title,
      banner: avatar,
      currCoverUrl,
      targetTabId: id || categoryId || categoryIds,
    }, () => this.initShareList(type, this.data.targetTabId))
    this.onProbe()
  },

  onProbe() {
    let _ProbeTimer = setTimeout(() => {
      clearTimeout(_ProbeTimer)

      let userRole = getUserRole()
      if (typeof userRole === 'string') {
        userRole = ''
      }

      this.setData({
        userRole
      })
    }, 1500)
  },

  async initShareList(type, id) {
    interaction.showLoading('加载中...')
    id = doUrlDeCode(id)
    vLog.log('SHARE_LIST getDetail >>>', type, id)
    const {
      hasLogin,
      content,
      wInfo,
      page = 0,
      currTotalPages = 1,
      pageSize = 20,
    } = this.data

    let params = {
      page,
      pageSize
    }

    let categoryParams = {}
    if (!page) {
      this.setData({
        currList: []
      });
    }

    //资讯
    if (type === RealmType.ARTICLE) {
      categoryParams = {
        categoryIds: id
      }
      const { success: cSucc, param: cRes } = await getArticleClassify(categoryParams)
      interaction.hideLoading()
      if (!cSucc || !cRes || isEmptyObject(cRes) || !cRes.length) {
        this.setData({
          refreshing: false,
          nomore: true,
          loadmoring: false,
          currTotalPages: 1,
          currList: []
        })
        return
      }

      params.localCategoryId = id
      const { msg, param, success } = await getNewsList(params)
      vLog.log('SHARE_LIST getDetail ARTICLE >>>', msg, param, success)
      this.getData(msg, param?.content, success, true)
    }

    //直播
    else if (type === RealmType.LIVE) {
      const { firstOrgId = '' } = wInfo || {}
      params = {
        start: page,
        limit: pageSize,
        wbsCategoryId: id,
        channelIds: [`${hasLogin ? firstOrgId : ''}`]
      }
      if (!params.start) {
        params.start = 1
      }
      categoryParams = {
        ids: [id],
        channelIds: [`${firstOrgId}`]
      }
      if (!firstOrgId) {
        delete params.channelIds
        delete categoryParams.channelIds
      }
      const { code: cCode, value: cRes } = await getCategoryListByIds(categoryParams)
      interaction.hideLoading()
      if (cCode !== 0 || !cRes || isEmptyObject(cRes) || !cRes.length) {
        this.setData({
          refreshing: false,
          nomore: true,
          loadmoring: false,
          currTotalPages: 1,
          currList: []
        })
        return
      }

      const { msg, rows, code } = await getLiveList(params)
      vLog.log('SHARE_LIST getDetail LIVE >>>', msg, rows, code)
      this.getData(msg, rows, code === 0, false)
    }

    //海报
    else if (type === RealmType.POSTER) {
      params.categoryId = id
      categoryParams = {
        categoryIds: id
      }
      const { code: cCode, param: cRes } = await getPosterClassify(categoryParams)
      interaction.hideLoading()
      vLog.log(`SHARE_LIST ${type} cRes >>>>`, cRes)
      if (cCode !== 0 || !cRes || isEmptyObject(cRes) || !cRes.length) {
        this.setData({
          refreshing: false,
          nomore: true,
          loadmoring: false,
          currTotalPages: 1,
          currList: []
        })
        return
      }

      const { msg, param, success } = await getPosterList(params)
      vLog.log('SHARE_LIST getDetail POSTER >>>', msg, param, success)
      this.getData(msg, param?.content, success, true)
    }

    //音视频
    else if (type === RealmType.MULTI_COURSE) {
      params.categoryId = id
      categoryParams = {
        categoryId: id
      }
      const { success: cSucc, data: cRes } = await getMulitClassify(categoryParams)
      interaction.hideLoading()
      vLog.log(`SHARE_LIST ${type} cRes >>>>`, cRes)
      if (!cSucc || !cRes || isEmptyObject(cRes) || !cRes.length) {
        this.setData({
          refreshing: false,
          nomore: true,
          loadmoring: false,
          currTotalPages: 1,
          currList: []
        })
        return
      }

      const { msg, data, success } = await getMulitList(params)
      vLog.log('SHARE_LIST getDetail MULTI_COURSE >>>', msg, data, success)
      this.getData(msg, data?.content, success, true)
    }

    //营销方案
    else if (type === RealmType.MARKET_PLAN) {
      params.categoryId = id
      if (content.type === "MARKET_PLAN_EXCLUSIVE_CATEGORY" && hasLogin) {
        const { firstOrgId = '' } = wInfo || {}
        params.orgId = firstOrgId
        this.setData({ orgId: firstOrgId })
      }
      categoryParams = {
        categoryIds: id,
      }
      const { code: cCode, param: cRes } = await getMarketPlanClassify(categoryParams)
      interaction.hideLoading()
      const proRes = await getMarketPlanClassify(categoryParams)
      vLog.log(`SHARE_LIST ${type} proRes >>>>`, proRes)
      if (cCode !== 0 || !cRes || isEmptyObject(cRes) || !cRes.length) {
        this.setData({
          refreshing: false,
          nomore: true,
          loadmoring: false,
          currTotalPages: 1,
          currList: []
        })
        return
      }

      const api = hasLogin ? getRecommendList : getHomeMarketPlanList
      if (currTotalPages <= page) {
        this.setData({
          refreshing: false,
          nomore: true,
          loadmoring: false
        })
        return
      }

      vLog.log('SHARE_LIST getDetail MARKET_PLAN params,api, >>>', params, api)
      const { msg, param, success } = await api(params)
      vLog.log('SHARE_LIST MARKET_PLAN msg, rows, code >>>', msg, param, success)
      this.getData(msg, param?.content, success, '', param?.totalPages)
    }
  },

  getData(msg, _currList, success, showTime = '', currTotalPages = 1) {
    interaction.hideLoading()
    let { pageSize, page, currList } = this.data
    this.setData({
      refreshing: false,
      loadmoring: false,
      currTotalPages,
    });

    if (!success) {
      return interaction.showToast(msg || '')
    }

    if (_currList && _currList.length < pageSize) {
      this.setData({
        nomore: true
      });
    }
    if (showTime) {
      _currList = _currList.map(item => ({
        ...item,
        timeCreatedStr: dayJs(item?.timeCreated || new Date()).format('YYYY-MM-DD'),
      }))
    }
    if (page > 0) {
      currList = currList.concat(_currList);
      this.setData({
        currList
      });
    } else {
      this.setData({
        currList: _currList
      });
    }
  },

  doSearch() {
    const { type, content, orgId, OID, targetTabId, currList = [] } = this.data
    const { type: nType = '', id = '', categoryId = '' } = content || {}
    let isNeedOrgid = (nType === "MARKET_PLAN_EXCLUSIVE_CATEGORY")
    let cId = targetTabId || categoryId || id || ''
    let canShowRes = !!currList.length

    const searchParams = {
      categoryId: cId,
      type,
      orgId: OID || orgId,
      isNeedOrgid,
      fromShareList: true,
      fromPage: 'SHARE_LIST',
      canShowRes
    }

    wx.navigateTo({
      url: `/packages-common/pages/common/search/search?${qs.stringify(searchParams)}`,
    })
  },

  async onHandleAction(e = {}) {
    if (!isEmptyObject(e)) {
      const {
        dataset: {
          item = {},
          type = ''
        }
      } = e.currentTarget || {}
      if (type) {
        const passParams = {
          ...item,
          action: `action://share/${type}`
        }
        return app.globalData.emitter.emit(SEND_EVENT_TO_POLYMERS, { detail: passParams })
      }
      return app.globalData.emitter.emit(SEND_EVENT_TO_POLYMERS, { detail: { ...e.detail } })
    }
  },

  handlePullDownRefresh() {
    const { type, currList = [], targetTabId = '' } = this.data
    if (!currList.length) {
      this.setData({ refreshing: false })
      return;
    }
    this.data.page = 0;
    this.setData({
      nomore: false,
      currTotalPages: 1,
      currList: []
    }, () => {
      return this.initShareList(type, targetTabId)
    })
  },

  //加载更多
  handleLoadMore() {
    const { targetTabId = '', currList = [], type, loadmoring } = this.data
    vLog.log('SHARE_LIST handleLoadMore >>', this.data)
    if (currList.length === 0 || loadmoring) {
      this.setData({ refreshing: false })
      return;
    }
    this.data.page++;
    this.setData({
      nomore: false,
      loadmoring: true
    }, () => {
      return this.initShareList(type, targetTabId)
    });
  },

  onHide() {
    vLog.log('SHARE_LIST onHide data >>>', this.data)
    this.setData({
      hasHide: true
    }, () => reportLeave())
  },

  onShow() {
    vLog.log('SHARE_LIST onShow data >>>', this.data)
    const { hasHide } = this.data || {}
    if (hasHide) {
      vLog.log('SHARE_LIST onShow hasHide >>>', hasHide)
      return reportReOpen()
    }
  },

  onReady() {
    vLog.log('SHARE_LIST onReady data >>>', this.data)
    storage.setStorage(global.STORAGE_GLOBAL_REPORT_STATUS, true)
  },

  onShareAppMessage() {
    const { name, currCoverUrl } = this.data
    return {
      title: name,
      imageUrl: currCoverUrl
    }
  },

  onUnload() {
    vLog.log('SHARE_LIST onUnload >>>>')
    app.globalData.emitter.off(SEND_EVENT_TO_SHARE_LIST)
    reportLeave()
  }
});

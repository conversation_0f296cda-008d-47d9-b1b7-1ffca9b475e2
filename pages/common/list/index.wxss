.list-container{
    padding-bottom: 25vh;
    /* background-color: red; */
}
.content-box {
    margin: 0 24rpx;
    background-color: #fff;
    border-radius: 8rpx;
}

.display-none{
    display: none;
}

.text-black{
    font-size: 16Px;
    color: #333;
    font-weight: 500;
    font-family: PingFangSC-Medium;
}

.content-list{
    display: flex;
    flex-direction: column;
    /* width: 100%; */
    background-color: #fff;
    border-radius: 10px;
    margin: 0 14px 20px;
    box-shadow: 0 4px 10px 10px #F7F7F7;
    /* background-color: coral; */
}
.content-poster-list{
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    /*margin: 0 0 0 14px;*/
    flex-wrap: wrap;
}
.content-media-list{
    display: flex;
    flex-direction: column;
    background-color: #fff;
    border-radius: 10px;
    margin: 0 14px 14px;
    overflow: hidden;
}
.search-block{
    display: flex;
    flex-direction: row;
    align-items: center;
    background-color: #fff;
    max-height: 15vw;
    padding: 12rpx 24rpx;
    border-radius: 8vw;
    border: 1px solid #EEEEEE;
    margin: 0 24rpx 14px;
}

.search-content{
    display: flex;
    flex-direction: row;
    /*justify-content: center;*/
    align-items: center;
    flex: 1;
}

.search-content-tips{
    font-size: 14Px;
    color: #999999;
    font-weight: 400;
    margin-right: 10rpx;
    font-family: PingFangSC-Regular;
}

.search-icon{
    width: 40rpx;
    height: 40rpx;
    margin: 0 6rpx;
}

.banner-img{
    width: 92%;
    margin: 0 14px 14px;
    border-radius: 10rpx;
}
.empty-class{
    width: 100%;
    height: 90vh;
    padding-top: 200px;
    background-color: #fff;
}


<wxs src="../../../wxs/common.wxs" module="tools"/>
<nb-page
    showNavBar="{{true}}"
    navShowBack="{{false}}"
    showHomeIcon="{{true}}"
    navTitle="{{title||'列表'}}"
    showTabBar="{{false}}">

  <x-scroll-view
      enableRefresh="{{true}}"
      class="list-container"
      style="margin-bottom: 25vh;"
      elementHeight="{{contentHeight}}"
      refreshing="{{refreshing}}"
      loadmoring="{{loadmoring}}"
      nomore="{{nomore}}"
      enableLoadMore="{{true}}"
      resetting="{{true}}"
      scrollTop='{{scrollTop}}'
      refreshTopDistance="{{1}}"
      hasTabBarInfo="{{false}}"
      bindpulluploadmore="handleLoadMore"
      bindpulldownrefresh="handlePullDownRefresh">

    <view class="search-block"
          style="margin-top:14px"
          wx:if="{{type !== 'POSTER'}}"
          bind:tap="doSearch">
      <image src="../../../imgs/empty/<EMAIL>" class="search-icon" mode="aspectFill"/>
      <view class="search-content">
        <text class="search-content-tips">{{placeholder || '请输入关键词'}}</text>
      </view>
    </view>
    <image class="banner-img"
           wx:if="{{banner}}"
           src="{{banner}}"
           mode="widthFix"/>
    <view wx:if="{{!currList || !currList.length}}"
          class="empty-class">
      <emptyBlock wx:if="{{!refreshing}}" tips="暂无数据" showBGColor="false"/>
      <vertical-space/>
    </view>
    <view>
      <view
          class="content-list"
          wx:if="{{currList.length>0 && type === 'ARTICLE'}}">
        <view
            wx:for="{{currList}}"
            wx:for-item="item"
            wx:for-index="id"
            wx:key="id">
          <news
              data="{{item}}"
              bind:onItemClick="onHandleAction"
              isLast="{{id === currList.length-1}}"
          />
        </view>
      </view>
      <view
          class="content-media-list"
          wx:if="{{currList.length>0 && type === 'LIVE'}}"
          wx:for="{{currList}}"
          wx:for-item="item"
          wx:for-index="id"
          wx:key="id">
        <live
            item="{{item}}"
            data-type="{{'AppAdvLive'}}"
            data-item="{{item}}"
            bind:tap="onHandleAction"
            isLast="{{id === currList.length-1}}"
        />
      </view>
      <view
          class="content-list"
          wx:if="{{currList.length>0 && type === 'MULTI_COURSE'}}">
        <view
            wx:for="{{currList}}"
            wx:for-item="item"
            wx:for-index="id"
            wx:key="id">
          <multimedia
              data="{{item}}"
              bind:onItemClick="onHandleAction"
              isLast="{{id === currList.length-1}}"
          />
        </view>
      </view>
      <view
          class="content-poster-list"
          style="margin-top:14px"
          wx:if="{{currList.length>0 && type === 'POSTER'}}">
        <view
            wx:for="{{currList}}"
            wx:for-item="item"
            wx:for-index="id"
            wx:key="id">
          <poster
              item="{{item}}"
              data-type="{{'AppAdvPosterInfo'}}"
              data-item="{{item}}"
              bind:tap="onHandleAction"
              isLast="{{id === currList.length-1}}"
          />
        </view>
      </view>
      <view
          class="content-list"
          wx:if="{{currList.length>0 && type === 'MARKET_PLAN'}}">
        <view
            wx:for="{{currList}}"
            wx:for-item="item"
            wx:for-index="id"
            wx:key="id">
          <marketPlan
              data="{{item}}"
              bind:onItemClick="onHandleAction"
              isLast="{{id === currList.length-1}}"
          />
        </view>
      </view>
    </view>
    <view style="width:100%;height:28px"></view>
  </x-scroll-view>
  <van-toast id="van-toast"/>
  <polymers></polymers>
</nb-page>

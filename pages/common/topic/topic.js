import {
  enums,
  interaction,
  systemtInfo,
  util,
  qs,
  vLog,
  userAction
} from '../../../common/index.js';

import {
  getCurrRoleType,
  getSystemInfo,
  getUserLogin,
  setAppHoldStatus,
  setLoadingMoment
} from "../../../common/utils/userStorage";

import {
  getTemplateById
} from "../../../common/nb/home";

const {
  screenHeight,
  titleHeight
} = systemtInfo

const {
  rpx2px,
  panDClone,
  doUrlDeCode,
  isEmptyObject,
  formatUrlObject,
  doJSON_PARSE
} = util;

const {
  GLOBAL_START_PATH,
  EnterSource,
  SHARE_IMG_DEFAULT,
  DISPLAY_403_PAGE
} = enums

const pageName = 'TOPIC'

Page({
  /**
   * 页面的初始数据
   */
  data: {
    topicInfo: {},
    cardInfos: [],
    pageName: '',
    followRoleType: '',

    loading: true,
    refreshing: false,
    nomore: false,
    contentHeight: rpx2px(screenHeight - titleHeight),
    rStatus: false,

    show403: false,
    display403: DISPLAY_403_PAGE,
    scrollTop: 0,
    showEmptyPage: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    vLog.log('TOPIC options >>>>', options)
    let topicInfo = {}
    for (const item of Object.entries(panDClone(qs.parse(options)))) {
      const [key, value] = item
      if (typeof value !== 'object'){
        topicInfo[key] = doUrlDeCode(value + '')
      } else {
        let temp = {}
        for (let [tKey, tValue] of Object.entries(panDClone(value))) {
          temp[tKey] = doUrlDeCode(tValue + '')
        }
        topicInfo[key] = {...temp}
      }
    }

    topicInfo = formatUrlObject(topicInfo) || {}
    vLog.log('TOPIC topicInfo >>>>', topicInfo)
    const {routerStatus = 'PAGE', customerType: followRoleType = ''} = topicInfo || {}

    this.setData({
      topicInfo,
      followRoleType,
      rStatus: routerStatus === 'PAGE',
    }, () => this.initTopicCards())
  },

  async initTopicCards() {
    const {rStatus, followRoleType} = this.data
    const hasLogin = getUserLogin()
    const customerType = getCurrRoleType()
    vLog.log('TOPIC followRoleType,customerType >>>', followRoleType, customerType)
    // 3. 游客（渠道站点）机构/渠道登录 跳转登录-> 判断注册身份是否与专题一致 -> 切站点访问 -> 专题页展示内容

    vLog.log('TOPIC customerType !== followRoleType >>>>', customerType !== followRoleType)
    // 游客外部访问
    if (!hasLogin && !rStatus){
      return this.onGetTopicInfo()
    }

    // 端内站点不同403
    if (customerType !== followRoleType){
      vLog.log('TOPIC hasLogin,rStatus>>>>', hasLogin, rStatus)

      this.setData({
        loading: false,
        refreshing: false,
        show403: true,
        cardInfos: []
      })
      return
    }

    return this.onGetTopicInfo()
  },

  async onGetTopicInfo(doFresh = false) {
    vLog.log('TOPIC onGetTopicInfo doFresh >>>', doFresh)
    const {topicInfo} = this.data
    const {code, msg, data, success} = await getTemplateById({admin: false, pageId: topicInfo?.pageId})
    vLog.log('TOPIC code, msg, data, success >>>>', code, msg, data, success)
    if (!success){
      this.setData({loading: false, refreshing: false, cardInfos: []})
      interaction.showToast(msg || "")
      return Promise.reject(msg || "")
    }
    let {cardInfos, status = ''} = data || {}
    // 403
    if ((!isEmptyObject(data) && data?.cardInfos == null) || status === 'DISABLE'){
      this.setData({loading: false, refreshing: false, show403: true, cardInfos: []})
      return Promise.reject(msg || "")
    }

    const _cardInfos = []
    if (!cardInfos){
      cardInfos = []
    }

    cardInfos.forEach((cItem, index) => {
      const props = {
        ...cItem,
        cardIndex: index,
        cardLast: index === cardInfos.length - 1,
        conf: '{}',
        finalConf: doJSON_PARSE(cItem?.finalConf),
        fromTab: 'TOPIC'
      }
      _cardInfos.push(props)
    })

    if (_cardInfos && _cardInfos.length){
      this.setData({
        cardInfos: _cardInfos,
        loading: false,
        refreshing: false,
      })
    }

    return Promise.resolve(_cardInfos)
  },

  onShow() {
    vLog.log('TOPIC onShow this.data >>>', this.data)
    let timer = setTimeout(() => {
      clearTimeout(timer)
      const {show403} = this.data

      if (show403){
        wx.hideShareMenu({
          menus: ['shareAppMessage', 'shareTimeline']
        })
      }
    }, 1000)
  },

  async doRefreshTopicInfo() {
    let that = this
    await Promise.allSettled([
      userAction({name: 'initWxInfo', params: {}}),
      this.onGetTopicInfo(true)
    ]).then((res) => {
      console.log(`${pageName} doRefreshTopicInfo res >>`,res)
      setLoadingMoment(false)
      that.setData({
        refreshing: false,
      })
    }).catch((err) => {
      vLog.error('TOPIC error >>>', err).report()
    })
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  handlePullDownRefresh() {
    vLog.log('TOPIC handlePullDownRefresh')
    setLoadingMoment(true)
    setAppHoldStatus(false)
    this.setData({
      nomore: false,
    }, () => this.doRefreshTopicInfo())
  },

  onShareAppMessage() {
    vLog.log('TOPIC onShareAppMessage >>>>', this.data)
    const customerType = getCurrRoleType()
    const sysInfo = getSystemInfo()
    const {topicInfo} = this.data
    const {
      pageId,
      title,
      shouldLogin
    } = topicInfo || {}

    const sParams = {
      pageId,
      title,
      shouldLogin,
      routerPage: `/pages/common/topic/topic`,
      shareFrom: EnterSource.TOPIC,
      fromTab: 'TOPIC',
      routerStatus: 'SHARE',
      customerType: getCurrRoleType(),
    }

    getApp().sensors.track('MPShare', {
      share_type: "微信好友",
    })
    
    let _path = `${GLOBAL_START_PATH}?${qs.stringify(sParams)}`
    let imageUrl = SHARE_IMG_DEFAULT
    vLog.log('TOPIC _path >>>>', _path, sParams)

    switch (customerType) {
      case 'AGENCY':
        imageUrl = sysInfo?.agency?.shareUserImg || SHARE_IMG_DEFAULT
        break

      case 'CUSTOMER':
        imageUrl = sysInfo?.common?.shareUserImg || SHARE_IMG_DEFAULT
        break

      default:
        break
    }

    return {
      title,
      path: _path,
      imageUrl
    }
  },
})

<!--
此文件为开发者工具生成，生成时间: 2023/2/10下午7:51:42
使用方法：
在 /Users/<USER>/hankins/BankerLab/hkDir/wbs-wechat-investor/pages/common/topic/topic.wxml 引入模板

```
<import src="topic.skeleton.wxml"/>
<template is="skeleton" wx:if="{{loading}}" />
```

在 /Users/<USER>/hankins/BankerLab/hkDir/wbs-wechat-investor/pages/common/topic/topic.wxss 中引入样式
```
@import "./topic.skeleton.wxss";
```

更多详细信息可以参考文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/skeleton.html
-->
<template name="skeleton">
  <view class="sk-container">
    <view is="components/nb-page/nb-page">
      <view class="container page--container normal-page page--normal-page" style="background-color:#f7f7f7;">
        <view is="components/navigation/index">
          <cover-view class="navigation-index--_nav" style="padding-top:47px; background-color:#fff;height:91px">
            <cover-view class="navigation-index--normal-nav-back" style="height: 32px!important; width: 87px!important;">
              <cover-image class="navigation-index--nav-icon sk-image"></cover-image>
            </cover-view>
            <cover-view class="navigation-index--loading">
              <cover-view class="navigation-index--nav-title sk-transparent sk-text-8-3333-168 sk-text" style="color: black; background-position-x: 50%;">渠道勿动</cover-view>
            </cover-view>
          </cover-view>
        </view>
        <scroll-view class="able-scroll page--able-scroll" enhanced="true" scroll-y="true" style="height:747.0px;margin-top:91px; margin-bottom:0px;">
          <view is="components/x-scroll-view/x-scroll-view" class="list-container" style="scroll-snap-align: none;">
            <view class="scroll-view--refresh-block scroll-view--x-common" style="height:0px;">
              <view>
                <image class="scroll-view--refresh sk-image"></image>
              </view>
              <view>
                <text class="sk-transparent sk-text-18-7500-407 sk-text">下拉刷新</text>
              </view>
              <view></view>
            </view>
            <scroll-view class="scroll-view--scroll_container" scroll-top="0" scroll-y="true" style="position:fixed;width:100%;left:0;height:791px;top:91px;bottom:0px;margin-top: 0px;">
              <view style="min-height:100%;position: absolute;width:100%;">
                <view>
                  <view is="components/polymers/polymers">
                    <view class="polymers--polymers-block">
                      <view is="components/polymers/cards/carousel/carousel">
                        <view class="carousel--carousel-card carousel--banner_line" style="margin-left: 0px;margin-right: 0px">
                          <view is="components/polymers/components/titleBar/titleBar">
                            <view class="titleBar--float-title-block" style="margin-bottom: 14px;margin-top: 10px">
                              <view class="titleBar--title-left-block" style="margin-left:14px;margin-right:14px;">
                                <text class="titleBar--title-txt sk-transparent sk-text-18-7500-227 sk-text" style="margin-left:0px;">运营广告</text>
                              </view>
                            </view>
                          </view>
                          <view is="components/polymers/cards/banner/banner">
                            <view style="margin-top: 0px;">
                              <swiper autoplay="false" circular="true" class="banner--banner-block" current="0" duration="500" indicator-active-color="rgba(255,255,255,1)" indicator-color="#ccc" interval="15000" next-margin="0" previous-margin="0" style="margin: 0;padding: 0;background-color: '#fff';">
                                <swiper-item class="banner--swiper-item" style="position: absolute; width: 100%; height: 100%; transform: translate(0%, 0px) translateZ(0px);">
                                  <image class="banner--h100 banner--image-swiper sk-image" data-item="[object Object]" mode="aspectFill fade_in" style="border-radius:15px;height: 156px;"></image>
                                </swiper-item>
                              </swiper>
                              <view class="banner--dots">
                                <view class="banner--dot banner--active" data-i="0"></view>
                                <view class="banner--dot" data-i="1"></view>
                                <view class="banner--dot" data-i="2"></view>
                              </view>
                            </view>
                          </view>
                        </view>
                      </view>
                    </view>
                    <view class="polymers--polymers-block">
                      <view is="components/polymers/cards/grid/grid">
                        <view is="components/nb-card/nb-card">
                          <view class="nb-card card--nb-card case card--case " style="[object Object]">
                            <view class="nb-item card--nb-item ">
                              <view is="components/polymers/components/titleBar/titleBar">
                                <view class="titleBar--title-block" style="margin-left:0px;margin-right:0px;margin-bottom: -12px;margin-top: 10px;">
                                  <view class="titleBar--title-left-block">
                                    <text class="titleBar--title-txt sk-transparent sk-text-18-7500-596 sk-text" style="margin-left:0px;">导航</text>
                                  </view>
                                </view>
                              </view>
                              <view is="components/polymers/cards/grid/template/grid3/grid3">
                                <view class="grid3--grid-template1" style="margin-top: 0px;">
                                  <view class="grid3--grid-temp1-item" data-item="[object Object]">
                                    <image class="grid3--grid-item-icon sk-image" mode="aspectFill"></image>
                                    <view class="grid3--grid-item3-block">
                                      <text class="grid3--grid-item-txt sk-transparent sk-opacity">1</text>
                                    </view>
                                  </view>
                                  <view class="grid3--grid-temp1-item" data-item="[object Object]">
                                    <image class="grid3--grid-item-icon sk-image" mode="aspectFill"></image>
                                    <view class="grid3--grid-item3-block">
                                      <text class="grid3--grid-item-txt sk-transparent sk-opacity">2</text>
                                    </view>
                                  </view>
                                  <view class="grid3--grid-temp1-item" data-item="[object Object]">
                                    <image class="grid3--grid-item-icon sk-image" mode="aspectFill"></image>
                                    <view class="grid3--grid-item3-block">
                                      <text class="grid3--grid-item-txt sk-transparent sk-opacity">3</text>
                                    </view>
                                  </view>
                                  <view class="grid3--grid-temp1-item" data-item="[object Object]">
                                    <image class="grid3--grid-item-icon sk-image" mode="aspectFill"></image>
                                    <view class="grid3--grid-item3-block">
                                      <text class="grid3--grid-item-txt sk-transparent sk-opacity">4</text>
                                    </view>
                                  </view>
                                </view>
                              </view>
                            </view>
                          </view>
                        </view>
                      </view>
                    </view>
                    <view class="polymers--polymers-block">
                      <view is="components/polymers/cards/content/content">
                        <view is="components/nb-card/nb-card">
                          <view class="nb-card card--nb-card case card--case " style="[object Object]">
                            <view class="nb-item card--nb-item ">
                              <view is="components/polymers/components/titleBar/titleBar">
                                <view class="titleBar--title-block" style="margin-left:0px;margin-right:0px;margin-bottom: -12px;margin-top: 10px;">
                                  <view class="titleBar--title-left-block">
                                    <text class="titleBar--title-txt sk-transparent sk-text-18-7500-940 sk-text" style="margin-left:0px;">内容推荐聚合</text>
                                  </view>
                                </view>
                              </view>
                              <view class="content--content-nav" style="margin-top: 0px">
                                <view is="components/scrollable-tabview/scrollable-tabview" class="content--current-item">
                                  <view class="tabview--container tabview--navBar" style="background-color:#fff;">
                                    <scroll-view enhanced="true" scroll-with-animation="true" scroll-x="true" scroll-into-view="tabbar0" scroll-left="0">
                                      <view class="tabview--tab sk-transparent" data-index="0" data-value="0" id="4c81c3db--tabbar0" style="font-weight: 500; color: rgb(51, 51, 51); background-color: rgb(255, 255, 255); padding-top: 10px; font-size: 16px; scroll-snap-align: none;">
                                        刘格菘
                                        <view class="tabview--indicator" style="background-color:#f04b28ff;margin-top:5px"></view>
                                      </view>
                                      <view class="tabview--tab sk-transparent" data-index="1" data-value="1" id="4c81c3db--tabbar1" style="font-weight: normal; color: rgb(153, 153, 153); background-color: rgb(255, 255, 255); padding-top: 10px; font-size: 16px; scroll-snap-align: none;">
                                        热点首发
                                        <view class="tabview--indicator" style="background-color:;margin-top:5px"></view>
                                      </view>
                                      <view class="tabview--tab sk-transparent" data-index="2" data-value="2" id="4c81c3db--tabbar2" style="font-weight: normal; color: rgb(153, 153, 153); background-color: rgb(255, 255, 255); padding-top: 10px; font-size: 16px; scroll-snap-align: none;">
                                        证券
                                        <view class="tabview--indicator" style="background-color:;margin-top:5px"></view>
                                      </view>
                                    </scroll-view>
                                    <view class="tabview--border-line"></view>
                                  </view>
                                </view>
                              </view>
                              <view class="content--content-list">
                                <view is="components/polymers/items/news/news">
                                  <view class="news--card" style="border-top-left-radius:0px; border-top-right-radius:0px; border-bottom-right-radius:0px; border-bottom-left-radius:0px;">
                                    <view class="news--card-box" data-item="[object Object]" style="margin-top:0px;padding-top:0px;border-bottom-color: rgba(238, 238, 238, 0.3)">
                                      <view class="news--card-left">
                                        <view class="news--card-left-title news--text-two-line sk-transparent sk-text-18-7500-809 sk-text">全面注册制“领衔” 资本市场将加快推进基础制度改革</view>
                                        <view class="news--card-news-bottom">
                                          <view class="news--card-subtitle-txt news--text-one-line sk-transparent sk-text-18-7500-508 sk-text">
                                            注册制改革“牵一发而动全身”。
                                          </view>
                                        </view>
                                      </view>
                                      <image class="news--image news--fade_in sk-image" mode="aspectFill"></image>
                                    </view>
                                  </view>
                                </view>
                              </view>
                              <view class="content--content-list">
                                <view is="components/polymers/items/news/news">
                                  <view class="news--card" style="border-top-left-radius:0px; border-top-right-radius:0px; border-bottom-right-radius:0px; border-bottom-left-radius:0px;">
                                    <view class="news--card-box" data-item="[object Object]" style="margin-top:0px;padding-top:14px;border-bottom-color: rgba(238, 238, 238, 0.3)">
                                      <view class="news--card-left">
                                        <view class="news--card-left-title news--text-two-line sk-transparent sk-text-18-7500-88 sk-text">刚刚，央行重磅发文！金融控股公司要注意了</view>
                                      </view>
                                      <image class="news--image news--fade_in sk-image" mode="aspectFill"></image>
                                    </view>
                                  </view>
                                </view>
                              </view>
                            </view>
                          </view>
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </scroll-view>
          </view>
          <view is="miniprogram_npm/vant-weapp/toast/index" id="van-toast" style="scroll-snap-align: none;">
            <view is="miniprogram_npm/vant-weapp/transition/index"></view>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
</template>

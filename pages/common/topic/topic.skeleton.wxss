/*
此文件为开发者工具生成，生成时间: 2023/2/10下午7:51:42

在 /Users/<USER>/hankins/BankerLab/hkDir/wbs-wechat-investor/pages/common/topic/topic.wxss 中引入样式
```
@import "./topic.skeleton.wxss";
```

更多详细信息可以参考文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/skeleton.html
*/
.sk-transparent {
    color: transparent !important;
  }
.sk-text-8-3333-168 {
    background-image: linear-gradient(transparent 8.3333%, #EEEEEE 0%, #EEEEEE 91.6667%, transparent 0%) !important;
    background-size: 100% 41.5385rpx;
    position: relative !important;
  }
.sk-text {
    background-origin: content-box !important;
    background-clip: content-box !important;
    background-color: transparent !important;
    color: transparent !important;
    background-repeat: repeat-y !important;
  }
.sk-text-18-7500-407 {
    background-image: linear-gradient(transparent 18.7500%, #EEEEEE 0%, #EEEEEE 81.2500%, transparent 0%) !important;
    background-size: 100% 43.0769rpx;
    position: relative !important;
  }
.sk-text-18-7500-227 {
    background-image: linear-gradient(transparent 18.7500%, #EEEEEE 0%, #EEEEEE 81.2500%, transparent 0%) !important;
    background-size: 100% 55.3846rpx;
    position: relative !important;
  }
.sk-text-18-7500-596 {
    background-image: linear-gradient(transparent 18.7500%, #EEEEEE 0%, #EEEEEE 81.2500%, transparent 0%) !important;
    background-size: 100% 55.3846rpx;
    position: relative !important;
  }
.sk-opacity {
    opacity: 0 !important;
  }
.sk-text-18-7500-940 {
    background-image: linear-gradient(transparent 18.7500%, #EEEEEE 0%, #EEEEEE 81.2500%, transparent 0%) !important;
    background-size: 100% 55.3846rpx;
    position: relative !important;
  }
.sk-text-18-7500-809 {
    background-image: linear-gradient(transparent 18.7500%, #EEEEEE 0%, #EEEEEE 81.2500%, transparent 0%) !important;
    background-size: 100% 43.0769rpx;
    position: relative !important;
  }
.sk-text-18-7500-508 {
    background-image: linear-gradient(transparent 18.7500%, #EEEEEE 0%, #EEEEEE 81.2500%, transparent 0%) !important;
    background-size: 100% 36.9231rpx;
    position: relative !important;
  }
.sk-text-18-7500-88 {
    background-image: linear-gradient(transparent 18.7500%, #EEEEEE 0%, #EEEEEE 81.2500%, transparent 0%) !important;
    background-size: 100% 43.0769rpx;
    position: relative !important;
  }
.sk-image {
    background: #EFEFEF !important;
  }
.sk-container {
    background-color: transparent;
  }
 
    @keyframes loading-rotate {
      100% {
        transform: rotate(360deg);
      }
    }

    .sk-loading-spinner {
      position: absolute;
      top: 50%;
      margin-top: -0.5rem;
      width: 100%;
      text-align: center;
    }

    .sk-loading-spinner .circular {
      border-radius: 50%;
      width: 1rem;
      height: 1rem;
      display: inline-block;
      box-sizing: border-box;
      border-top: solid 2px #409eff;
      border-right: solid 2px #409eff;
      border-bottom: solid 2px #409eff;
      border-left: solid 2px transparent;
      animation: loading-rotate 1s linear infinite;
    }
    

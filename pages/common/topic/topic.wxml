<import src="topic.skeleton.wxml"/>

<nb-page
    contentUseView="{{false}}"
    navTitle="{{topicInfo.title}}"
    floatMarginSize="{{10}}"
    inHomePage="{{false}}"
    showTabBar="{{false}}"
    navShowBack="{{rStatus}}"
    showHomeIcon="{{!rStatus}}">

  <x-scroll-view
      enableRefresh="{{true}}"
      class="list-container"
      elementHeight="{{contentHeight}}"
      refreshing="{{refreshing}}"
      nomore="{{nomore}}"
      enableLoadMore="{{false}}"
      resetting="{{true}}"
      scrollTop='{{scrollTop}}'
      floatMarginSize="{{10}}"
      bindpulldownrefresh="handlePullDownRefresh">

    <template is="skeleton" wx:if="{{loading}}"/>

    <view wx:if="{{cardInfos && cardInfos.length>0}}">
      <polymers
          wx:if="{{cardInfos.length>0}}"
          data="{{cardInfos}}"
          fromTab="{{pageName}}"
      />
    </view>

    <view wx:if="{{show403}}" class="show403" style="padding-top: 30%;">
      <image src="{{display403}}" class="icon403" mode="aspectFill"/>
      <view class="title403">{{'暂不可见'}}</view>
      <view class="tips403">{{'该内容不可见或无访问权限'}}</view>
    </view>
  </x-scroll-view>
  <van-toast id="van-toast"/>

</nb-page>


import { qyCode2session } from "../../../common/nb/home";
import { getUnionID } from "../../../common/utils/userStorage";
// const { platform } = wx.getSystemInfoSync()
import { util } from "../../../common/index.js";
const { getQueryString } = util
Page({
  onLoad(options = {}) {
    this.options = options
    wx.showLoading({ title: '', mask: true })
    console.log('shareQY', options)
    this.checkSession(options)
  },

  checkSession(options) {
    const that = this
    wx.qy.checkSession({
      success: function (e) {
        that.share(options)
      },
      fail: function (e) {
        that.login(options)
      },
    })
  },

  login(options) {
    const that = this
    wx.qy.login({
      success: async function (res) {
        if (res.code) {
          const { code } = await qyCode2session({ code: res.code })
          if (code === 0) { that.share(options) }
        } else {
          console.log('企业微信登录失败！' + res.errMsg)
        }
      }
    });
  },

  share(options) {
    const url = decodeURIComponent(options?.url)
    let share_type = ''
    if (options.type == 1) {
      share_type = '转发好友'
      this.shareToExternalContact(options)
    } else if (options.type == 2) {
      share_type = '客户'
      this.shareToExternalChat(options)
    } else if (options.type == 3) {
      share_type = '客户群'
      this.shareToExternalMoments(options)
    } else if (options.type == 4) {
      share_type = '客户朋友圈'
      this.sendChatMessage(options)
    }
    getApp().sensors.track('share', {
      share_type,
      title: options?.title,
      to_unionid: getUnionID(),
      need_login: getQueryString(url, 'hasLogin') === 2 ? true : false,
      show_namecard: getQueryString(url, 'hasCard') === 2 ? true : false,
      invite_signup: getQueryString(url, 'hasInvite' === 2 ? true : false),
    })
  },

  end(res) {
    console.log('openEnd', res)
    wx.navigateBack()
    wx.hideLoading()
  },

  //发送给客户
  shareToExternalContact(options) {
    const that = this
    wx.qy.shareToExternalContact({
      text: {
        content: "",	// 文本内容
      },
      attachments: [
        {
          msgtype: "link",    // 消息类型，必填
          link: {
            title: decodeURIComponent(options?.title),        // H5消息标题
            imgUrl: decodeURIComponent(options?.imgUrl),  // H5消息封面图片URL
            desc: decodeURIComponent(options?.desc),    // H5消息摘要
            url: decodeURIComponent(options?.url),         // H5消息页面url 必填
          },
        }
      ],
      success: function (res) {
        that.end(res)
      },
      fail: function (res) {
        that.end(res)
      },
    });
  },
  //发送到客户群
  shareToExternalChat(options) {
    const that = this
    wx.qy.shareToExternalChat({
      text: {
        content: "",	// 文本内容
      },
      attachments: [
        {
          msgtype: "link",    // 消息类型，必填
          link: {
            title: decodeURIComponent(options?.title),        // H5消息标题
            imgUrl: decodeURIComponent(options?.imgUrl),  // H5消息封面图片URL
            desc: decodeURIComponent(options?.desc),    // H5消息摘要
            url: decodeURIComponent(options?.url),        // H5消息页面url 必填
          },
        },
      ],
      success: function (res) {
        that.end(res)
      },
      fail: function (res) {
        that.end(res)
      },
    });
  },
  //发送到朋友圈
  shareToExternalMoments(options) {
    const that = this
    wx.qy.shareToExternalMoments({
      text: {
        content: "",    // 文本内容
      },
      attachments: [
        {
          msgtype: "link",    // 消息类型，必填
          link: {
            title: decodeURIComponent(options?.title),        // H5消息标题
            imgUrl: decodeURIComponent(options?.imgUrl),  // H5消息封面图片URL
            desc: decodeURIComponent(options?.desc),    // H5消息摘要
            url: decodeURIComponent(options?.url),      // H5消息页面url 必填
          },
        },
      ],
      success: function (res) {
        that.end(res)
      },
      fail: function (res) {
        that.end(res)
      },
    });
  },
  //发送给好友
  sendChatMessage(options) {
    const that = this
    wx.qy.sendChatMessage({
      msgtype: "news", //消息类型，必填
      enterChat: true, //为true时表示发送完成之后顺便进入会话，仅移动端3.1.10及以上版本支持该字段
      news:
      {
        title: decodeURIComponent(options?.title),        // H5消息标题
        imgUrl: decodeURIComponent(options?.imgUrl),  // H5消息封面图片URL
        desc: decodeURIComponent(options?.desc),    // H5消息摘要
        link: decodeURIComponent(options?.url),        // H5消息页面url 必填
      },
      success: function (res) {
        that.end(res)
      },
      fail: function (res) {
        that.end(res)
      },
    });
  }
})

import {
  global,
  interaction,
  qs,
  storage,
  util,
  vLog
} from '../../../common/index'

import {
  setAppHoldStatus,
  setSpringboardStatus
} from "../../../common/utils/userStorage";

const {
  doUrlDeCode
} = util

/**
 * 中继跳板
 */
Page({
  data: {
    options: {}
  },
  onLoad(options) {
    vLog.log('SPRING_BOARD options >>>>', qs.parse(options))
    let opt = {...qs.parse(options)}
    for (const item of Object.entries(opt)) {
      let [key, value] = item
      key = doUrlDeCode(key)
      value = doUrlDeCode(value)
      opt[key] = value
    }
    vLog.log('SPRING_BOARD opt >>>>', opt)
    this.setData({
      options: opt
    }, () => this.toMiniProgram())
  },

  toMiniProgram() {
    const {options} = this.data
    const {appid, path} = options || {}

    return wx.navigateToMiniProgram({
      appId: appid,
      path: `${path}`,
      envVersion: "release",
      success() {
        wx.navigateBack({})
      },
      fail(err) {
        vLog.error('SPRING_BOARD failed err >>>', err).report()
        const {errMsg = ''} = err || {}
        if (`${errMsg}`.includes('invalid')){
          interaction.showToast('请检查配置信息~')
        }
        if (`${errMsg}`.includes('cancel')){
          return wx.navigateBack({})
        }

        if (`${errMsg}`.includes('gesture')){
          setSpringboardStatus(true)
          storage.setStorage(global.STORAGE_GLOBAL_SPRINGBOARD_PARAMS, {appid, path})
        }

        let fTimer = setTimeout(() => {
          clearTimeout(fTimer)
          return wx.navigateBack({})
        }, 1000)
      },
      complete() {
        setAppHoldStatus(true)
      }
    })
  }
});

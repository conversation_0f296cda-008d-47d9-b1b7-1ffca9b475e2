import {
  util,
  systemtInfo,
  interaction,
  eventName,
  wbs,
  storage,
  qs,
  global,
  enums,
  breakIn,
  vLog,
} from "../../../common/index.js";

import compass_JSON from '../../../json/compass.json'

import {
  getUnionID,
  getOpenId,
  getToken,
  getWechatInfoId,
  getUser,
  setUser,
  getCurrRoleType,
  getSystemInfo,
  getUserLogin,
  setAppHoldStatus,
  setPreviousPath,
  getCustomerTypeInt,
  getStartUpRoutePageType,
  setStartUpRoutePageType,
} from "../../../common/utils/userStorage";

import {
  getAdvStaffCardInfo,
} from "../../../common/nb/home";

const {
  EnterSource,
  GLOBAL_START_PATH
} = enums

const {
  SEND_WEBVIEW_OPTIONS,
  SEND_REGISTER_OPTIONS,
  REFRESH_PAGE_DATA
} = eventName

const {
  screenHeight,
  titleHeight
} = systemtInfo

const {
  rpx2px,
  analyzeAction,
  isEmptyObject,
  panDClone,
  doUrlDeCode
} = util

const app = getApp()

Page({
  data: {
    contentHeight: rpx2px(screenHeight - titleHeight),
    compassConfig: {},
    bgBlock: {},
    content: [],
    bottomBar: '',
    picBgUrl: '',

    hasLogin: false,
    wInfo: {},
    omitMPage: 0,
    shareFrom: '',
    fromTab: '',
  },

  onLoad(options) {
    let opts = {}
    for (let [fKey, fValue] of Object.entries(panDClone(options))) {
      opts[fKey] = doUrlDeCode(fValue + '')
    }
    const {shareFrom, fromTab} = opts || {}
    vLog.log('COMPASS options, opts >>>', options, opts)

    if (!opts || isEmptyObject(opts)){
      this.fixDoCheckLogin()
    }

    const hasLogin = getUserLogin()
    this.setData({
      options: opts,
      hasLogin,
      shareFrom,
      fromTab
    }, () => this.getWeChatInfo())
  },

  fixDoCheckLogin() {
    let timer = setTimeout(async () => {
      clearTimeout(timer)

      const loginParams = {
        openid: getOpenId(),
        unionid: getUnionID(),
      }

      vLog.info('COMPASS fixDoCheckLogin (!param || isEmptyObject(param) || !param?.openid || !param.unionid >>>', !loginParams || isEmptyObject(loginParams) || !loginParams?.openid || !loginParams.unionid)
      if (!loginParams || isEmptyObject(loginParams) || !loginParams?.openid || !loginParams.unionid){
        return
      }

      const {executed, code} = await breakIn({
        name: 'doCheckLogin',
        params: {...loginParams, customerType: getCustomerTypeInt()}
      })
      vLog.log('COMPASS executed, code >>>>', executed, code)
      let hasLogin = false
      let shareFrom = EnterSource.COMPASS
      if (executed !== 'DONE'){
        this.setData({
          hasLogin,
          shareFrom,
        })
        const routeParams = getStartUpRoutePageType() || {}
        if (routeParams && !isEmptyObject(routeParams)){
          const {path = '', way} = routeParams
          switch (way) {
            case 'reLaunch': {
              return wx.reLaunch({
                url: `${path}`,
                complete: () => {
                  setStartUpRoutePageType({})
                }
              })
            }

            default:
              break
          }
        }
      }
      this.setData({
        hasLogin: true,
        shareFrom: EnterSource.COMPASS,
      })
    }, 1000)
  },

  async getWeChatInfo() {
    interaction.showLoading('加载中...')
    const {success, data} = await getAdvStaffCardInfo({
      timeStamp: new Date().getTime()
    })
    interaction.hideLoading()
    let _wInfo = getUser()
    if (success){
      const {orgId = ''} = data || {}
      storage.setStorage('orgId', orgId)

      _wInfo = {
        ..._wInfo,
        ...data
      }
      setUser(_wInfo)
    }
    const sysInfo = getSystemInfo()

    const compassConfig = sysInfo?.staff?.clientParams?.compassConfig || {}
    if (compassConfig && !isEmptyObject(compassConfig)){
      const {bgBlock = {}, content = [], bottomBar} = compassConfig
      this.setData({
        compassConfig,
        bgBlock,
        content,
        picBgUrl: bgBlock?.picBgUrl,
        bottomBar,
        wInfo: _wInfo
      })
    } else {
      let compassConfig = compass_JSON.compass
      const {bgBlock = {}, content = [], bottomBar} = compassConfig || {}
      this.setData({
        compassConfig,
        bgBlock,
        content,
        picBgUrl: bgBlock?.picBgUrl,
        bottomBar,
        wInfo: _wInfo
      })
    }
  },

  onAction(e = {}) {
    const customerType = getCurrRoleType()
    if (customerType !== 'CHANNEL') {
      return wx.showToast({ title: '仅理财经理可用', icon: 'none' })
    }
    vLog.log('COMPASS onAction >>>', e)
    const {fromTab = 'HOME', hasLogin} = this.data
    interaction.showLoading('加载中...')

    const {
      dataset: {
        item
      }
    } = e.currentTarget || {}

    const {action} = item || {}

    const {type} = analyzeAction(action)
    vLog.log('COMPASS item >>>', item)

    let params = {
      token: getToken(),
      openid: getOpenId(),
      unionid: getUnionID(),
      wechatInfoId: getWechatInfoId(),
      pageType: type,
    }
    params.perfix = `${wbs.gfH5}/share/${type}`
    interaction.hideLoading()

    if (!hasLogin){
      let pagePath = getCurrentPages().pop();
      vLog.log('COMPASS pagePath >>>', pagePath?.route)
      setPreviousPath(pagePath?.route)
      params = {
        ...params,
        TAG: 'AppAdvCompass',
        fromTab,
        targetPath: '/pages/mine/compass/index'
      }

      return wx.navigateTo({
        url: '/pages/user/login/index',
        success(res) {
          vLog.log('COMPASS event >>>', params)
          res.eventChannel.emit(SEND_REGISTER_OPTIONS, params)
        }
      })
    }
    return wx.navigateTo({
      url: "/pages/common/webview/webPage",
      success(res) {
        res.eventChannel.emit(SEND_WEBVIEW_OPTIONS, params)
      }
    })
  },

  onUnload() {
    const {omitMPage = ''} = this.data || {}
    setAppHoldStatus(false)
    storage.getStorage(global.STORAGE_GLOBAL_INLINE_PAGE, false)

    if (omitMPage){
      app.globalData.emitter.emit(REFRESH_PAGE_DATA, {
        isRefresh: true,
        pageType: 'AppAdvProfileInfo',
        registerRouter: true
      })

      return wx.switchTab({
        url: '/pages/mine/mine'
      })
    }
  },

  onShareAppMessage() {
    const {compassConfig} = this.data || {}
    const sParams = {
      routerPage: `/pages/mine/compass/index?shareFrom=${EnterSource.COMPASS}`,
      shareFrom: EnterSource.COMPASS,
    }
    // 分享打开的页面
    let path = `${GLOBAL_START_PATH}?${qs.stringify(sParams)}`
    const shareData = {
      path,
      imageUrl: `${compassConfig?.headerUrl}` || '',
      title: '定投指南针'
    }
    getApp().sensors.track('MPShare', {
      share_type: "微信好友",
    })
    return shareData
  }
});

// pages/mine/mine.js
import {
  systemtInfo,
  util,
  eventName,
  storage,
  global,
  enums,
  vLog,
  userAction,
  breakIn,
} from '../../common/index.js'

import {
  getTabBarConfig,
  getTabBarList,
  setAppHoldStatus,
  setLoadingMoment
} from "../../common/utils/userStorage";

const {
  screenHeight,
  footHeight,
  titleHeight
} = systemtInfo

const {
  ERROR_STATUS,
  VERSION_BLOCK
} = enums

const {
  DISMISS_TAB_BAR,
  SET_SHOW_RECORD,
  FETCH_NET_DATA_ERROR,
  SET_SCROLL_TO_TARGET,
  SET_REFRESH_PAGE,
  TAB_LISTENER_EVENT,
} = eventName

const pageName = 'MINE'

const {
  rpx2px,
} = util;

Page({
  /**
   * 页面的初始数据
   */
  data: {
    cardInfos: [],
    pageName,

    refreshing: false,
    nomore: false,
    contentHeight: rpx2px(screenHeight - footHeight - titleHeight),

    loading: true,
    showEmptyPage: false,
    scrollTop: 0,
    tBarType: 0,
    isMarkDay: false
  },

  onLoad() {
    getApp().event.on(TAB_LISTENER_EVENT, (event) => {
      this.onHandleEvent(event)
    })

    let _tConfig = getTabBarConfig()
    for (let [tKey, tValue] of Object.entries(_tConfig)) {
      if (pageName === tValue){
        this.setData({tBarType: tKey * 1})
      }
    }

    this.init()
      .then((res) => {
        console.log(res)
      })
      .catch((err) => {
        console.log(err)
      })
  },

  async init() {
    let that = this
    await Promise.allSettled([
      that.getCPInfo(),
      userAction({name: 'getUserTypeInfo', params: {}}),
      userAction({name: 'initWxInfo', params: {}}),
    ]).then((res) => {
      console.log(`${pageName} init res >>`,res)
      that.onProbe()
    }).catch((err) => {
      vLog.error(`${pageName} onLoad err >>>`, err).report()
    })
  },

  onHandleEvent(event = {}) {
    vLog.log(`${pageName} onHandleEvent event >>>`, event)
    const {key, tab} = event || {}

    switch (key) {
      case SET_SCROLL_TO_TARGET:
        if (tab === pageName){
          this.handlePullDownRefresh()
        }
        break

      case FETCH_NET_DATA_ERROR:
        this.doCancelFresh()
        break

      case SET_REFRESH_PAGE:
        this.setRefreshPage()
        break

      case DISMISS_TAB_BAR:
        this.doSetPageHeight()
        break

      case SET_SHOW_RECORD:
        this.onShowRecordModel()
        break

      default:
        break
    }
  },

  setScrollTo(tab, scrollTo = 0) {
    if (tab === pageName){
      this.setData({
        scrollTop: scrollTo
      }, () => this.setRefreshPage())
    }
  },

  /**
   * 首页优化探针
   */
  onProbe() {
    let {cardInfos} = this.data
    vLog.log(`${pageName} onProbe cardInfos >>>>`, cardInfos)
    if (!cardInfos || !cardInfos.length){
      this.setData({
        loading: false,
        refreshing: false,
        showEmptyPage: true,
      }, () => this.getCPInfo(true))
    }
  },

  setRefreshPage() {
    vLog.log('setRefreshPage')
    return this.doRefreshMineInfo()
  },

  doSetPageHeight() {
    this.setData({
      contentHeight: rpx2px(screenHeight - titleHeight)
    })
  },

  onShowRecordModel() {
    return userAction({name: 'showRecordModal', params: {tabName: pageName}})
  },

  doCancelFresh() {
    let timer = setTimeout(() => {
      clearTimeout(timer)
      setLoadingMoment(false)
      this.setData({
        refreshing: false,
      })
    }, 1200)
  },

  onShow() {
    wx.hideShareMenu({
      menus: ['shareAppMessage', 'shareTimeline']
    })
  },

  onReady() {
    let delayRefreshTimer = setTimeout(() => {
      clearTimeout(delayRefreshTimer)
      setLoadingMoment(true)
      this.setRefreshPage()
    }, 2000)
  },

  async getCPInfo(doFresh = false) {
    let tList = (getApp().globalData.tabList)?.length ? getApp().globalData.tabList : getTabBarList() || []
    vLog.log(` ${pageName} tList origin >>>>`, tList)
    let {cardInfos} = this.data || {}
    if ((cardInfos && cardInfos.length) && !doFresh){
      return Promise.resolve(cardInfos)
    }

    if (doFresh){
      const _cardInfos = await breakIn({name: 'getTabCardInfo', params: {pageName}}) || []

      if (_cardInfos && _cardInfos.length > 1){
        _cardInfos.concat(VERSION_BLOCK)

        this.setData({
          cardInfos: _cardInfos,
          loading: false,
          refreshing: false,
        })
      }
      return Promise.resolve(_cardInfos)
    }
    if (tList && tList.length){
      tList = tList.filter((tItem) => {
        const {
          pageTabBarConf: {
            tabName = ''
          }
        } = tItem || {}

        return pageName === tabName
      })

      let {cardInfos} = tList[0] || {}
      if (cardInfos && cardInfos.length > 1){
        cardInfos = cardInfos.concat(VERSION_BLOCK)
      }
      console.log(`${pageName} getCPInfo cardInfos >>`,cardInfos)
      if (ERROR_STATUS.includes(cardInfos)){
        cardInfos = []
      }
      if (cardInfos && cardInfos.length){
        cardInfos = cardInfos.map((cardItem, cardIndex) => {
          return {
            ...cardItem,
            cardIndex,
            cardLast: cardIndex === cardInfos.length - 1,
            fromTab: pageName
          }
        })
        this.setData({
          cardInfos,
          loading: false,
          refreshing: false,
        })
      }

      return Promise.resolve(cardInfos)
    }
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {
    setAppHoldStatus(false)
    storage.setStorage(global.STORAGE_GLOBAL_RESET_SHARE_PATH_DATA, {})
  },

  async doRefreshMineInfo() {
    let that = this
    await Promise.allSettled([
      userAction({name: 'initWxInfo', params: {}}),
      this.getCPInfo(true)
    ]).then((res) => {
      console.log(`${pageName} doRefreshMineInfo res >>`,res)
      setLoadingMoment(false)
      that.setData({
        refreshing: false,
      })
    }).catch((err) => {
      vLog.error(`${pageName} doRefreshMineInfo  err>>>`, err).report()
    })
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  handlePullDownRefresh: function() {
    setLoadingMoment(true)
    setAppHoldStatus(false)
    this.setData({
      nomore: false,
    }, () => {
      return this.doRefreshMineInfo()
    })
  }
})

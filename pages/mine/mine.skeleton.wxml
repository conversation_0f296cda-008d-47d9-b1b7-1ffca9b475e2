<!--
此文件为开发者工具生成，生成时间: 2022/4/18 下午2:15:22
使用方法：
在 /Users/<USER>/Desktop/work/wbs-wechat-investor/pages/mine/mine.wxml 引入模板

```
<import src="mine.skeleton.wxml"/>
<template is="skeleton" wx:if="{{loading}}" />
```

在 /Users/<USER>/Desktop/work/wbs-wechat-investor/pages/mine/mine.wxss 中引入样式
```
@import "./mine.skeleton.wxss";
```

更多详细信息可以参考文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/skeleton.html
-->
<template name="skeleton">
  <view class="sk-container">
    <view is="components/nb-page/nb-page">
      <view class="container page--container" style="background-color:#f7f7f7;">
        <scroll-view scroll-y="true" style="height:642px;margin-top:88px; margin-bottom:82px;">
          <view is="components/x-scroll-view/x-scroll-view" class="list-container">
            <view class="scroll-view--refresh-block scroll-view--x-common" style="height:0px;">
              <view>
                <image class="scroll-view--refresh sk-image"></image>
              </view>
              <view>
                <text class="sk-transparent sk-text-18-7500-539 sk-text">下拉刷新</text>
              </view>
              <view></view>
            </view>
            <scroll-view class="scroll-view--scroll_container" scroll-top="0" scroll-y="true" style="position:fixed;width:100%;left:0;height:642px;top:88px;bottom:0px;">
              <view style="min-height:100%;position: absolute;width:100%;">
                <view>
                  <view is="components/polymers/polymers">
                    <view class="polymers--polymers-block">
                      <view is="components/polymers/cards/profile/profile">
                        <view is="components/nb-card/nb-card">
                          <view class="nb-card card--nb-card case card--case ">
                            <view class="nb-item card--nb-item ">
                              <view is="components/polymers/components/titleBar/titleBar"></view>
                              <view is="components/polymers/cards/profile/template/card1/card1">
                                <view class="card1--profile-card1-block" data-item="[object Object]" style="margin-top: 20px;">
                                  <image class="card1--profile-avatar sk-image" mode="aspectFill"></image>
                                  <view class="card1--profile-card1-content">
                                    <text class="card1--profile-name sk-transparent sk-text-18-7500-843 sk-text">郭小磊</text>
                                    <text class="card1--profile-channel-name sk-transparent sk-text-18-7500-994 sk-text">机构客户</text>
                                  </view>
                                </view>
                              </view>
                            </view>
                          </view>
                        </view>
                      </view>
                    </view>
                    <view class="polymers--polymers-block">
                      <view is="components/polymers/cards/grid/grid">
                        <view is="components/nb-card/nb-card">
                          <view class="nb-card card--nb-card case card--case ">
                            <view class="nb-item card--nb-item ">
                              <view is="components/polymers/components/titleBar/titleBar">
                                <view class="titleBar--float-title-block" style="margin-top: 10px">
                                  <view class="titleBar--title-left-block" style="margin-left:0px;margin-right:0px;">
                                    <text class="titleBar--title-txt sk-transparent sk-text-18-7500-873 sk-text" style="margin-left:0px;">线索追踪</text>
                                    <text class="titleBar--subTitle-txt sk-transparent sk-text-18-7500-849 sk-text">木棉花营销工具</text>
                                  </view>
                                </view>
                              </view>
                              <view is="components/polymers/cards/grid/template/grid3/grid3">
                                <view class="grid3--grid-template1" style="margin-top: 0px;">
                                  <view class="grid3--grid-temp1-item" data-item="[object Object]">
                                    <image class="grid3--grid-item-icon sk-image" mode="aspectFill"></image>
                                    <view class="grid3--grid-item3-block">
                                      <text class="grid3--grid-item-txt sk-transparent sk-text-18-7500-867 sk-text">智能名片</text>
                                    </view>
                                  </view>
                                  <view class="grid3--grid-temp1-item" data-item="[object Object]">
                                    <image class="grid3--grid-item-icon sk-image" mode="aspectFill"></image>
                                    <view class="grid3--grid-item3-block">
                                      <text class="grid3--grid-item-txt sk-transparent sk-text-18-7500-80 sk-text">营销线索</text>
                                    </view>
                                  </view>
                                  <view class="grid3--grid-temp1-item" data-item="[object Object]">
                                    <image class="grid3--grid-item-icon sk-image" mode="aspectFill"></image>
                                    <view class="grid3--grid-item3-block">
                                      <text class="grid3--grid-item-txt sk-transparent sk-text-18-7500-325 sk-text">邀请注册</text>
                                    </view>
                                  </view>
                                </view>
                              </view>
                            </view>
                          </view>
                        </view>
                      </view>
                    </view>
                    <view class="polymers--polymers-block">
                      <view is="components/polymers/cards/grid/grid">
                        <view is="components/nb-card/nb-card">
                          <view class="nb-card card--nb-card case card--case ">
                            <view class="nb-item card--nb-item ">
                              <view is="components/polymers/components/titleBar/titleBar">
                                <view class="titleBar--float-title-block" style="margin-top: 10px">
                                  <view class="titleBar--title-left-block" style="margin-left:0px;margin-right:0px;">
                                    <text class="titleBar--title-txt sk-transparent sk-text-18-7500-733 sk-text" style="margin-left:0px;">工具箱</text>
                                    <text class="titleBar--subTitle-txt sk-transparent sk-text-18-7500-856 sk-text">木棉花产品工具</text>
                                  </view>
                                </view>
                              </view>
                              <view is="components/polymers/cards/grid/template/grid4/grid4">
                                <view class="grid4--grid-template2" style="margin-top: 0px;">
                                  <view class="grid4--grid-temp2-item" data-item="[object Object]">
                                    <image class="grid4--grid-item-icon sk-image" mode="aspectFill"></image>
                                    <view class="grid4--grid-item4-block">
                                      <text class="grid4--grid-item-txt sk-transparent sk-text-18-7500-551 sk-text">木棉智投</text>
                                    </view>
                                  </view>
                                  <view class="grid4--grid-temp2-item" data-item="[object Object]">
                                    <image class="grid4--grid-item-icon sk-image" mode="aspectFill"></image>
                                    <view class="grid4--grid-item4-block">
                                      <text class="grid4--grid-item-txt sk-transparent sk-text-18-7500-244 sk-text">养老计算</text>
                                    </view>
                                  </view>
                                  <view class="grid4--grid-temp2-item" data-item="[object Object]">
                                    <image class="grid4--grid-item-icon sk-image" mode="aspectFill"></image>
                                    <view class="grid4--grid-item4-block">
                                      <text class="grid4--grid-item-txt sk-transparent sk-text-18-7500-462 sk-text">组合健诊</text>
                                    </view>
                                  </view>
                                  <view class="grid4--grid-temp2-item" data-item="[object Object]">
                                    <image class="grid4--grid-item-icon sk-image" mode="aspectFill"></image>
                                    <view class="grid4--grid-item4-block">
                                      <text class="grid4--grid-item-txt sk-transparent sk-text-18-7500-458 sk-text">基金解读</text>
                                    </view>
                                  </view>
                                </view>
                              </view>
                            </view>
                          </view>
                        </view>
                      </view>
                    </view>
                    <view class="polymers--page-footer"></view>
                  </view>
                </view>
              </view>
            </scroll-view>
          </view>
          <view is="miniprogram_npm/vant-weapp/toast/index" id="van-toast">
            <view is="miniprogram_npm/vant-weapp/transition/index"></view>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
</template>